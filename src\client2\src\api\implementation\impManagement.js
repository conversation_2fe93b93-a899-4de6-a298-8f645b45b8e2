import request from '@/utils/request'
import { serviceArea } from '@/api/serviceArea'

let serviceAreaName = serviceArea.business + '/Implement'

let serviceChangeName = serviceArea.business + '/Change'

let serviceManageName = serviceArea.business + '/ImplementationManage'

let serviceCommentName = serviceArea.business + '/Comment'

let serviceAcceptName = serviceArea.business + '/ImplementsAcceptanceApplication'
/**新增 */
export function add(data) {
    return request({
        url: serviceAreaName + '/Add',
        method: 'post',
        data
    })
}
export function addPersonalDataCenter(data) {
    return request({
        url: serviceAreaName + '/AddPersonalDataCenter',
        method: 'post',
        data
    })
}
export function delPersonalDataCenter(data) {
    return request({
        url: serviceAreaName + '/DeletePersonalDataCenter',
        method: 'post',
        data
    })
}

export function fieldShows(data) {
    return request({
        url: serviceAreaName + '/FieldShows',
        method: 'post',
        data
    })
}
export function getPersonalDataCenter(data) {
    return request({
        url: serviceAreaName + '/GetPersonalDataCenter',
        method: 'post',
        data
    })
}

// 删除
export function del(data) {
    return request({
        url: serviceAreaName + '/Delete',
        method: 'post',
        data
    })
}

//创建审批
export function approval(data) {
    return request({
        url: serviceAreaName + '/ApprovalAsync',
        method: 'post',
        data
    })
}

// 获取查询列表
export function getList(data) {
    return request({
        url: serviceAreaName + '/GetListPage',
        method: 'post',
        data
    })
}
//获得工程负责人
export function getImplementPrincipal(params) {
    return request({
        url: serviceAreaName + '/GetImplementPrincipalAsync',
        method: 'get',
        params
    })
}


//获取详情
export function detail(params) {
    return request({
        url: serviceAreaName + '/GetDetails',
        method: 'get',
        params
    })
}

//添加实施变更
export function edit(data) {
    return request({
        url: serviceChangeName + '/AddImplementChangeAsync',
        method: 'post',
        data
    })
}

//全国实施数据
export function getNationwideDataChart(data) {
    return request({
        url: serviceAreaName + '/NationwideDataChartAsync',
        method: 'post',
        data
    })
}

//设备数量分析
export function getEquipmentQuantity(data) {
    return request({
        url: serviceAreaName + '/EquipmentQuantityAnalysisChartAsync',
        method: 'post',
        data
    })
}

//设备类型分析
export function getEquipmentType(data) {
    return request({
        url: serviceAreaName + '/EquipmentTypeAnalysisChartAsync',
        method: 'post',
        data
    })
}

//列表统计图表
export function getListStatisticsChart(data) {
    return request({
        url: serviceAreaName + '/ListStatisticsChartAsync',
        method: 'post',
        data
    })
}

export function getImplementationStatement(data) {
    return request({
        url: serviceAreaName + '/GetImplementationStatement',
        method: 'post',
        data
    })
}

export function getImplementationStatementNew(data) {
    return request({
        url: serviceAreaName + '/GetImplementationStatementNew',
        method: 'post',
        data
    })
}


//获取评论列表
export function getAllComment(data) {
    return request({
        url: serviceCommentName + '/GetAllComment',
        method: 'post',
        data
    })
}



//获取评论列表
export function getImplementationProcedureListByCondition(data) {
    return request({
        url: serviceManageName + '/GetImplementationProcedureListByCondition',
        method: 'post',
        data
    })
}


//交付新增
export function addAccept(data) {
    return request({
        url: serviceAcceptName + '/Add',
        method: 'post',
        data
    })
}

//交付记录
export function getAcceptList(data) {
    return request({
        url: serviceAcceptName + '/GetListPage',
        method: 'post',
        data
    })
}


//交付审批    
export function acceptApproval(data) {
    return request({
        url: serviceAcceptName + '/ApprovalAsync',
        method: 'post',
        data
    })
}

//获取验收交付详情
export function getAcceptDetail(params) {
    return request({
        url: serviceAcceptName + '/GetDetails',
        method: 'get',
        params
    })
}
