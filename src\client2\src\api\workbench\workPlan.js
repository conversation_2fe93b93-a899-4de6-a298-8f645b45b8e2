import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/WorkPlan/'


export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//写日报获取的任务列表
export function getTaskList(data) {
    return request({
        url: serviceAreaName + 'GetTaskList',
        method: 'post',
        data
    })
}

//写日报获取的任务列表
export function getNewTaskList(data) {
    return request({
        url: serviceAreaName + 'GetNewTaskList',
        method: 'post',
        data
    })
}


export function getRelevantMeListPage(data) {
    return request({
        url: serviceAreaName + 'RelevantMeListPage',
        method: 'post',
        data
    })
}


export function getPersonnel(params) {
    return request({
        url: serviceAreaName + 'GetPersonnel',
        method: 'get',
        params
    })
}


export function markAllRead(data) {
    return request({
        url: serviceAreaName + 'MarkAllRead',
        method: 'post',
        data
    })
}

export function relevantMeListPageCount(data) {
    return request({
        url: serviceAreaName + 'RelevantMeListPageCount',
        method: 'post',
        data
    })
}

export function workPlanListPageCount(params) {
    return request({
        url: serviceAreaName + 'WorkPlanListPageCount',
        method: 'get',
        params
    })
}

//获取通用任务（非项目任务）详细
export function getWorkTaskById(params) {
    return request({
        url: serviceAreaName + 'GetWorkTaskById',
        method: 'get',
        params
    })
}

export function read(params) {
    return request({
        url: serviceAreaName + 'Read',
        method: 'get',
        params
    })
}

export function chartByEmployee(data) {
    return request({
        url: serviceAreaName + 'ChartByEmployee',
        method: 'post',
        data
    })
}

export function calculateProgress(params) {
    return request({
        url: serviceAreaName + 'CalculateProgress',
        method: 'get',
        params
    })
}


export function delReport(data) {
    return request({
        url: serviceAreaName + 'RelevantMeDelete',
        method: 'post',
        data
    })
}

export function getListPageBoard(data) {
    return request({
        url: serviceAreaName + 'ListPageBoard',
        method: 'post',
        data
    })
}

export function GetEmployeeWorkPlan(params) {
    return request({
        url: serviceAreaName + 'GetEmployeeWorkPlan',
        method: 'get',
        params
    })
}

export function setBoardParticipantEmployee(data) {
    return request({
        url: serviceAreaName + 'SetBoardParticipantEmployee',
        method: 'post',
        data
    })
}

export function getListPageBoardNew(data) {
    return request({
        url: serviceAreaName + 'ListPageBoardNew',
        method: 'post',
        data
    })
}

export function getWorkPlanByParticipantId(data) {
    return request({
        url: serviceAreaName + 'GetWorkPlanByParticipantId',
        method: 'post',
        data
    })
}

export function getChildPrincipalEmployee(params) {
    return request({
        url: serviceAreaName + 'GetChildPrincipalEmployee',
        method: 'get',
        params
    })
}

export function getRelationWorkTask(params) {
    return request({
        url: serviceAreaName + 'GetRelationWorkTask',
        method: 'get',
        params
    })
}

export function getBoardDetails(data) {
    return request({
        url: serviceAreaName + 'GetBoardDetails',
        method: 'post',
        data
    })
}

export function getBodyDetails(params) {
    return request({
        url: serviceAreaName + 'GetBodyDetails',
        method: 'get',
        params
    })
}

export function getNewModelDetails(params) {
    return request({
        url: serviceAreaName + 'GetNewModelDetails',
        method: 'get',
        params
    })
}

export function getBoardParticipantEmployee(params) {
    return request({
        url: serviceAreaName + 'GetBoardParticipantEmployee',
        method: 'get',
        params
    })
}

export function getListNewData(data) {
    return request({
        url: serviceAreaName + 'GetListNewData',
        method: 'post',
        data
    })
}


export function memberEdit(data) {
    return request({
        url: serviceAreaName + 'MemberEdit',
        method: 'post',
        data
    })
}

export function includeOrReturn(data) {
    return request({
        url: serviceAreaName + 'IncludeOrReturn',
        method: 'post',
        data
    })
}

export function deleteHisReportTask(data) {
    return request({
        url: serviceAreaName + 'DeleteHisReportTask',
        method: 'post',
        data
    })
}

export function anyUnincludedTask(params) {
    return request({
        url: serviceAreaName + 'AnyUnincludedTask',
        method: 'get',
        params
    })
}

export function getChartEmployeeData(params) {
    return request({
        url: serviceAreaName + 'ChartEmployeeData',
        method: 'get',
        params
    })
}

export function getChartData(params) {
    return request({
        url: serviceAreaName + 'ChartData',
        method: 'get',
        params
    })
}


 export function getWorkPlanDailyWhiteList(params) {
    return request({
        url: serviceAreaName + 'GetWorkPlanDailyWhiteList',
        method: 'get',
        params
    })
}

export function setWorkPlanDailyWhiteList(data) {
    return request({
        url: serviceAreaName + 'SetWorkPlanDailyWhiteList',
        method: 'post',
        data
    })
}

export function getWeekPlanTimeList(data) {
    return request({
        url: serviceAreaName + 'WeekPlanTimeList',
        method: 'post',
        data
    })
}

export function getMonthPlan(data) {
    return request({
        url: serviceAreaName + 'MonthPlan',
        method: 'post',
        data
    })
}

export function getWeekPlan(data) {
    return request({
        url: serviceAreaName + 'WeekPlan',
        method: 'post',
        data
    })
}

export function getThisWeekTask(params) {
    return request({
        url: serviceAreaName + 'ThisWeekTask',
        method: 'get',
        params
    })
}


export function getThisWeekTaskByDate(params) {
    return request({
        url: serviceAreaName + 'ThisWeekTaskByDate',
        method: 'get',
        params
    })
}

export function sendBack(data) {
    return request({
        url: serviceAreaName + 'SendBack',
        method: 'post',
        data
    })
}