import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/SystemEmployeeRecordRemind/'

// 获取设员工档案消息提醒
export function GetRecordRemindingSetting(params) {
    return request({
        url: serviceAreaName + 'GetRecordRemindingSetting',
        method: 'get',
        params
    })
}

// 设置员工档案消息提醒
export function SetRecordRemindingSetting(data) {
    return request({
        url: serviceAreaName + 'SetRecordRemindingSetting',
        method: 'post',
        data
    })
}

// 获取员工档案消息提醒规则 详情
export function GetRecordRemindingSettingRuleDetail(params) {
    return request({
        url: serviceAreaName + 'GetRecordRemindingSettingRuleDetail',
        method: 'get',
        params
    })
}

// 添加员工档案消息提醒规则
export function AddRecordRemindingSettingRule(data) {
    return request({
        url: serviceAreaName + 'AddRecordRemindingSettingRule',
        method: 'post',
        data
    })
}

// 编辑员工档案消息提醒规则
export function EditRecordRemindingSettingRule(data) {
    return request({
        url: serviceAreaName + 'EditRecordRemindingSettingRule',
        method: 'post',
        data
    })
}

// 删除员工档案消息提醒规则
export function DeleteRecordRemindingSettingRule(params) {
    return request({
        url: serviceAreaName + 'DeleteRecordRemindingSettingRule',
        method: 'get',
        params
    })
}

