import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business



export function saveTheSubmission(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementDemand/SaveTheSubmission',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementDemand/GetProjectManagementDemandList',
        method: 'post',
        data
    })
}

export function getAllDemandsPoolList(params) {
    return request({
        url: serviceAreaName + '/DemandsPoolManagement/GetAllDemandsPoolList',
        method: 'get',
        params
    })
}


export function getDemandsPoolById(params) {
    return request({
        url: serviceAreaName + '/DemandsPoolManagement/GetDemandsPoolDetailById',
        method: 'get',
        params
    })
}


export function detail(params) {
    return request({
        url: serviceAreaName + '/ProjectManagementDemand/GetProjectManagementDemandDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementDemand/AddProjectManagementDemand',
        method: 'post',
        data
    })
}


export function del(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementDemand/DeleteProjectManagementDemand',
        method: 'post',
        data
    })
}


export function edit(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementDemand/EditProjectManagementDemand',
        method: 'post',
        data
    })
}

//获取开放的需求
export function getAllProjectManagementDemandList(params) {
    return request({
        url: serviceAreaName + '/ProjectManagementDemand/GetAllProjectManagementDemandList',
        method: 'get',
        params
    })
}

