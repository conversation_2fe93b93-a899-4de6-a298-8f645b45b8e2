import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.user
export function getList(data) {
    return request({
        url: serviceAreaName + '/Enterprise/GetEnterpriseList',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/Enterprise/Edit',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/Enterprise/Add',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + '/Enterprise/Delete',
        method: 'post',
        data
    })
}

export function setAdministrator(params){
    return request({
        url: serviceAreaName + '/Enterprise/SetEnterpriseAdministrator',
        method: 'post',
        params
    })
}

export function getAllEnterprise(params) {
    return request({
        url: serviceAreaName + '/Enterprise/GetEnterpriseAllListAsync',
        method: 'get',
        params
    })
}