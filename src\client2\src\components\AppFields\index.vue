<template>
  <el-popover
    placement="bottom-start"
    width="240"
    popper-class="filter-popper-wrapper"
    trigger="click"
    ref="fieldGroupsPopoverRef"
  >
    <div class="fields-wrapper">
      <div class="search">
        <el-input
          style="width: 100%;"
          placeholder="搜索关键词"
          v-antiShake="{
            time: 300,
            callback: () => {},
          }"
          clearable
          v-model="keywords"
        ></el-input>
      </div>

      <!-- 冻结的列 -->
      <div class="groups">
        <!-- 已冻结的列——不支持排序 -->

        <div class="groups-wrapper" v-show="fixedCols.length > 0">
          <div v-for="(f, gIdx) in fixedCols" :key="`g-${gIdx}`">
            <div class="field-item-wrapper">
              <span class="move-wrapper"></span>
              <span class="flex-1 title">
                {{ f.attr.label }}
                <span v-if="f.attr.fixed">（冻结）</span>
              </span>
              <el-switch
                :disabled="f.attr.disabledClose"
                :key="f.attr.prop"
                :change="handleChange"
                v-model="f.attr.isShow"
              ></el-switch>
            </div>
          </div>
        </div>
        <!-- 未冻结的列——支持排序 -->
        <noData
          size="mini"
          v-if="fieldsList.filter(ff => ff.attr.label.indexOf(keywords) > -1).length == 0"
        ></noData>

        <draggable
          v-model="fieldsList"
          tag="div"
          class="groups-wrapper"
          handle=".mover"
          filter=".hide"
          @end="onEnd"
        >
          <transition-group>
            <!-- 只显示未冻结的列，没有过滤掉，是考虑到排序问题，该容器里面需要支持排序 -->
            <div
              v-for="(f, gIdx) in fieldsList.filter(ff => ff.attr.label.indexOf(keywords) > -1)"
              :key="f.attr.prop"
              v-show="!f.attr.fixed"
              :class="!f.attr.fixed ? 'mover' : 'hide'"
            >
              <div class="field-item-wrapper">
                <span class="move-wrapper">
                  <svg-icon @click.stop icon-class="move" className="icon-mini move"></svg-icon>
                </span>
                <span class="flex-1 title">
                  {{ f.attr.label }}
                  <span v-if="f.attr.fixed">（冻结）</span>
                </span>
                <el-switch
                  @change="onEnd()"
                  :disabled="f.attr.disabledClose"
                  :key="f.attr.prop"
                  v-model="f.attr.isShow"
                ></el-switch>
              </div>
            </div>
          </transition-group>
        </draggable>
      </div>
    </div>
    <AppToggleButton slot="reference" style="margin-right: 4px;" :list="cardList">
      <template v-for="t in cardList" :slot="t.value">
        <svg-icon icon-class="ziduanpeizhi" className="icon-mini"></svg-icon>
        <span style="margin-left: 4px;">{{ t.label }}</span>
      </template>
    </AppToggleButton>

    <div class="footer">
      <app-cus-button
        type="default"
        text="恢复默认"
        @click="handleReset()"
        style="margin-right: 4px;"
      ></app-cus-button>

      <span class="flex-1"></span>

      <!-- <app-cus-button
        type="default"
        text="取消"
        @click="handleClose()"
        style="margin-right: 4px;"
      ></app-cus-button>
      <app-cus-button type="primary" text="确认" @click="handleSave()"></app-cus-button> -->
    </div>
  </el-popover>
</template>

<script>
import noData from "@/views/common/components/noData";
import draggable from "vuedraggable";
import * as cte from "@/api/configTableHead";
import { saveTabColumns } from '@/utils/customTab'

export default {
  name: "AppFields",
  components: {
    noData,
    draggable,
  },
  mounted() {

  },
  computed: {
    fixedCols() {
      return this.fieldsList.filter(s => s.attr.fixed && s.attr.label.indexOf(this.keywords) > -1);

      // fixedCols.filter(ff => ff.attr.label.indexOf(keywords) > -1)
    },
  },
  props: {
    /**
     * 表格枚举：
     * 1：超级干线管理首页列表表格（/workbench/mainLineMgmt）
     *
     */
    viewBusinessType: {
      type: Number,
      default: -1,
    },
    /**
     * 当前字段集合
     */
    fields: {
      type: Array,
      default: () => {
        return [];
      },
    },
    /**
     * 原始字段集合
     */
    fieldsOrg: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  watch: {
    fields: {
      handler(val) {
        this.fieldsList = JSON.parse(JSON.stringify(val));
      },
      deep: true,
      immediate: true,
    },
    fieldsOrg: {
      handler(val) {
        this.fieldsOrgList = JSON.parse(JSON.stringify(val));
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    return {
      cardList: [{ value: 111, label: "字段显示" }],
      keywords: "",
      fieldsList: [],
      fieldsOrgList: [],
    };
  },
  methods: {
    handleChange() {
      this.$nextTick(() => {
        this.handleSave()
      })
    },
    onEnd() {
      this.$emit("optSuccess", JSON.parse(JSON.stringify(this.fieldsList.map(s => s.attr))));
      this.handleSave()
    },
    // 恢复默认
    handleReset() {
      this.fieldsList = JSON.parse(JSON.stringify(this.fieldsOrgList));
      this.onEnd();

      // this.fieldsList.forEach(field => {
      //   this.$set(field.attr, "isShow", !!field.attr.defaultShow);
      // });
    },
    // 关闭
    handleClose() {
      this.$refs["fieldGroupsPopoverRef"] && this.$refs["fieldGroupsPopoverRef"].doClose();
    },
    handleSave() {

      saveTabColumns(this.fieldsList, this.viewBusinessType)

      // if (this.viewBusinessType <= 0) {
      //   console.log('未配置 viewBusinessType 参数，所以无法保存')
      //   return;
      // }

      // let list = this.fieldsList.map(s => {
      //   let temp = {
      //     prop: s.attr.prop,
      //     isShow: !!s.attr.isShow,
      //     fixed: s.attr.fixed || "",
      //     // width: s.attr.width || ""
      //   };
      //   return temp;
      // });

      // let postDatas = {
      //   ViewBusinessType: this.viewBusinessType,
      //   Content: JSON.stringify(list),
      // };

      // cte.edit(postDatas).then(res => {
      //   // this.$message.success("保存成功");
      //   // this.handleClose();
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.fields-wrapper {
  .search {
    padding: 10px;
  }

  .groups {
    max-height: 400px;
    overflow-y: auto;
    padding: 5ox 0;
    .groups-wrapper {
      .field-item-wrapper {
        padding: 3px 10px;
        padding-left: 5px;
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        .move-wrapper{
          width: 16px;
          height: 16px;
          margin-right: 2px;
          .move{
            display: none;
            cursor: move;
          }
        }
        &:hover{
          .move{
            display: inline-block;
          }
        }
        .title{
          line-height: 16px;
        }

      }
    }
    // .title {
    //   color: $placeholder;
    //   padding: 10px;
    //   padding-bottom: 0;
    //   border-top: 1px solid $border-color-light;
    // }
    // .fields {
    //   padding: 10px;
    // }
  }
}

.footer {
  display: flex;
}

// .move-icon-wrapper {
//   width: 20px;
//   justify-content: center;
//   cursor: pointer;
// }


</style>
