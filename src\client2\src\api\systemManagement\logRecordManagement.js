
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/LogRecord/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
  return request({
      url: serviceAreaName + 'GetDetails',
      method: 'get',
      params
  })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function activeUserChart(data) {
    return request({
        url: serviceAreaName + 'ActiveUserChart',
        method: 'post',
        data
    })
}

export function dataStatistics(data) {
    return request({
        url: serviceAreaName + 'DataStatistics',
        method: 'post',
        data
    })
}

export function statisticsMagnitude(params) {
    return request({
        url: serviceAreaName + 'StatisticsMagnitude',
        method: 'get',
        params
    })
}

export function applyRank(data) {
    return request({
        url: serviceAreaName + 'ApplyRank',
        method: 'post',
        data
    })
}
