import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
/**验证是否允许添加基础参数
 */
export function allowedAddPlcFunction(params) {
    return request({
        url: serviceAreaName + '/PlcFunction/AllowedAddPlcFunction',
        method: 'GET',
        params
    })
}

/**基础参数配置新增
 */
export function addPlcFunction(data) {
    return request({
        url: serviceAreaName + '/PlcFunction/Add',
        method: 'POST',
        data
    })
}

/**基础参数配置编辑
 */
export function editPlcFunction(data) {
    return request({
        url: serviceAreaName + '/PlcFunction/Edit',
        method: 'POST',
        data
    })
}

/**基础参数配置删除
 */
export function deletePlcFunction(data) {
    return request({
        url: serviceAreaName + '/PlcFunction/Delete',
        method: 'POST',
        data
    })
}

/**基础参数配置分页列表
 */
export function getPlcFunctionListPage(data) {
    return request({
        url: serviceAreaName + '/PlcFunction/GetListPage',
        method: 'POST',
        data
    })
}

/**基础参数配置完整列表
 */
export function getPlcFunctionList(params) {
    return request({
        url: serviceAreaName + '/PlcFunction/GetList',
        method: 'GET',
        params
    })
}


/**验证是否允许添加参数所属节点类型
 */
export function allowedAddPlcFunctionParamType(params) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamType/AllowedAddPlcFunctionParamType',
        method: 'GET',
        params
    })
}


/**参数所属节点类型新增
 */
export function addPlcFunctionParamType(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamType/Add',
        method: 'POST',
        data
    })
}

/**参数所属节点类型编辑
 */
export function editPlcFunctionParamType(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamType/Edit',
        method: 'POST',
        data
    })
}

/**参数所属节点类型删除
 */
export function deletePlcFunctionParamType(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamType/Delete',
        method: 'POST',
        data
    })
}

/**参数所属节点类型分页列表
 */
export function getPlcFunctionParamTypeListPage(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamType/GetListPage',
        method: 'POST',
        data
    })
}

/**参数所属节点类型完整列表
 */
export function getPlcFunctionParamTypeList(params) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamType/GetList',
        method: 'GET',
        params
    })
}


/**参数单位新增
 */
export function addPlcFunctionParamUnit(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamUnit/Add',
        method: 'POST',
        data
    })
}

/**参数单位编辑
 */
export function editPlcFunctionParamUnit(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamUnit/Edit',
        method: 'POST',
        data
    })
}

/**参数单位删除
 */
export function deletePlcFunctionParamUnit(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamUnit/Delete',
        method: 'POST',
        data
    })
}

/**参数单位分页列表
 */
export function getPlcFunctionParamUnitListPage(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionParamUnit/GetListPage',
        method: 'POST',
        data
    })
}

/**参数单位完整列表
 */
export function getPlcFunctionParamUnitList() {
    return request({
        url: serviceAreaName + '/PlcFunctionParamUnit/GetList',
        method: 'GET'
    })
}


/**参数分类新增
 */
export function addPlcFunctionType(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionType/Add',
        method: 'POST',
        data
    })
}

/**参数分类编辑
 */
export function editPlcFunctionType(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionType/Edit',
        method: 'POST',
        data
    })
}

/**参数分类删除
 */
export function deletePlcFunctionType(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionType/Delete',
        method: 'POST',
        data
    })
}

/**参数分类分页列表
 */
export function getPlcFunctionTypeListPage(data) {
    return request({
        url: serviceAreaName + '/PlcFunctionType/GetListPage',
        method: 'POST',
        data
    })
}

/**参数分类完整列表
 */
export function getPlcFunctionTypeList() {
    return request({
        url: serviceAreaName + '/PlcFunctionType/GetList',
        method: 'GET'
    })
}
