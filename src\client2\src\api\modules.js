import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let userServiceAreaName = serviceArea.user
// export function get(params) {
//   return request({
//     url: '/Modules/Get',
//     method: 'get',
//     params
//   })
// }

export function loadMenus(moduleId) {
  return request({
    url: userServiceAreaName + '/Modules/LoadMenus',
    method: 'get',
    params: { moduleId: moduleId }
  })
}

export function loadForRole(roleId) {
  return request({
    url: userServiceAreaName + '/Modules/LoadForRole',
    method: 'get',
    params: { firstId: roleId }
  })
}

export function add(data) {
  return request({
    url: userServiceAreaName + '/Modules/Add',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: userServiceAreaName + '/Modules/Update',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: userServiceAreaName + '/Modules/Delete',
    method: 'post',
    data
  })
}

export function addMenu(data) {
  return request({
    url: userServiceAreaName + '/Modules/AddMenu',
    method: 'post',
    data
  })
}

export function updateMenu(data) {
  return request({
    url: userServiceAreaName + '/Modules/UpdateMenu',
    method: 'post',
    data
  })
}

export function delMenu(data) {
  return request({
    url: userServiceAreaName + '/Modules/DeleteMenu',
    method: 'post',
    data
  })
}

export function loadMenusForRole(roleId) {
  return request({
    url: userServiceAreaName + '/Modules/LoadMenusForRole',
    method: 'get',
    params: { moduleId: '', firstId: roleId }
  })
}

export function getProperties(code) {
  return request({
    url: userServiceAreaName + '/Modules/GetProperties',
    method: 'get',
    params: { moduleCode: code }
  })
}

export function loadPropertiesForRole(code, roleId) {
  return request({
    url: userServiceAreaName + '/Modules/LoadPropertiesForRole',
    method: 'get',
    params: { moduleCode: code, roleId: roleId }
  })
}

export function assignDataProperty(code, roleId, properties) {
  return request({
    url: userServiceAreaName +'/AccessObjs/AssignDataProperty',
    method: 'post',
    data: { moduleCode: code, roleId: roleId, 'properties': properties }
  })
}

export function unAssignDataProperty(code, roleId) {
  return request({
    url: userServiceAreaName +'/AccessObjs/UnAssignDataProperty',
    method: 'post',
    data: { moduleCode: code, roleId: roleId, 'properties': [] }
  })
}



//为字段添加过滤规则
export function addFilterRuleForField(data) {
  return request({
    url: userServiceAreaName + '/Modules/EditDataFilterRule',
    method: 'post',
    data
  })
}

export function getFilterRules(data) {
  return request({
    url: userServiceAreaName + '/Modules/GetDataFilterRule',
    method: 'post',
    data
  })
}


export function getRelevance() {
  return request({
    url: userServiceAreaName + '/Modules/GetRelevance',
    method: 'get'
  })
}

export function getAuthActionTree() {
  return request({
    url: userServiceAreaName + '/Modules/GetAssembly',
    method: 'get'
  })
}

export function getBusinessRolePage() {
  return request({
    url: userServiceAreaName + '/Modules/GetBusinessRolePage',
    method: 'get',
  })
}