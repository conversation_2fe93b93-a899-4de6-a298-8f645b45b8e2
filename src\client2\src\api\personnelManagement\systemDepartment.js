
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/SystemDepartment/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(params) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'get',
        params
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function setDuty(params) {
    return request({
        url: serviceAreaName + 'SetDuty',
        method: 'get',
        params
    })
}

export function getDepartmentPrincipal(params) {
    return request({
        url: serviceAreaName + 'GetDepartmentPrincipal',
        method: 'get',
        params
    })
}
