import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/ProjectRole/'

export function getList(data) {
    return request({
        url: busServiceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: busServiceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: busServiceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: busServiceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: busServiceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getRolesByProjectId(params) {
    return request({
        url: busServiceAreaName + 'CurrentEmployeePermission',
        method: 'get',
        params
    })
}


export function getAllCommonRoles(data) {
    return request({
        url: busServiceAreaName + 'GetListByConditionAsync',
        method: 'post',
        data
    })
}