import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkIPDStageReview/'

export function initiateReview(data) {
    return request({
        url: serviceAreaName + 'InitiateReview',
        method: 'post',
        data
    })
}

export function reInitiateReview(data) {
    return request({
        url: serviceAreaName + 'ReInitiateReview',
        method: 'post',
        data
    })
}


export function createApproval(data){
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function revocation(data){
    return request({
        url: serviceAreaName + 'Revocation',
        method: 'post',
        data
    })
}


