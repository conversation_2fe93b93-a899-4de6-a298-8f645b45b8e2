import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/Meeting/'


export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function editSummary(data) {
    return request({
        url: serviceAreaName + 'EditSummary',
        method: 'post',
        data
    })
}

export function getPersonnel(params) {
    return request({
        url: serviceAreaName + 'GetPersonnel',
        method: 'get',
        params
    })
}

export function getSuggestionAndWorkableItemList(params) {
    return request({
        url: serviceAreaName + 'GetSuggestionAndWorkableItemList',
        method: 'get',
        params
    })
}

export function getStatistics(params) {
    return request({
        url: serviceAreaName + 'Statistics',
        method: 'get',
        params
    })
}

export function editMeetingState(data) {
    return request({
        url: serviceAreaName + 'EditMeetingState',
        method: 'post',
        data
    })
}
