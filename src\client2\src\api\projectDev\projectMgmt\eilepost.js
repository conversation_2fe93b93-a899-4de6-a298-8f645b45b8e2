import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/Milepost/'

export function getList(data) {
  return request({
    url: busServiceAreaName + 'GetListPage',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: busServiceAreaName + 'Edit',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: busServiceAreaName + 'Delete',
    method: 'post',
    data
  })
}

//项目看板蛇形图接口
export function getListByCondition(data) {
  return request({
    url: busServiceAreaName + 'GetListByCondition',
    method: 'post',
    data
  })
}
// 项目看板蛇形图接口,获得项目看板里程碑详情列表
export function getDetailsListAsync(params) {
  return request({
    url: busServiceAreaName + 'GetDetailsListAsync',
    method: 'get',
    params
  })
}




export function start(data) {
  return request({
    url: busServiceAreaName + 'StartAsync',
    method: 'post',
    data
  })
}

export function end(data) {
  return request({
    url: busServiceAreaName + 'AccomplishAsync',
    method: 'post',
    data
  })
}

export function detail(params) {
  return request({
    url: busServiceAreaName + 'GetDetails',
    method: 'get',
    params
  })
}