import request from '@/utils/request'
import { serviceArea } from '@/api/serviceArea'

let serviceAreaName = serviceArea.business + '/CustomerServiceManage'

/******************************************** 业务员 ********************************************/
/**新增 */
export function addCustomerServiceManage(data) {
    return request({
        url: serviceAreaName + '/Add',
        method: 'post',
        data
    })
}

/**新增集合 */
export function addCustomerServiceManageList(data) {
    return request({
        url: serviceAreaName + '/AddList',
        method: 'post',
        data
    })
}

/**删除 */
export function deleteCustomerServiceManage(data) {
    return request({
        url: serviceAreaName + '/Delete',
        method: 'post',
        data
    })
}

/**修改 */
export function editCustomerServiceManage(data) {
    return request({
        url: serviceAreaName + '/Edit',
        method: 'post',
        data
    })
}

/**获取详情 */
export function getCustomerServiceManageDetails(params) {
    return request({
        url: serviceAreaName + '/GetDetails',
        method: 'get',
        params
    })
}

/**获取分页列表 */
export function getCustomerServiceManageListPage(data) {
    return request({
        url: serviceAreaName + '/GetListPage',
        method: 'post',
        data
    })
}

/**获取条件查询所有内容 */
export function getCustomerServiceManageListByCondition(data) {
    return request({
        url: serviceAreaName + '/GetListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 业务员区域 ********************************************/
/**新增 */
export function addCustomerServiceDepartment(data) {
    return request({
        url: serviceAreaName + '/AddCustomerServiceDepartment',
        method: 'post',
        data
    })
}

/**新增集合 */
export function addCustomerServiceDepartmentList(data) {
    return request({
        url: serviceAreaName + '/AddCustomerServiceDepartmentList',
        method: 'post',
        data
    })
}

/**删除 */
export function deleteCustomerServiceDepartment(data) {
    return request({
        url: serviceAreaName + '/DeleteCustomerServiceDepartment',
        method: 'post',
        data
    })
}

/**修改 */
export function editCustomerServiceDepartment(data) {
    return request({
        url: serviceAreaName + '/EditCustomerServiceDepartment',
        method: 'post',
        data
    })
}

/**获取详情 */
export function getCustomerServiceDepartmentDetails(params) {
    return request({
        url: serviceAreaName + '/GetCustomerServiceDepartmentDetails',
        method: 'get',
        params
    })
}

/**获取分页列表 */
export function getCustomerServiceDepartmentListPage(data) {
    return request({
        url: serviceAreaName + '/GetCustomerServiceDepartmentListPage',
        method: 'post',
        data
    })
}

/**获取条件查询所有内容 */
export function getCustomerServiceDepartmentListByCondition(data) {
    return request({
        url: serviceAreaName + '/GetCustomerServiceDepartmentListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 自定义 ********************************************/


/**获取条件查询所有内容 */
export function changeCustomerServiceDepartment(data) {
    return request({
        url: serviceAreaName + '/ChangeCustomerServiceDepartment',
        method: 'post',
        data
    })
}