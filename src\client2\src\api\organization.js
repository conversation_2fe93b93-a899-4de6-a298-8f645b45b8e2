import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
//获取有效的组织架构（已过滤无效数据）
export function getValidOrgs(params) {
    return request({
        url: serviceAreaName + '/Organization/GetValidOrganizations',
        method: 'get',
        params
    })
}

export function getOrgsByLevel(params) {
    return request({
        url: serviceAreaName + '/Organization/GetOrganization',
        method: 'get',
        params
    })
}

export function getOrgs(params) {
    return request({
        url: serviceAreaName + '/Organization/GetOrganizations',
        method: 'get',
        params
    })
}

export function getSubOrgs(data) {
    return request({
        url: serviceAreaName + '/Organization/GetOrganizationWithChildren',
        method: 'post',
        data
    })
}

//新增一级组织和二级组织合并为一个接口，以适配模块管理ActionName配置
export function addOrg(data){
    return request({
        url: serviceAreaName + '/Organization/AddOrganization',
        method: 'post',
        data
    })
}

export function editOrg(data){
    return request({
        url: serviceAreaName + '/Organization/EditOrganization',
        method: 'post',
        data
    })
}

export function getOrgInfo(params) {
    return request({
        url: serviceAreaName + '/Organization/GetOrganizationWithDetail',
        method: 'get',
        params
    })
}


export function del(data){
    return request({
        url: serviceAreaName + '/Organization/DeleteOrganizations',
        method: 'post',
        data
    })
}

export function getMajorList(params) {
    return request({
        url: serviceAreaName + '/Organization/GetMajorList',
        method: 'get',
        params
    })
}

//根据当前用户获取具有查看权限的组织
export function getAuthOrgs() {
    return request({
        url: serviceAreaName + '/Organization/GetTaskManagementOrganization',
        method: 'get'
    })
}