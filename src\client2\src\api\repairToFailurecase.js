import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
//获取维修单转案例库审批列表
export function getList(data) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/GetListPage',
        method: 'post',
        data
    })
}


export function add(data) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/Create',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/GetDetails',
        method: 'get',
        params
    })
}

//转审
export function assign(data) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/Assign',
        method: 'post',
        data
    })
}

//撤回
export function revoke(params) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/Revoke',
        method: 'get',
        params
    })
}


//驳回、同意
export function audit(data) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/NodeAudit',
        method: 'post',
        data
    })
}

export function getApprovalUser() {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/GetUsersOfRepairOrderToCase',
        method: 'get'
    })
}

export function del(params) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/DeleteById',
        method: 'get',
        params
    })
}

export function getApprovalLogs(params) {
    return request({
        url: serviceAreaName + '/RepairOrderToFailureCases/GetOperationHistories',
        method: 'get',
        params
    })
}