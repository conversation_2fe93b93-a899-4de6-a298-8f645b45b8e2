import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/TimecardDepartment/'


let serviceUserName = serviceArea.user + '/TimecardEmployee/'

let serviceBusName = serviceArea.business

let serviceUserName2 = serviceArea.business + '/TimecardAppeal/'

let serviceUserAttendance = serviceArea.user + '/TimecardAttendanceConfirm/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function computationDay(data) {
    return request({
        url: serviceAreaName + 'ComputationDay',
        method: 'post',
        data
    })
}



export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

//判断当前用户是否为排班管理员（任一考勤组下）
export function currentTimecardSchedulingManger(params) {
    return request({
        url: serviceAreaName + 'currentTimecardSchedulingManger',
        method: 'get',
        params
    })
}

export function getAttendanceOfMonth(data) {
    return request({
        url: serviceAreaName + 'getAttendanceOfMonth',
        method: 'post',
        data
    })
}

export function whetherIllegalValue(data) {
    return request({
        url: serviceAreaName + 'whetherIllegalValue',
        method: 'post',
        data
    })
}

export function editTimecardScheduling(data) {
    return request({
        url: serviceAreaName + 'EditTimecardScheduling',
        method: 'post',
        data
    })
}


export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//获取已经加入组的用户（参数 TimecardDepartmentIdList 排除组 id 集合）
export function getTimecardDepartmentEmployee(data) {
    return request({
        url: serviceAreaName + 'GetTimecardDepartmentEmployee',
        method: 'post',
        data
    })
}

//获取考勤明细
export function getTimecardEmployeeRecordDetail(data) {
    return request({
        url: serviceAreaName + 'GetTimecardEmployeeRecordDetail',
        method: 'post',
        data
    })
}


//获取考勤记录（打卡记录）
export function getTimecardEmployeeRecords(data) {
    return request({
        url: serviceAreaName + 'GetTimecardEmployeeRecords',
        method: 'post',
        data
    })
}


//获取考勤报告
export function getTimecardReport(data) {
    return request({
        url: serviceAreaName + 'GetTimecardReport',
        method: 'post',
        data
    })
}

export function getTimecardSetException(params) {
    return request({
        url: serviceAreaName + 'GetTimecardSetException',
        method: 'get',
        params
    })
}

//异常设置保存
export function timecardSetException(data) {
    return request({
        url: serviceAreaName + 'TimecardSetException',
        method: 'post',
        data
    })
}

//获取个人考勤日历
export function getPersonalTimecardRecords(data) {
    return request({
        url: serviceAreaName + 'GetPersonalTimecardRecords',
        method: 'post',
        data
    })
}

//获取个人排班月历
export function getTimecardScheduling(data) {
    return request({
        url: serviceAreaName + 'GetTimecardScheduling',
        method: 'post',
        data
    })
}


export function getPrincipalEmployeeList(params) {
    return request({
        url: serviceAreaName + 'GetPrincipalEmployeeList',
        method: 'get',
        params
    })
}

export function getEmployeeList(data) {
    return request({
        url: serviceAreaName + 'GetEmployeeList',
        method: 'post',
        data
    })
}

export function getPersonalBusinessTripTimecardRecords(params) {
    return request({
        url: serviceAreaName + 'GetPersonalBusinessTripTimecardRecords',
        method: 'get',
        params
    })
}




//申诉
export function addTimecardAppeal(data) {
    return request({
        url: serviceUserName2 + 'AddTimecardAppeal',
        method: 'post',
        data
    })
}
export function createApproval(data) {
    return request({
        url: serviceUserName2 + 'Approval',
        method: 'post',
        data
    })
}

export function getListPageTimecardAppeal(data) {
    return request({
        url: serviceUserName2 + 'GetListPageTimecardAppeal',
        method: 'post',
        data
    })
}
export function getPendingAppeal(params) {
    return request({
        url: serviceUserName + 'GetPendingAppeal',
        method: 'get',
        params
    })
}
export function getTimecardAppealDetails(params) {
    return request({
        url: serviceUserName2 + 'GetTimecardAppealDetails',
        method: 'get',
        params
    })
}
export function getDetails(params) {
    return request({
        url: serviceUserName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getLeftDetails(params) {
    return request({
        url: serviceBusName + '/HRLeaveBalanceData/GetDetails',
        method: 'get',
        params
    })
}

export function getHistory(params) {
    return request({
        url: serviceBusName + '/HRLeaveBalanceData/GetHistory',
        method: 'get',
        params
    })
}

export function editHoliday(data) {
    return request({
        url: serviceBusName + '/HRLeaveBalanceData/Edit',
        method: 'post',
        data
    })
}

export function getAllComment(data) {
    return request({
        url: serviceBusName + '/Comment/GetAllComment',
        method: 'post',
        data
    })
}


export function getTimecardAttendanceConfirm(data) {
    return request({
        url: serviceUserAttendance + 'Check',
        method: 'post',
        data
    })
}


export function confirmTimecardAttendance(data) {
    return request({
        url: serviceUserAttendance + 'Add',
        method: 'post',
        data
    })
}

// export function getListByCondition(data) {
//     return request({
//         url: serviceAreaName + 'GetListByCondition',
//         method: 'post',
//         data
//     })
// }