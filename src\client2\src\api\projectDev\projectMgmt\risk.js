import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/Risk/'

export function getList(data) {
  return request({
    url: busServiceAreaName + 'GetListPage',
    method: 'post',
    data
  })
}

/**根据条件查询 */
export function getListByCondition(data) {
  return request({
    url: busServiceAreaName + 'GetListByCondition',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: busServiceAreaName + 'Delete',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: busServiceAreaName + 'Edit',
    method: 'post',
    data
  })
}

export function detail(params) {
  return request({
    url: busServiceAreaName + 'GetDetails',
    method: 'get',
    params
  })
}

export function relivev(data) {
  return request({
    url: busServiceAreaName + 'RelieveAsync',
    method: 'post',
    data
  })
}

