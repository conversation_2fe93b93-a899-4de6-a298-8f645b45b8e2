
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/RegionalManagement/'

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function addSingle(data) {
    return request({
        url: serviceAreaName + 'AddSingle',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}
export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function getPoorListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetPoorListByCondition',
        method: 'post',
        data
    })
}


export function getAllChildrenByRegionalId(params) {
    return request({
        url: serviceAreaName + 'GetAllChildrenByRegionalId',
        method: 'get',
        params
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function approval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function batcheEnabledState(data) {
    return request({
        url: serviceAreaName + 'BatcheEnabledState',
        method: 'post',
        data
    })
}

export function dataMigration(data) {
    return request({
        url: serviceAreaName + 'DataMigration',
        method: 'post',
        data
    })
}



