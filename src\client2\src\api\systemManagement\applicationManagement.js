
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceApplicationTypeName = serviceArea.user + '/ApplicationType/'
let serviceApplicationManagementName = serviceArea.user+'/ApplicationManagement/'
let serviceApplicationElementName = serviceArea.user+'/ApplicationElement/'

export function addType(data) {
    return request({
        url: serviceApplicationTypeName + 'Add',
        method: 'post',
        data
    })
}

export function detailType(params) {
    return request({
        url: serviceApplicationTypeName + 'GetDetails',
        method: 'get',
        params
    })
}


export function editType(data) {
    return request({
        url: serviceApplicationTypeName + 'Edit',
        method: 'post',
        data
    })
}

export function delType(data) {
    return request({
        url: serviceApplicationTypeName + 'Delete',
        method: 'post',
        data
    })
}
export function getListType(data) {
    return request({
        url: serviceApplicationTypeName + 'GetListPage',
        method: 'post',
        data
    })
}

export function getAllType(data) {
    return request({
        url: serviceApplicationTypeName + 'GetListByCondition',
        method: 'post',
        data
    })
}





export function BatchEdit(data) {
    return request({
        url: serviceApplicationManagementName + 'BatchEdit',
        method: 'post',
        data
    })
}
export function addApplication(data) {
    return request({
        url: serviceApplicationManagementName + 'Add',
        method: 'post',
        data
    })
}




export function editApplication(data) {
    return request({
        url: serviceApplicationManagementName + 'Edit',
        method: 'post',
        data
    })
}

export function delApplication(data) {
    return request({
        url: serviceApplicationManagementName + 'Delete',
        method: 'post',
        data
    })
}
export function getListApplication(data) {
    return request({
        url: serviceApplicationManagementName + 'GetListPage',
        method: 'post',
        data
    })
}


export function detailApplication(params) {
    return request({
        url: serviceApplicationManagementName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getListByConditionpplication(data) {
    return request({
        url: serviceApplicationManagementName + 'GetListByCondition',
        method: 'post',
        data
    })
}




export function addElement(data) {
    return request({
        url: serviceApplicationElementName + 'Add',
        method: 'post',
        data
    })
}

export function detailElement(params) {
    return request({
        url: serviceApplicationElementName + 'GetDetails',
        method: 'get',
        params
    })
}


export function editElement(data) {
    return request({
        url: serviceApplicationElementName + 'Edit',
        method: 'post',
        data
    })
}

export function delElement(data) {
    return request({
        url: serviceApplicationElementName + 'Delete',
        method: 'post',
        data
    })
}
export function getListElement(data) {
    return request({
        url: serviceApplicationElementName + 'GetListPage',
        method: 'post',
        data
    })
}
