<template>
  <div class="avatar-list-wrapper" v-if="showList && showList.length > 0">
    <div class="avatar-list" :style="{ height: `${size}px` }">
      <div
        class="avatar-wrapper"
        v-for="(i, idx) in showList"
        :key="`emp-${idx}`"
        :style="[{ left: `${idx * 20}px` }]"
      >
        <el-avatar :size="size" :src="i.AvatarPath" :title="i.Name"></el-avatar>
        <!-- 头像多于1个，也不能显示名称 -->
        <span class="name" v-if="showName && showList.length == 1">{{ i.Name }}</span>
      </div>

      <div
        v-if="emps.length > limit"
        class="avatar-wrapper num-warpper"
        :style="[{ left: `${limit * 20}px` }, { width: `${size}px` }, { height: `${size}px` }]"
      >
        <span class="num" :title="emps.map(s => s.Name).join('、')">+{{ notExpandNum }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AppAvatar",
  computed: {
    notExpandNum() {
      let temp = this.emps.length - this.limit;
      return temp > 99 ? 99 : temp;
    },
    showList() {
      if (this.emps && this.emps.length > 0) {
        let limit = this.limit;
        if (limit > this.emps.length) {
          limit = this.emps.length;
        }
        let temp = this.emps.slice(0, limit);

        return temp;
      }
      return [];
    },
  },
  props: {
    /**
     * 员工对象列表
     */
    emps: {
      type: Array,
      default: () => {
        return [];
      },
    },
    /**
     * 头像大小
     */
    size: {
      type: Number,
      default: 28,
    },
    /**
     * 是否显示名称
     */
    showName: {
      type: Boolean,
      default: true,
    },
    /**
     * 限制显示多少个头像，超出显示，显示一个头像大小的数字
     */
    limit: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {};
  },
};
</script>

<style scoped lang="scss">
.common-border {
  border: 1px solid #fff;
}

.avatar-list-wrapper {
  .avatar-list {
    line-height: normal;
    display: flex;
    align-items: center;
    .avatar-wrapper {
      border-radius: 15px;
      background: rgba($color: $color-primary, $alpha: 0.15);
      display: flex;
      align-items: center;
      /deep/.el-avatar {
        @extend .common-border;
      }
      .name {
        padding-left: 8px;
        padding-right: 12px;
        white-space: nowrap;
      }
    }
    .num-warpper {
      @extend .common-border;
      background: $bg-color-2;
      color: $text-secondary;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
