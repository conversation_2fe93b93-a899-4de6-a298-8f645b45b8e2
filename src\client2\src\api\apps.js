import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/AppVersion/GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + '/AppVersion/GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/AppVersion/Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/AppVersion/Edit',
        method: 'post',
        data
    })
}


export function del(data){
    return request({
        url: serviceAreaName + '/AppVersion/Delete',
        method: 'post',
        data
    })
}
