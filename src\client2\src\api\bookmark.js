import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(params) {
    return request({
        url: serviceAreaName + '/Bookmark/GetListPage',
        method: 'get',
        params
    })
}

export function add(data){
    return request({
        url: serviceAreaName + '/Bookmark/Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/Bookmark/Edit',
        method: 'post',
        data
    })
}


export function del(data){
    return request({
        url: serviceAreaName + '/Bookmark/Delete',
        method: 'post',
        data
    })
}

export function detail(params){
    return request({
        url: serviceAreaName + '/Bookmark/GetDetails',
        method: 'get',
        params
    })
}

