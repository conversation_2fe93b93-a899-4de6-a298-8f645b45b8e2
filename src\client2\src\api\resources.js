import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.resource
export function getList(params) {
  return request({
    url: serviceAreaName + '/resources/load',
    method: 'get',
    params
  })
}

export function loadForRole(roleId) {
  return request({
    url: serviceAreaName + '/resources/loadForRole',
    method: 'get',
    params: { appId: '', firstId: roleId }
  })
}

export function add(data) {
  return request({
    url: serviceAreaName + '/resources/add',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: serviceAreaName + '/resources/update',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: serviceAreaName + '/resources/delete',
    method: 'post',
    data
  })
}

