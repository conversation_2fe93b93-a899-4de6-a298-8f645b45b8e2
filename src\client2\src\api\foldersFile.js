
import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + '/FoldersFile/'

export function getList(params) {
    return request({
        url: serviceAreaName + 'GetList',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}


export function editContent(data) {
    return request({
        url: serviceAreaName + 'EditContent',
        method: 'post',
        data
    })
}

export function move(data) {
    return request({
        url: serviceAreaName + 'Move',
        method: 'post',
        data
    })
}

export function getListPage(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
