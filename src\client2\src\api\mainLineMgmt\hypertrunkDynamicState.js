import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkDynamicState/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function revocation(data) {
    return request({
        url: serviceAreaName + 'Revocation',
        method: 'post',
        data
    })
}

export function setRead(data) {
    return request({
        url: serviceAreaName + 'Read',
        method: 'post',
        data
    })
}

export function getDailyFollowUp(data) {
    return request({
        url: serviceAreaName + 'GetDailyFollowUp',
        method: 'post',
        data
    })
}



