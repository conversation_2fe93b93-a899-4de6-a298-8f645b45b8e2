
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/ServiceEvaluation/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}


export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(datas) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        datas
    })
}

export function add(datas) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        datas
    })
}