import request from "@/utils/request";
import { serviceArea } from "../serviceArea";

const serviceAreaNameS = serviceArea.business + "/SalesActivitys/";
const serviceAreaNameH = serviceArea.business + "/HypertrunkTask/";

export function getListApi(data) {
  return request({
    url: serviceAreaNameS + "GetList",
    method: "post",
    data,
  });
}
export function addActivityApi(data) {
  return request({
    url: serviceAreaNameS + "Add",
    method: "post",
    data,
  });
}
export function getDetailApi(params) {
  return request({
    url: serviceAreaNameS + "GetDetail",
    method: "get",
    params,
  });
}
export function deleteApi(data) {
  return request({
    url: serviceAreaNameS + "Delete",
    method: "post",
    data,
  });
}
export function editApi(data) {
  return request({
    url: serviceAreaNameS + "Edit",
    method: "post",
    data,
  });
}
export function followApi(data) {
  return request({
    url: serviceAreaNameS + "Follow",
    method: "post",
    data,
  });
}
export function getSalesActivitysBoard(data) {
  return request({
    url: serviceAreaNameS + "GetSalesActivitysBoard",
    method: "post",
    data,
  });
}
export function getRelevantCustomers(params) {
  return request({
    url: serviceAreaNameS + "GetRelevantCustomers",
    method: "get",
    params,
  });
}
export function saveFollowRemind(data) {
  return request({
    url: serviceAreaNameS + "SaveFollowRemind",
    method: "post",
    data,
  });
}
// 任务列表
export function getListPageNew(data) {
  return request({
    url: serviceAreaNameH + "GetListPageNew",
    method: "post",
    data,
  });
}


export function getListPageData(data) {
  return request({
    url: serviceAreaNameS + "ListPageData",
    method: "post",
    data,
  });
}



