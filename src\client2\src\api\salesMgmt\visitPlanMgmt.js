import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/VisitRecordManagement/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function GetApprovalPrestore(params) {
    return request({
        url: serviceAreaName + 'GetApprovalPrestore',
        method: 'get',
        params
    })
}

export function createApproval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function followDetail(params) {
    return request({
        url: serviceAreaName + 'FollowUpDetail',
        method: 'get',
        params
    })
}

export function followUp(data) {
    return request({
        url: serviceAreaName + 'FollowUp',
        method: 'post',
        data
    })
}


export function getDataStatistics(data) {
    return request({
        url: serviceAreaName + 'DataStatistics',
        method: 'post',
        data
    })
}

