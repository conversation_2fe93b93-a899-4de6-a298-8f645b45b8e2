import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAppraName = serviceArea.business + '/AppraisePersonal'


export function getList(data) {
    return request({
        url: serviceAppraName + '/GetListPage',
        method: 'post',
        data
    })
}

//获取详情
export function detail(params) {
    return request({
        url: serviceAppraName + '/GetDetails',
        method: 'get',
        params
    })
}

//提交承诺或自我评定
export function edit(data) {
    return request({
        url: serviceAppraName + '/Edit',
        method: 'post',
        data
    })
}


//终止考核
export function stopCheck(data) {
    return request({
        url: serviceAppraName + '/StopCheck',
        method: 'post',
        data
    })
}

//继续考核
export function goonCheck(data) {
    return request({
        url: serviceAppraName + '/GoonCheck',
        method: 'post',
        data
    })
}

//重新提交
export function resubmit(data) {
    return request({
        url: serviceAppraName + '/Resubmit',
        method: 'post',
        data
    })
}
//申诉
export function assessmentAppeal(data) {
    return request({
        url: serviceAppraName + '/AssessmentAppeal',
        method: 'post',
        data
    })
}
//团队详情
export function getSelfRatingList(params) {
    return request({
        url: serviceAppraName + '/GetSelfRatingList',
        method: 'get',
        params
    })
}

//审批
export function approval(data) {
    return request({
        url: serviceAppraName + '/Approval',
        method: 'post',
        data
    })
}

export function getEmployeeTeamEvaluate(data) {
    return request({
        url: serviceAppraName + '/GetEmployeeTeamEvaluate',
        method: 'post',
        data
    })
}
