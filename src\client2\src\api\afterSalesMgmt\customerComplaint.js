import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/CustomerComplaint/'
export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data){
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function handlingResult(data) {
  return request({
      url: serviceAreaName + 'HandlingResult',
      method: 'post',
      data
  })
}

export function del(data){
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function detail(params){
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function createApproval(data){
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}


export function getServiceEmployee(params) {
  return request({
      url: serviceAreaName + 'GetServiceEmployee',
      method: 'get',
      params
  })
}
