<template>
    <el-button :type="btnType" v-bind="$attrs" v-on="$listeners">{{ btnText }}</el-button>
</template>

<script>
export default {
    name: 'app-button',
    props: {
        //1：确认；2：取消
        type: {
            type: String,
            default: 'primary'
        },
        text: {
            type: String,
            default: '确认'
        },
        /**
         * 1：确认
         * 2：取消
         */
        buttonType: {
            type: Number,
            default: 9999
        },
    },
    computed: {
        btnType() {
            if(this.buttonType == 1) {
                return 'primary'
            }else if(this.buttonType == 2) {
                return ''
            }else if(this.buttonType == 3) {
                return 'success'
            }
            return this.type
        },
        btnText() {
            if(this.buttonType == 1) {
                return '确认'
            }else if(this.buttonType == 2) {
                return '取消'
            }else if(this.buttonType == 3) {
                return '保存'
            }
            return this.text
        },
    },
    data() {
        return {

        }
    },
}
</script>