import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/ProjectConfigurationFile/'

export function getList(data) {
  return request({
    url: busServiceAreaName + 'GetListPage',
    method: 'post',
    data
  })
}

export function send(data) {
  return request({
    url: busServiceAreaName + 'Send',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: busServiceAreaName + 'Edit',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: busServiceAreaName + 'Delete',
    method: 'post',
    data
  })
}

export function detail(params) {
  return request({
    url: busServiceAreaName + 'GetDetails',
    method: 'get',
    params
  })
}

export function getRecordDetails(params) {
  return request({
    url: busServiceAreaName + 'GetRecordDetails',
    method: 'get',
    params
  })
}


export function getProductList(params) {
  return request({
    url: busServiceAreaName + 'GetProductList',
    method: 'get',
    params
  })
}

export function getProjectConfigManagementList(data) {
  return request({
    url: busServiceAreaName + 'GetProjectConfigManagementList',
    method: 'post',
    data
  })
}


export function settingReviewersEmployee(data) {
  return request({
    url: busServiceAreaName + 'SettingReviewersEmployee',
    method: 'post',
    data
  })
}


export function getSettingReviewersEmployee(params) {
  return request({
    url: busServiceAreaName + 'GetSettingReviewersEmployee',
    method: 'get',
    params
  })
}


export function review(data) {
  return request({
    url: busServiceAreaName + 'Review',
    method: 'post',
    data
  })
}


