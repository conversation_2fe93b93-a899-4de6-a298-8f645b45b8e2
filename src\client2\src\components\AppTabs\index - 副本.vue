<template>
    <div class="tab-wrapper">
        <div class="tab-item-wrapper">
            <div class="tab-item" :class="{'is-active': idx == currentIdx}" v-for="(item, idx) in items" :key="idx" @click="choiceTab(idx)">{{ item }}</div>
            <div class="tab-opt-wrapper">
                <div class="tab-opt">更多 <img src="../../assets/images/more.png" /></div>
            </div>
            <!-- <slot v-for="idx in items.length" :name="'tab_' + (idx + 1)" v-show="idx == currentIdx"></slot> -->

            <slot name="tab_1" v-show="1 == currentIdx"></slot>
            <slot name="tab_2" v-show="2 == currentIdx"></slot>
            <slot name="tab_3" v-show="3 == currentIdx"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'app-tabs',
    data() {
        return {
            currentIdx: 0,
            items: ['待审批', '已审批', '抄送我']

            
        }
    },
    methods: {
        choiceTab(idx) {
            this.currentIdx = idx
        }
    }
}
</script>

<style scoped>

.tab-wrapper, .tab-item, .tab-opt{
    height: 54px;
    line-height: 54px;
}

.tab-item-wrapper{
    position: relative;
}

.tab-wrapper{
    box-sizing: border-box;
    border-bottom: 1px solid #E4E7ED;
}
.tab-item{
    display: inline-block;
    margin: 0 15px;
    cursor: pointer;
    color: #9c9faf;
    font-size: 14px;
    transition: background-color .3s;
}

.tab-item.is-active{
    color: #409eff;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #409eff;
}

.tab-opt-wrapper{
    cursor: pointer;
    color: #409eff;
}

.tab-opt{
    position: absolute;
    display: inline-block;
    top: 0;
    right: 15px;
}

.tab-opt img{
    display: inline-block;
    vertical-align: middle;
    margin-top: -3px;
}



</style>
