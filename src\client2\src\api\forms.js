import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
//无分页
export function getList(params) {
  return request({
    url: serviceAreaName + '/Flow/FindForms',
    method: 'get',
    params
  })
}

//有分页
export function getList222(data) {
  return request({
    url: serviceAreaName + '/Flow/FindFormsByPage',
    method: 'post',
    data
  })
}

export function get(params) {
  return request({
    url: serviceAreaName + '/Flow/FindFormById',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: serviceAreaName + '/Flow/CreateForm',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: serviceAreaName + '/Flow/EditForm',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: serviceAreaName + '/Flow/DeleteFormsByIds',
    method: 'post',
    data
  })
}

