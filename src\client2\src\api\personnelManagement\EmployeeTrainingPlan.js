
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/EmployeeTrainingPlan/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
export function GetEmployeeList(data) {
    return request({
        url: serviceAreaName + 'GetEmployeeList',
        method: 'post',
        data
    })
}
export function GetEmployeeTrainingPlanList(data) {
    return request({
        url: serviceAreaName + 'GetEmployeeTrainingPlanList',
        method: 'post',
        data
    })
}
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function Positive(data) {
    return request({
        url: serviceAreaName + 'Positive',
        method: 'post',
        data
    })
}

export function Approval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}


export function getReportFeedbacks(params) {
    return request({
        url: serviceAreaName + 'GetReportFeedbacks',
        method: 'get',
        params
    })
}