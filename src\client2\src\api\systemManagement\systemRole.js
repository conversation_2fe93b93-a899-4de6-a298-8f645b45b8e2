
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/SystemRole/'

// export function getTreeList(params) {
//     return request({
//         url: serviceAreaName + 'GetTreeList',
//         method: 'get',
//         params
//     })
// }

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}
export function addAssociatedCustomers(data) {
  return request({
      url: serviceAreaName + 'AddAssociatedCustomers',
      method: 'post',
      data
  })
}
export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}
export function getEmployeeByCondition(data) {
  return request({
      url: serviceAreaName + 'GetEmployeeByCondition',
      method: 'post',
      data
  })
}
export function delEmployeeRoles(data) {
  return request({
      url: serviceAreaName + 'DeleteSystemEmployeeRoles',
      method: 'post',
      data
  })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}
export function getModules(params) {
    return request({
        url: serviceAreaName + 'GetModules',
        method: 'get',
        params
    })
}
export function getAppModule(params) {
    return request({
        url: serviceAreaName + 'GetAppModule',
        method: 'get',
        params
    })
}

export function getRoleByEmployeeId(params) {
    return request({
        url: serviceAreaName + 'GetRoleByEmployeeId',
        method: 'get',
        params
    })
}

export function getModulesByEmployeeId(params) {
    return request({
        url: serviceAreaName + 'GetModulesByEmployeeId',
        method: 'get',
        params
    })
}

export function getAppModulesByEmployeeId(params) {
    return request({
        url: serviceAreaName + 'GetAppModulesByEmployeeId',
        method: 'get',
        params
    })
}

