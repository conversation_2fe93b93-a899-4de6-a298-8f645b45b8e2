import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.user

export function assign(data) {
  var obj = data
  return request({
    url: serviceAreaName + '/AccessObjs/UnAssign',
    method: 'post',
    data: {
      type: obj.type,
      firstId: obj.firstId
    }
  }).then(function() {
    request({
      url: serviceAreaName + '/AccessObjs/Assign',
      method: 'post',
      data
    })
  })
}

export function assignForUser(data) {
  var obj = data
  return request({
    url: serviceAreaName + '/AccessObjs/UnUserRole',
    method: 'post',
    data: {
      type: obj.type,
      firstId: obj.firstId
    }
  }).then(function() {
    request({
      url: serviceAreaName + '/AccessObjs/UserRole',
      method: 'post',
      data
    })
  })
}

export function assignForElement(data) {
  var obj = data
  return request({
    url: serviceAreaName + '/AccessObjs/UnRoleElement',
    method: 'post',
    data: {
      firstId: obj.firstId
    }
  }).then(function() {
    request({
      url: serviceAreaName + '/AccessObjs/RoleElement',
      method: 'post',
      data
    })
  })
}

