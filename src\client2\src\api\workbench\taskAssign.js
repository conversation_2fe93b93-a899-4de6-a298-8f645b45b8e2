import request from "@/utils/request";
import { serviceArea } from "../serviceArea";

let serviceBusiness = serviceArea.business + "/AssignTask/";
let serviceUser = serviceArea.user + "/SystemEmployee/";

export function getListPage(data) {
  return request({
    url: serviceBusiness + "GetListPage",
    method: "post",
    data,
  });
}
export function addTaskApi(data) {
  return request({
    url: serviceBusiness + "AddTask",
    method: "post",
    data,
  });
}
export function ediTaskApi(data) {
  return request({
    url: serviceBusiness + "EdiTask",
    method: "post",
    data,
  });
}
export function getDetailApi(params) {
  return request({
    url: serviceBusiness + "GetDetails",
    method: "get",
    params,
  });
}
export function deleteApi(data) {
  return request({
    url: serviceBusiness + "Delete",
    method: "post",
    data,
  });
}
export function setAssignStateMainApi(params) {
  return request({
    url: serviceBusiness + "SetWithdrawTotal",
    method: "get",
    params,
  });
}
export function setAssignStateSubApi(params) {
  return request({
    url: serviceBusiness + "SetAssignState",
    method: "get",
    params,
  });
}
export function AssignManApi(params) {
  return request({
    url: serviceBusiness + "AssignMan",
    method: "get",
    params,
  });
}
// 获取人
export function getAllEmployees(data) {
  return request({
    url: serviceUser + "GetAllEmployees",
    method: "post",
    data,
  });
}

export function getStateDetailsApi(params) {
  return request({
    url: serviceBusiness + "GetStateDetails",
    method: "get",
    params,
  });
}

export function getRetAssignList(data) {
  return request({
    url: serviceArea.business + "/AssignQuestion/GetList",
    method: "post",
    data,
  });
}
export function questionAssignRefuseApi(params) {
  return request({
    url: serviceArea.business + "/AssignQuestion/Refuse",
    method: "get",
    params,
  });
}
export function questionAssignWithdrawApi(params) {
  return request({
    url: serviceArea.business + "/AssignQuestion/Withdraw",
    method: "get",
    params,
  });
}
export function GetOrganizerPageList(data) {
  return request({
    url: serviceArea.business + "/AssignReview/GetOrganizerPageList",
    method: "post",
    data,
  });
}
export function addTaskRetAssignApi(data) {
  return request({
    url: serviceArea.business + "/AssignReview/AddReview",
    method: "post",
    data,
  });
}
export function getActiveAssignTaskList(data) {
  return request({
    url: serviceArea.business + "/AssignReview/GetOrganizerSecendPageList",
    method: "post",
    data,
  });
}
export function getPassiveAssignTaskList(data) {
  return request({
    url: serviceArea.business + "/AssignReview/GetAssignerPageList",
    method: "post",
    data,
  });
}
export function getRetAssignDetail(params) {
  return request({
    url: serviceArea.business + "/AssignReview/GetDetail",
    method: "get",
    params,
  });
}
export function withdrawRetAssignApi(params) {
  return request({
    url: serviceArea.business + "/AssignReview/Withdraw",
    method: "get",
    params,
  });
}
export function getAssignerSecendPageList(data) {
  return request({
    url: serviceArea.business + "/AssignReview/GetAssignerSecendPageList",
    method: "post",
    data,
  });
}
export function issueAssignApi(params) {
  return request({
    url: serviceArea.business + "/AssignQuestion/AssignMan",
    method: "get",
    params,
  });
}
export function issueAssignDeleteApi(data) {
  return request({
    url: serviceArea.business + "/AssignQuestion/Delete",
    method: "post",
    data,
  });
}
export function relationTask(data) {
  return request({
    url: serviceArea.business + "/AssignQuestion/RelationTask",
    method: "post",
    data,
  })
}
export function getSubAssignListApi(params) {
  return request({
    url: serviceBusiness + "GetSubDetails",
    method: "get",
    params,
  });
}
