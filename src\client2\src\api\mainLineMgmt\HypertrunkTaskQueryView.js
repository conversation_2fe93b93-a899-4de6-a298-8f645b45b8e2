import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkTaskQueryView/'

export function getList(data) {
  return request({
    url: serviceAreaName + 'GetListPage',
    method: 'post',
    data
  })
}
export function add(data) {
  return request({
    url: serviceAreaName + 'Add',
    method: 'post',
    data
  })
}
export function edit(data) {
  return request({
    url: serviceAreaName + 'Edit',
    method: 'post',
    data
  })
}
export function del(data) {
  return request({
    url: serviceAreaName + 'Delete',
    method: 'post',
    data
  })
}
export function isUseApi(data) {
  return request({
    url: serviceAreaName + 'IsUse',
    method: 'post',
    data
  })
}
export function setOrderValueApi(data) {
  return request({
    url: serviceAreaName + 'SetOrderValue',
    method: 'post',
    data
  })
}

