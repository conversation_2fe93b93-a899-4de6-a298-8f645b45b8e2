import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkGuidepost/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function editGuidepostStatus(data) {
    return request({
        url: serviceAreaName + 'EditGuidepostStatus',
        method: 'post',
        data
    })
}

export function getTreeData(params) {
    return request({
        url: serviceAreaName + 'GetTreeData',
        method: 'get',
        params
    })
}

export function statisticalSubterm(data) {
    return request({
        url: serviceAreaName + 'StatisticalSubterm',
        method: 'post',
        data
    })
}

export function getRelationSchema(params) {
    return request({
        url: serviceAreaName + 'GetRelationSchema',
        method: 'get',
        params
    })
}

export function editRelationSchema(data) {
    return request({
        url: serviceAreaName + 'EditRelationSchema',
        method: 'post',
        data
    })
}






