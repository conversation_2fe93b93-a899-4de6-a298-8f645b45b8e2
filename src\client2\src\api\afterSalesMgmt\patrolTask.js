import request from '@/utils/request'
import { serviceArea } from '@/api/serviceArea'

let serviceAreaName = serviceArea.business + '/PatrolTask'

/**新增 */
export function add(data) {
    return request({
        url: serviceAreaName + '/Add',
        method: 'post',
        data
    })
}

/**新增集合 */
export function addList(data) {
    return request({
        url: serviceAreaName + '/AddList',
        method: 'post',
        data
    })
}

/**删除 */
export function del(data) {
    return request({
        url: serviceAreaName + '/Delete',
        method: 'post',
        data
    })
}

/**修改 */
export function edit(data) {
    return request({
        url: serviceAreaName + '/Edit',
        method: 'post',
        data
    })
}

/**获取详情 */
export function getDetails(params) {
    return request({
        url: serviceAreaName + '/GetDetails',
        method: 'get',
        params
    })
}

/**获取分页列表 */
export function getListPage(data) {
    return request({
        url: serviceAreaName + '/GetListPage',
        method: 'post',
        data
    })
}

/**获取条件查询所有内容 */
export function getListByCondition(data) {
    return request({
        url: serviceAreaName + '/GetListByCondition',
        method: 'post',
        data
    })
}

/**审批 */
export function approval(data) {
    return request({
        url: serviceAreaName + '/Approval',
        method: 'post',
        data
    })
}

/**设置为已报修 */
export function setIsRepair(params) {
    return request({
        url: serviceAreaName + '/SetIsRepair',
        method: 'get',
        params
    })
}
/**根据巡检ID获取报修设备列表 */
export function getEquipmentListByPatrolTaskId(params) {
    return request({
        url: serviceAreaName + '/GetEquipmentListByPatrolTaskId',
        method: 'get',
        params
    })
}
/**获取已分配任务的实施人员 */
export function getImplementerList() {
    return request({
        url: serviceAreaName + '/GetImplementerList',
        method: 'get'
    })
}

