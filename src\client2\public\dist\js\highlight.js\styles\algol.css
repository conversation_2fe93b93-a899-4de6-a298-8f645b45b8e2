/* Background */ .highlight-bg { background-color: #ffffff }
/* PreWrapper */ .highlight-chroma { background-color: #ffffff; }
/* Error */ .highlight-chroma .highlight-err {  }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .highlight-chroma .highlight-hl { background-color: #e5e5e5 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Line */ .highlight-chroma .highlight-line { display: flex; }
/* Keyword */ .highlight-chroma .highlight-k { font-weight: bold; text-decoration: underline }
/* KeywordConstant */ .highlight-chroma .highlight-kc { font-weight: bold; text-decoration: underline }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { font-weight: bold; font-style: italic; text-decoration: underline }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { font-weight: bold; text-decoration: underline }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { font-weight: bold; text-decoration: underline }
/* KeywordReserved */ .highlight-chroma .highlight-kr { font-weight: bold; text-decoration: underline }
/* KeywordType */ .highlight-chroma .highlight-kt { font-weight: bold; text-decoration: underline }
/* NameBuiltin */ .highlight-chroma .highlight-nb { font-weight: bold; font-style: italic }
/* NameBuiltinPseudo */ .highlight-chroma .highlight-bp { font-weight: bold; font-style: italic }
/* NameClass */ .highlight-chroma .highlight-nc { color: #666666; font-weight: bold; font-style: italic }
/* NameConstant */ .highlight-chroma .highlight-no { color: #666666; font-weight: bold; font-style: italic }
/* NameFunction */ .highlight-chroma .highlight-nf { color: #666666; font-weight: bold; font-style: italic }
/* NameNamespace */ .highlight-chroma .highlight-nn { color: #666666; font-weight: bold; font-style: italic }
/* NameVariable */ .highlight-chroma .highlight-nv { color: #666666; font-weight: bold; font-style: italic }
/* LiteralString */ .highlight-chroma .highlight-s { color: #666666; font-style: italic }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #666666; font-style: italic }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #666666; font-style: italic }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #666666; font-style: italic }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #666666; font-style: italic }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #666666; font-style: italic }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #666666; font-style: italic }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #666666; font-style: italic }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #666666; font-style: italic }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #666666; font-style: italic }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #666666; font-style: italic }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #666666; font-style: italic }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #666666; font-style: italic }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #666666; font-style: italic }
/* OperatorWord */ .highlight-chroma .highlight-ow { font-weight: bold }
/* Comment */ .highlight-chroma .highlight-c { color: #888888; font-style: italic }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #888888; font-style: italic }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #888888; font-style: italic }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #888888; font-style: italic }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #888888; font-weight: bold }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #888888; font-weight: bold }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #888888; font-weight: bold }
/*
Docco style used in http://jashkenas.github.com/docco/ converted by Simon Madine (@thingsinjars)
*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    color: #000;
    background: #f8f8ff;
}

.hljs-comment,
.hljs-quote {
    color: #408080;
    font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-subst {
    color: #954121;
}

.hljs-number {
    color: #40a070;
}

.hljs-string,
.hljs-doctag {
    color: #219161;
}

.hljs-selector-id,
.hljs-selector-class,
.hljs-section,
.hljs-type {
    color: #19469d;
}

.hljs-params {
    color: #00f;
}

.hljs-title {
    color: #458;
    font-weight: bold;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
    color: #000080;
    font-weight: normal;
}

.hljs-variable,
.hljs-template-variable {
    color: #008080;
}

.hljs-regexp,
.hljs-link {
    color: #b68;
}

.hljs-symbol,
.hljs-bullet {
    color: #990073;
}

.hljs-built_in,
.hljs-builtin-name {
    color: #0086b3;
}

.hljs-meta {
    color: #999;
    font-weight: bold;
}

.hljs-deletion {
    background: #fdd;
}

.hljs-addition {
    background: #dfd;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}

