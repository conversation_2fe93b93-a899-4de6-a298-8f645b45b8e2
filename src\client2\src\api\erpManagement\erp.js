import request from '@/utils/request'

import { serviceArea } from '../serviceArea'

let busServiceAreaName = serviceArea.business + '/ERPAccount/'

export function addAccount(data) {
    return request({
      url: busServiceAreaName + 'Add',
      method: 'post',
      data
    })
  }

  export function getAccountList(data) {
    return request({
      url: busServiceAreaName + 'GetListPage',
      method: 'post',
      data
    })
  }

  export function del(data){
    return request({
        url: busServiceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: busServiceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: busServiceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getCacheAccount(params) {
  return request({
      url: busServiceAreaName + 'GetRedis',
      method: 'get',
      params
  })
}


  
