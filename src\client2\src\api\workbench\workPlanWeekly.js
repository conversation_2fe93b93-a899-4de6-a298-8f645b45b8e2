import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/WorkPlanWeekly/'


//新增周报
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

// //获取上一次写日报的抄送人
// export function loadLast(params) {
//     return request({
//         url: serviceAreaName + 'LoadLast',
//         method: 'get',
//         params
//     })
// }

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}


export function detail(params) {
    return request({
        url: serviceAreaName + 'getDetails',
        method: 'get',
        params
    })
}

//获取上一次写日报的抄送人
export function loadLast(params) {
    return request({
        url: serviceAreaName + 'LoadLast',
        method: 'get',
        params
    })
}

export function referenceImportReportEmployee(params) {
    return request({
        url: serviceAreaName + 'ReferenceImportReportEmployee',
        method: 'get',
        params
    })
}
export function referenceImportReport(params) {
    return request({
        url: serviceAreaName + 'ReferenceImportReport',
        method: 'get',
        params
    })
}

export function addReport(data) {
    return request({
        url: serviceArea.business + '/WorkPlanReport/Add',
        method: 'post',
        data
    })
}
 
export function loadLastReport(params) {
    return request({
        url: serviceArea.business + '/WorkPlanReport/LoadLast',
        method: 'get',
        params
    })
}

export function getReportDetails(params) {
    return request({
        url: serviceArea.business + '/WorkPlanReport/getDetails',
        method: 'get',
        params
    })
}

