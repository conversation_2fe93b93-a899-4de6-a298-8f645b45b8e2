import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + '/CheckList/'

//新增
export function add(data) {
    return request({
        url: serviceAreaName + 'AddTag',
        method: 'post',
        data
    })
}

//编辑
export function edit(data) {
    return request({
        url: serviceAreaName + 'EditTag',
        method: 'post',
        data
    })
}

//删除
export function del(data) {
    return request({
        url: serviceAreaName + 'DeleteTag',
        method: 'post',
        data
    })
}
//分页列表
export function getListPage(data) {
    return request({
        url: serviceAreaName + 'GetTagList',
        method: 'post',
        data
    })
}

export function getTagLeafList(data) {
    return request({
        url: serviceAreaName + 'GetTagLeafList',
        method: 'post',
        data
    })
}

export function editLeafTag(data) {
    return request({
        url: serviceAreaName + 'EditLeafTag',
        method: 'post',
        data
    })
}



export function editSortTag(data) {
    return request({
        url: serviceAreaName + 'EditSortTag',
        method: 'post',
        data
    })
}



export function getTagListByStencilId(params) {
    return request({
        url: serviceAreaName + 'GetTagListByStencilId',
        method: 'get',
        params
    })
}

export function getCheckListToMakeGrade(params) {
    return request({
        url: serviceAreaName + 'GetCheckListToMakeGrade',
        method: 'get',
        params
    })
}

export function getCheckListDetail(params) {
    return request({
        url: serviceAreaName + 'GetCheckListDetail',
        method: 'get',
        params
    })
}






