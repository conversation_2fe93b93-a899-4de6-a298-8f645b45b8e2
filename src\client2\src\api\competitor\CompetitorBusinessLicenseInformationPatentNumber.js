
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/CompetitorBusinessLicenseInformationPatentNumber/'
export function editList(data) {
    return request({
        url: serviceAreaName + 'EditList',
        method: 'post',
        data
    })
}

export function getDetails(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getChart(params) {
    return request({
        url: serviceAreaName + 'GetChart',
        method: 'get',
        params
    })
}

