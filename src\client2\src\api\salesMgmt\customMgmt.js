import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/Customer/'

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function getLastVisitTime(params) {
    return request({
        url: serviceAreaName + 'GetLastVisitTime',
        method: 'get',
        params
    })
}


export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function assign(data) {
    return request({
        url: serviceAreaName + 'Assign',
        method: 'post',
        data
    })
}

export function getSalesman(params) {
    return request({
        url: serviceAreaName + 'GetSalesman',
        method: 'get',
        params
    })
}

export function getStatisticsChart(params) {
    return request({
        url: serviceAreaName + 'GetStatisticsChart',
        method: 'get',
        params
    })
}

export function getAnalyzeDetails(params) {
    return request({
        url: serviceAreaName + 'GetAnalyzeDetails',
        method: 'get',
        params
    })
}

export function editCustomerAnalyze(data) {
    return request({
        url: serviceAreaName + 'EditCustomerAnalyze',
        method: 'post',
        data
    })
}

export function editStatus(data) {
    return request({
        url: serviceAreaName + 'EditStatus',
        method: 'post',
        data
    })
}


export function editFriendliness(data) {
    return request({
        url: serviceAreaName + 'EditFriendliness',
        method: 'post',
        data
    })
}

export function getFollowUpRecords(params) {
    return request({
        url: serviceAreaName + 'GetFollowUpRecords',
        method: 'get',
        params
    })
} 

export function getVisitList(data) {
    return request({
        url: serviceAreaName + 'GetVisitList',
        method: 'post',
        data
    })
} 

export function deleteVisit(data) {
    return request({
        url: serviceAreaName + 'DeleteVisit',
        method: 'post',
        data
    })
}

export function editVisit(data) {
    return request({
        url: serviceAreaName + 'EditVisit',
        method: 'post',
        data
    })
}


export function detailVisit(params) {
    return request({
        url: serviceAreaName + 'DetailVisit',
        method: 'get',
        params
    })
} 

export function getRelatedList(data) {
    return request({
        url: serviceAreaName + 'GetRelatedList',
        method: 'post',
        data
    })
} 

export function deleteRelated(data) {
    return request({
        url: serviceAreaName + 'DeleteRelated',
        method: 'post',
        data
    })
}
export function editCustomerRelated(data) {
    return request({
        url: serviceAreaName + 'EditCustomerRelated',
        method: 'post',
        data
    })
}


export function getCostRecordList(data) {
    return request({
        url: serviceAreaName + 'GetCostRecordList',
        method: 'post',
        data
    })
} 

export function deleteCostRecord(data) {
    return request({
        url: serviceAreaName + 'DeleteCostRecord',
        method: 'post',
        data
    })
}

export function editCostRecord(data) {
    return request({
        url: serviceAreaName + 'EditCostRecord',
        method: 'post',
        data
    })
}


export function getCustomerOrderRelationList(data) {
    return request({
        url: serviceAreaName + 'GetCustomerOrderRelationList',
        method: 'post',
        data
    })
} 

export function deleterOrderRelation(data) {
    return request({
        url: serviceAreaName + 'DeleterOrderRelation',
        method: 'post',
        data
    })
}

export function editOrderRelationAsync(data) {
    return request({
        url: serviceAreaName + 'EditOrderRelationAsync',
        method: 'post',
        data
    })
}

export function getCustomerBusinessRelationList(data) {
    return request({
        url: serviceAreaName + 'GetCustomerBusinessRelationList',
        method: 'post',
        data
    })    
}

export function editBusinessOpportunityRelation(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityRelation',
        method: 'post',
        data
    })    
}

export function deleteBusinessOpportunityRelation(data) {
    return request({
        url: serviceAreaName + 'DeleteBusinessOpportunityRelation',
        method: 'post',
        data
    })    
}

export function getCustomerBidRelationList(data) {
    return request({
        url: serviceAreaName + 'GetCustomerBidRelationList',
        method: 'post',
        data
    })    
}


export function deleteCustomerBidRelationList(data) {
    return request({
        url: serviceAreaName + 'DeleteCustomerBidRelationList',
        method: 'post',
        data
    })    
}

export function editBidRelation(data) {
    return request({
        url: serviceAreaName + 'EditBidRelation',
        method: 'post',
        data
    })    
}

export function getStateStatistics(params) {
    return request({
        url: serviceAreaName + 'GetStateStatistics',
        method: 'get',
        params
    })
} 

export function addRelationPersonCustomer(data) {
    return request({
        url: serviceAreaName + 'AddRelationPersonCustomer',
        method: 'post',
        data
    })    
}

export function editRelated(data) {
    return request({
        url: serviceAreaName + 'EditRelated',
        method: 'post',
        data
    })    
}

export function uploadAttachment(data) {
    return request({
        url: serviceAreaName + 'UploadAttachment',
        method: 'post',
        data
    })    
}

export function setFocus(data) {
    return request({
        url: serviceAreaName + 'SetFocus',
        method: 'post',
        data
    })    
}

export function relationshipDiagram(params) {
    return request({
        url: serviceAreaName + 'RelationshipDiagram',
        method: 'get',
        params
    })
} 

export function getTags(params) {
    return request({
        url: serviceAreaName + 'GetTags',
        method: 'get',
        params
    })
} 

export function editTag(data) {
    return request({
        url: serviceAreaName + 'EditTag',
        method: 'post',
        data
    })    
}

export function editVisitAndSchedule(data) {
    return request({
        url: serviceAreaName + 'EditVisitAndSchedule',
        method: 'post',
        data
    })    
}

export function getRelationCustomList(params) {
    return request({
        url: serviceAreaName + 'GetRelationCustomList',
        method: 'get',
        params
    })
} 
