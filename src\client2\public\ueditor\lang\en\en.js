/**
 * Created with JetBrains PhpStorm.
 * User: taoqili
 * Date: 12-6-12
 * Time: 下午6:57
 * To change this template use File | Settings | File Templates.
 */
UE.I18N['en'] = {
    'labelMap': {
        'anchor': 'Anchor', 'undo': 'Undo', 'redo': 'Redo', 'bold': 'Bold', 'indent': 'Indent', 'snapscreen': 'SnapScreen',
        'italic': 'Italic', 'underline': 'Underline', 'strikethrough': 'Strikethrough', 'subscript': 'SubScript', 'fontborder': 'text border',
        'superscript': 'SuperScript', 'formatmatch': 'Format Match', 'source': 'Source', 'blockquote': 'BlockQuote',
        'pasteplain': 'PastePlain', 'selectall': 'SelectAll', 'print': 'Print', 'preview': 'Preview',
        'horizontal': 'Horizontal', 'removeformat': 'RemoveFormat', 'time': 'Time', 'date': 'Date',
        'unlink': 'Unlink', 'insertrow': 'InsertRow', 'insertcol': 'InsertCol', 'mergeright': 'MergeRight', 'mergedown': 'MergeDown',
        'deleterow': 'DeleteRow', 'deletecol': 'DeleteCol', 'splittorows': 'SplitToRows', 'insertcode': 'insert code',
        'splittocols': 'SplitToCols', 'splittocells': 'SplitToCells', 'deletecaption': 'DeleteCaption', 'inserttitle': 'InsertTitle',
        'mergecells': 'MergeCells', 'deletetable': 'DeleteTable', 'cleardoc': 'Clear', 'insertparagraphbeforetable': "InsertParagraphBeforeTable",
        'fontfamily': 'FontFamily', 'fontsize': 'FontSize', 'paragraph': 'Paragraph', 'simpleupload': 'Single Image', 'insertimage': 'Multi Image', 'edittable': 'Edit Table', 'edittd': 'Edit Td', 'link': 'Link',
        'emotion': 'Emotion', 'spechars': 'Spechars', 'searchreplace': 'SearchReplace', 'map': 'BaiduMap', 'gmap': 'GoogleMap',
        'insertvideo': 'Video', 'help': 'Help', 'justifyleft': 'JustifyLeft', 'justifyright': 'JustifyRight', 'justifycenter': 'JustifyCenter',
        'justifyjustify': 'Justify', 'forecolor': 'FontColor', 'backcolor': 'BackColor', 'insertorderedlist': 'OL',
        'insertunorderedlist': 'UL', 'fullscreen': 'FullScreen', 'directionalityltr': 'EnterFromLeft', 'directionalityrtl': 'EnterFromRight',
        'rowspacingtop': 'RowSpacingTop', 'rowspacingbottom': 'RowSpacingBottom', 'pagebreak': 'PageBreak', 'insertframe': 'Iframe', 'imagenone': 'Default',
        'imageleft': 'ImageLeft', 'imageright': 'ImageRight', 'attachment': 'Attachment', 'imagecenter': 'ImageCenter', 'wordimage': 'WordImage',
        'lineheight': 'LineHeight', 'edittip': 'EditTip', 'customstyle': 'CustomStyle', 'scrawl': 'Scrawl', 'autotypeset': 'AutoTypeset',
        'webapp': 'WebAPP', 'touppercase': 'UpperCase', 'tolowercase': 'LowerCase', 'template': 'Template', 'background': 'Background', 'inserttable': 'InsertTable',
        'music': 'Music', 'charts': 'charts', 'drafts': 'Load from Drafts'
    },
    'insertorderedlist': {
        'num': '1,2,3...',
        'num1': '1),2),3)...',
        'num2': '(1),(2),(3)...',
        'cn': '一,二,三....',
        'cn1': '一),二),三)....',
        'cn2': '(一),(二),(三)....',
        'decimal': '1,2,3...',
        'lower-alpha': 'a,b,c...',
        'lower-roman': 'i,ii,iii...',
        'upper-alpha': 'A,B,C...',
        'upper-roman': 'I,II,III...'
    },
    'insertunorderedlist': {
        'circle': '○ Circle',
        'disc': '● Circle dot',
        'square': '■ Rectangle ',
        'dash': '－ Dash',
        'dot': '。dot'
    },
    'paragraph': { 'p': 'Paragraph', 'h1': 'Title 1', 'h2': 'Title 2', 'h3': 'Title 3', 'h4': 'Title 4', 'h5': 'Title 5', 'h6': 'Title 6' },
    'fontfamily': {
        'songti': 'Sim Sun',
        'kaiti': 'Sim Kai',
        'heiti': 'Sim Hei',
        'lishu': 'Sim Li',
        'yahei': 'Microsoft YaHei',
        'andaleMono': 'Andale Mono',
        'arial': 'Arial',
        'arialBlack': 'Arial Black',
        'comicSansMs': 'Comic Sans MS',
        'impact': 'Impact',
        'timesNewRoman': 'Times New Roman'
    },
    'customstyle': {
        'tc': 'Title center',
        'tl': 'Title left',
        'im': 'Important',
        'hi': 'Highlight'
    },
    'autoupload': {
        'exceedSizeError': 'File Size Exceed',
        'exceedTypeError': 'File Type Not Allow',
        'jsonEncodeError': 'Server Return Format Error',
        'loading': "loading...",
        'loadError': "load error",
        'errorLoadConfig': 'Server config not loaded, upload can not work.',
    },
    'simpleupload': {
        'exceedSizeError': 'File Size Exceed',
        'exceedTypeError': 'File Type Not Allow',
        'jsonEncodeError': 'Server Return Format Error',
        'loading': "loading...",
        'loadError': "load error",
        'errorLoadConfig': 'Server config not loaded, upload can not work.',
    },
    'elementPathTip': "Path",
    'wordCountTip': "Word Count",
    'wordCountMsg': '{#count} characters entered,{#leave} left. ',
    'wordOverFlowMsg': '<span style="color:red;">The number of characters has exceeded allowable maximum values, the server may refuse to save!</span>',
    'ok': "OK",
    'cancel': "Cancel",
    'closeDialog': "closeDialog",
    'tableDrag': "You must import the file uiUtils.js before drag! ",
    'autofloatMsg': "The plugin AutoFloat depends on EditorUI!",
    'loadconfigError': 'Get server config error.',
    'loadconfigFormatError': 'Server config format error.',
    'loadconfigHttpError': 'Get server config http error.',
    'snapScreen_plugin': {
        'browserMsg': "Only IE supported!",
        'callBackErrorMsg': "The callback data is wrong,please check the config!",
        'uploadErrorMsg': "Upload error,please check your server environment! "
    },
    'insertcode': {
        'as3': 'ActionScript 3',
        'bash': 'Bash/Shell',
        'cpp': 'C/C++',
        'css': 'CSS',
        'cf': 'ColdFusion',
        'c#': 'C#',
        'delphi': 'Delphi',
        'diff': 'Diff',
        'erlang': 'Erlang',
        'groovy': 'Groovy',
        'html': 'HTML',
        'java': 'Java',
        'jfx': 'JavaFX',
        'js': 'JavaScript',
        'pl': 'Perl',
        'php': 'PHP',
        'plain': 'Plain Text',
        'ps': 'PowerShell',
        'python': 'Python',
        'ruby': 'Ruby',
        'scala': 'Scala',
        'sql': 'SQL',
        'vb': 'Visual Basic',
        'xml': 'XML'
    },
    'confirmClear': "Do you confirm to clear the Document?",
    'contextMenu': {
        'delete': "Delete",
        'selectall': "Select all",
        'deletecode': "Delete Code",
        'cleardoc': "Clear Document",
        'confirmclear': "Do you confirm to clear the Document?",
        'unlink': "Unlink",
        'paragraph': "Paragraph",
        'edittable': "Table property",
        'aligncell': 'Align cell',
        'aligntable': 'Table alignment',
        'tableleft': 'Left float',
        'tablecenter': 'Center',
        'tableright': 'Right float',
        'aligntd': 'Cell alignment',
        'edittd': "Cell property",
        'setbordervisible': 'set table edge visible',
        'table': "Table",
        'justifyleft': 'Justify Left',
        'justifyright': 'Justify Right',
        'justifycenter': 'Justify Center',
        'justifyjustify': 'Default',
        'deletetable': "Delete table",
        'insertparagraphbefore': "InsertedBeforeLine",
        'insertparagraphafter': 'InsertedAfterLine',
        'inserttable': 'Insert table',
        'insertcaption': 'Insert caption',
        'deletecaption': 'Delete Caption',
        'inserttitle': 'Insert Title',
        'deletetitle': 'Delete Title',
        'inserttitlecol': 'Insert Title Col',
        'deletetitlecol': 'Delete Title Col',
        'averageDiseRow': 'AverageDise Row',
        'averageDisCol': 'AverageDis Col',
        'deleterow': "Delete row",
        'deletecol': "Delete col",
        'insertrow': "Insert row",
        'insertcol': "Insert col",
        'insertrownext': 'Insert Row Next',
        'insertcolnext': 'Insert Col Next',
        'mergeright': "Merge right",
        'mergeleft': "Merge left",
        'mergedown': "Merge down",
        'mergecells': "Merge cells",
        'splittocells': "Split to cells",
        'splittocols': "Split to Cols",
        'splittorows': "Split to Rows",
        'tablesort': 'Table sorting',
        'enablesort': 'Sorting Enable',
        'disablesort': 'Sorting Disable',
        'reversecurrent': 'Reverse current',
        'orderbyasc': 'Order By ASCII',
        'reversebyasc': 'Reverse By ASCII',
        'orderbynum': 'Order By Num',
        'reversebynum': 'Reverse By Num',
        'borderbk': 'Border shading',
        'setcolor': 'interlaced color',
        'unsetcolor': 'Cancel interlacedcolor',
        'setbackground': 'Background interlaced',
        'unsetbackground': 'Cancel Bk interlaced',
        'redandblue': 'Blue and red',
        'threecolorgradient': 'Three-color gradient',
        'copy': "Copy(Ctrl + c)",
        'copymsg': "Browser does not support. Please use 'Ctrl + c' instead!",
        'paste': "Paste(Ctrl + v)",
        'pastemsg': "Browser does not support. Please use 'Ctrl + v' instead!"
    },
    'copymsg': "Browser does not support. Please use 'Ctrl + c' instead!",
    'pastemsg': "Browser does not support. Please use 'Ctrl + v' instead!",
    'anthorMsg': "Link",
    'clearColor': 'Clear',
    'standardColor': 'Standard color',
    'themeColor': 'Theme color',
    'property': 'Property',
    'default': 'Default',
    'modify': 'Modify',
    'justifyleft': 'Justify Left',
    'justifyright': 'Justify Right',
    'justifycenter': 'Justify Center',
    'justify': 'Default',
    'clear': 'Clear',
    'anchorMsg': 'Anchor',
    'delete': 'Delete',
    'clickToUpload': "Click to upload",
    'unset': 'Language hasn\'t been set!',
    't_row': 'row',
    't_col': 'col',
    'pasteOpt': 'Paste Option',
    'pasteSourceFormat': "Keep Source Formatting",
    'tagFormat': 'Keep tag',
    'pasteTextFormat': 'Keep Text only',
    'more': 'More',
    'autoTypeSet': {
        'mergeLine': "Merge empty line",
        'delLine': "Del empty line",
        'removeFormat': "Remove format",
        'indent': "Indent",
        'alignment': "Alignment",
        'imageFloat': "Image float",
        'removeFontsize': "Remove font size",
        'removeFontFamily': "Remove fontFamily",
        'removeHtml': "Remove redundant HTML code",
        'pasteFilter': "Paste filter",
        'run': "Done",
        'symbol': 'Symbol Conversion',
        'bdc2sb': 'Full-width to Half-width',
        'tobdc': 'Half-width to Full-width'
    },

    'background': {
        'static': {
            'lang_background_normal': 'Normal',
            'lang_background_local': 'Online',
            'lang_background_set': 'Background Set',
            'lang_background_none': 'No Background',
            'lang_background_colored': 'Colored Background',
            'lang_background_color': 'Color Set',
            'lang_background_netimg': 'Net-Image',
            'lang_background_align': 'Align Type',
            'lang_background_position': 'Position',
            'repeatType': { 'options': ["Center", "Repeat-x", "Repeat-y", "Tile", "Custom"] }
        },
        'noUploadImage': "No pictures has been uploaded！",
        'toggleSelect': 'Change the active state by click!\n Image Size: '
    },
    //===============dialog i18N=======================
    'insertimage': {
        'static': {
            'lang_tab_remote': "Insert",
            'lang_tab_upload': "Local",
            'lang_tab_online': "Manager",
            'lang_tab_search': "Search",
            'lang_input_url': "Address:",
            'lang_input_size': "Size:",
            'lang_input_width': "Width",
            'lang_input_height': "Height",
            'lang_input_border': "Border:",
            'lang_input_vhspace': "Margins:",
            'lang_input_title': "Title:",
            'lang_input_align': 'Image Float Style:',
            'lang_imgLoading': "Loading...",
            'lang_start_upload': "Start Upload",
            'lock': { 'title': "Lock rate" },
            'searchType': { 'title': "ImageType", 'options': ["News", "Wallpaper", "emotions", "photo"] },
            'searchTxt': { 'value': "Enter the search keyword!" },
            'searchBtn': { 'value': "Search" },
            'searchReset': { 'value': "Clear" },
            'noneAlign': { 'title': 'None Float' },
            'leftAlign': { 'title': 'Left Float' },
            'rightAlign': { 'title': 'Right Float' },
            'centerAlign': { 'title': 'Center In A Line' }
        },
        'uploadSelectFile': 'Select File',
        'uploadAddFile': 'Add File',
        'uploadStart': 'Start Upload',
        'uploadPause': 'Pause Upload',
        'uploadContinue': 'Continue Upload',
        'uploadRetry': 'Retry Upload',
        'uploadDelete': 'Delete',
        'uploadTurnLeft': 'Turn Left',
        'uploadTurnRight': 'Turn Right',
        'uploadPreview': 'Doing Preview',
        'uploadNoPreview': 'Can Not Preview',
        'updateStatusReady': 'Selected _ pictures, total _KB.',
        'updateStatusConfirm': '_ uploaded successfully and _ upload failed',
        'updateStatusFinish': 'Total _ pictures (_KB), _  uploaded successfully',
        'updateStatusError': ' and _ upload failed',
        'errorNotSupport': 'WebUploader does not support the browser you are using. Please upgrade your browser or flash player',
        'errorLoadConfig': 'Server config not loaded, upload can not work.',
        'errorExceedSize': 'File Size Exceed',
        'errorFileType': 'File Type Not Allow',
        'errorInterrupt': 'File Upload Interrupted',
        'errorUploadRetry': 'Upload Error, Please Retry.',
        'errorHttp': 'Http Error',
        'errorServerUpload': 'Server Result Error.',
        'remoteLockError': "Cannot Lock the Proportion between width and height",
        'numError': "Please enter the correct Num. e.g 123,400",
        'imageUrlError': "The image format may be wrong!",
        'imageLoadError': "Error,please check the network or URL！",
        'searchRemind': "Enter the search keyword!",
        'searchLoading': "Image is loading,please wait...",
        'searchRetry': " Sorry,can't find the image,please try again!"
    },
    'attachment': {
        'static': {
            'lang_tab_upload': 'Upload',
            'lang_tab_online': 'Online',
            'lang_start_upload': "Start upload",
            'lang_drop_remind': "You can drop files here, a single maximum of 300 files"
        },
        'uploadSelectFile': 'Select File',
        'uploadAddFile': 'Add File',
        'uploadStart': 'Start Upload',
        'uploadPause': 'Pause Upload',
        'uploadContinue': 'Continue Upload',
        'uploadRetry': 'Retry Upload',
        'uploadDelete': 'Delete',
        'uploadTurnLeft': 'Turn Left',
        'uploadTurnRight': 'Turn Right',
        'uploadPreview': 'Doing Preview',
        'updateStatusReady': 'Selected _ files, total _KB.',
        'updateStatusConfirm': '_ uploaded successfully and _ upload failed',
        'updateStatusFinish': 'Total _ files (_KB), _  uploaded successfully',
        'updateStatusError': ' and _ upload failed',
        'errorNotSupport': 'WebUploader does not support the browser you are using. Please upgrade your browser or flash player',
        'errorLoadConfig': 'Server config not loaded, upload can not work.',
        'errorExceedSize': 'File Size Exceed',
        'errorFileType': 'File Type Not Allow',
        'errorInterrupt': 'File Upload Interrupted',
        'errorUploadRetry': 'Upload Error, Please Retry.',
        'errorHttp': 'Http Error',
        'errorServerUpload': 'Server Result Error.'
    },

    'insertvideo': {
        'static': {
            'lang_tab_insertV': "Video",
            'lang_tab_searchV': "Search",
            'lang_tab_uploadV': "Upload",
            'lang_video_url': " URL ",
            'lang_video_size': "Video Size",
            'lang_videoW': "Width",
            'lang_videoH': "Height",
            'lang_alignment': "Alignment",
            'videoSearchTxt': { 'value': "Enter the search keyword!" },
            'videoType': { 'options': ["All", "Hot", "Entertainment", "Funny", "Sports", "Science", "variety"] },
            'videoSearchBtn': { 'value': "Search in Baidu" },
            'videoSearchReset': { 'value': "Clear result" },

            'lang_input_fileStatus': ' No file uploaded!',
            'startUpload': { 'style': "background:url(upload.png) no-repeat;" },

            'lang_upload_size': "Video Size",
            'lang_upload_width': "Width",
            'lang_upload_height': "Height",
            'lang_upload_alignment': "Alignment",
            'lang_format_advice': "Recommends mp4 format."
        },
        'numError': "Please enter the correct Num. e.g 123,400",
        'floatLeft': "Float left",
        'floatRight': "Float right",
        'default': "Default",
        'block': "Display in block",
        'urlError': "The video url format may be wrong!",
        'loading': " &nbsp;The video is loading, please wait…",
        'clickToSelect': "Click to select",
        'goToSource': 'Visit source video ',
        'noVideo': " &nbsp; &nbsp;Sorry,can't find the video,please try again!",

        'browseFiles': 'Open files',
        'uploadSuccess': 'Upload Successful!',
        'delSuccessFile': 'Remove from the success of the queue',
        'delFailSaveFile': 'Remove the save failed file',
        'statusPrompt': ' file(s) uploaded! ',
        'flashVersionError': 'The current Flash version is too low, please update FlashPlayer,then try again!',
        'flashLoadingError': 'The Flash failed loading! Please check the path or network state',
        'fileUploadReady': 'Wait for uploading...',
        'delUploadQueue': 'Remove from the uploading queue ',
        'limitPrompt1': 'Can not choose more than single',
        'limitPrompt2': 'file(s)！Please choose again！',
        'delFailFile': 'Remove failure file',
        'fileSizeLimit': 'File size exceeds the limit！',
        'emptyFile': 'Can not upload an empty file！',
        'fileTypeError': 'File type error！',
        'unknownError': 'Unknown error！',
        'fileUploading': 'Uploading,please wait...',
        'cancelUpload': 'Cancel upload',
        'netError': 'Network error',
        'failUpload': 'Upload failed',
        'serverIOError': 'Server IO error！',
        'noAuthority': 'No Permission！',
        'fileNumLimit': 'Upload limit to the number',
        'failCheck': 'Authentication fails, the upload is skipped!',
        'fileCanceling': 'Cancel, please wait...',
        'stopUploading': 'Upload has stopped...',

        'uploadSelectFile': 'Select File',
        'uploadAddFile': 'Add File',
        'uploadStart': 'Start Upload',
        'uploadPause': 'Pause Upload',
        'uploadContinue': 'Continue Upload',
        'uploadRetry': 'Retry Upload',
        'uploadDelete': 'Delete',
        'uploadTurnLeft': 'Turn Left',
        'uploadTurnRight': 'Turn Right',
        'uploadPreview': 'Doing Preview',
        'updateStatusReady': 'Selected _ files, total _KB.',
        'updateStatusConfirm': '_ uploaded successfully and _ upload failed',
        'updateStatusFinish': 'Total _ files (_KB), _  uploaded successfully',
        'updateStatusError': ' and _ upload failed',
        'errorNotSupport': 'WebUploader does not support the browser you are using. Please upgrade your browser or flash player',
        'errorLoadConfig': 'Server config not loaded, upload can not work.',
        'errorExceedSize': 'File Size Exceed',
        'errorFileType': 'File Type Not Allow',
        'errorInterrupt': 'File Upload Interrupted',
        'errorUploadRetry': 'Upload Error, Please Retry.',
        'errorHttp': 'Http Error',
        'errorServerUpload': 'Server Result Error.'
    },
    'webapp': {
        'tip1': "This function provided by Baidu APP,please apply for baidu APPKey webmaster first!",
        'tip2': "And then open the file ueditor.config.js to set it! ",
        'applyFor': "APPLY FOR",
        'anthorApi': "Baidu API"
    },
    'template': {
        'static': {
            'lang_template_bkcolor': 'Background Color',
            'lang_template_clear': 'Keep Content',
            'lang_template_select': 'Select Template'
        },
        'blank': "Blank",
        'blog': "Blog",
        'resume': "Resume",
        'richText': "Rich Text",
        'scrPapers': "Scientific Papers"
    },
    scrawl: {
        'static': {
            'lang_input_previousStep': "Previous",
            'lang_input_nextsStep': "Next",
            'lang_input_clear': 'Clear',
            'lang_input_addPic': 'AddImage',
            'lang_input_ScalePic': 'ScaleImage',
            'lang_input_removePic': 'RemoveImage',
            'J_imgTxt': { title: 'Add background image' }
        },
        'noScarwl': "No paint, a white paper...",
        'scrawlUpLoading': "Image is uploading, please wait...",
        'continueBtn': "Try again",
        'imageError': "Image failed to load!",
        'backgroundUploading': 'Image is uploading,please wait...'
    },
    'music': {
        'static': {
            'lang_input_tips': "Input singer/song/album, search you interested in music!",
            'J_searchBtn': { value: 'Search songs' }
        },
        'emptyTxt': 'Not search to the relevant music results, please change a keyword try.',
        'chapter': 'Songs',
        'singer': 'Singer',
        'special': 'Album',
        'listenTest': 'Audition'
    },
    anchor: {
        'static': {
            'lang_input_anchorName': 'Anchor Name:'
        }
    },
    'charts': {
        'static': {
            'lang_data_source': 'Data source:',
            'lang_chart_format': 'Chart format:',
            'lang_data_align': 'Align',
            'lang_chart_align_same': 'Consistent with the X-axis Y-axis',
            'lang_chart_align_reverse': 'X-axis Y-axis opposite',
            'lang_chart_title': 'Title',
            'lang_chart_main_title': 'main title:',
            'lang_chart_sub_title': 'sub title:',
            'lang_chart_x_title': 'X-axis title:',
            'lang_chart_y_title': 'Y-axis title:',
            'lang_chart_tip': 'Prompt',
            'lang_cahrt_tip_prefix': 'prefix:',
            'lang_cahrt_tip_description': '仅饼图有效， 当鼠标移动到饼图中相应的块上时，提示框内的文字的前缀',
            'lang_chart_data_unit': 'Unit',
            'lang_chart_data_unit_title': 'unit:',
            'lang_chart_data_unit_description': '显示在每个数据点上的数据的单位， 比如： 温度的单位 ℃',
            'lang_chart_type': 'Chart type:',
            'lang_prev_btn': 'Previous',
            'lang_next_btn': 'Next'
        }
    },
    emotion: {
        'static': {
            'lang_input_choice': 'Choice',
            'lang_input_Tuzki': 'Tuzki',
            'lang_input_lvdouwa': 'LvDouWa',
            'lang_input_BOBO': 'BOBO',
            'lang_input_babyCat': 'BabyCat',
            'lang_input_bubble': 'Bubble',
            'lang_input_youa': 'YouA'
        }
    },
    gmap: {
        'static': {
            'lang_input_address': 'Address:',
            'lang_input_search': 'Search',
            'address': { value: "Beijing" }
        },
        searchError: 'Unable to locate the address!'
    },
    help: {
        'static': {
            'lang_input_about': 'About',
            'lang_input_shortcuts': 'Shortcuts',
            'lang_input_introduction': "UEditor is developed by Baidu Co.ltd.  It is lightweight, customizable , focusing on user experience and etc. , UEditor is based on open source BSD license , allowing free use and redistribution.",
            'lang_Txt_shortcuts': 'Shortcuts',
            'lang_Txt_func': 'Function',
            'lang_Txt_bold': 'Bold',
            'lang_Txt_copy': 'Copy',
            'lang_Txt_cut': 'Cut',
            'lang_Txt_Paste': 'Paste',
            'lang_Txt_undo': 'Undo',
            'lang_Txt_redo': 'Redo',
            'lang_Txt_italic': 'Italic',
            'lang_Txt_underline': 'Underline',
            'lang_Txt_selectAll': 'Select All',
            'lang_Txt_visualEnter': 'Submit',
            'lang_Txt_fullscreen': 'Fullscreen'
        }
    },
    insertframe: {
        'static': {
            'lang_input_address': 'Address：',
            'lang_input_width': 'Width：',
            'lang_input_height': 'height：',
            'lang_input_isScroll': 'Enable scrollbars：',
            'lang_input_frameborder': 'Show frame border：',
            'lang_input_alignMode': 'Alignment：',
            'align': { title: "Alignment", options: ["Default", "Left", "Right", "Center"] }
        },
        'enterAddress': 'Please enter an address!'
    },
    link: {
        'static': {
            'lang_input_text': 'Text：',
            'lang_input_url': 'URL：',
            'lang_input_title': 'Title：',
            'lang_input_target': 'open in new window：'
        },
        'validLink': 'Supports only effective when a link is selected',
        'httpPrompt': 'The hyperlink you enter should start with "http|https|ftp://"!'
    },
    map: {
        'static': {
            lang_city: "City",
            lang_address: "Address",
            city: { value: "Beijing" },
            lang_search: "Search",
            lang_dynamicmap: "Dynamic map"
        },
        cityMsg: "Please enter the city name!",
        errorMsg: "Can't find the place!"
    },
    searchreplace: {
        'static': {
            lang_tab_search: "Search",
            lang_tab_replace: "Replace",
            lang_search1: "Search",
            lang_search2: "Search",
            lang_replace: "Replace",
            lang_searchReg: 'Support regular expression ,which starts and ends with a slash ,for example "/expression/"',
            lang_searchReg1: 'Support regular expression ,which starts and ends with a slash ,for example "/expression/"',
            lang_case_sensitive1: "Case sense",
            lang_case_sensitive2: "Case sense",
            nextFindBtn: { value: "Next" },
            preFindBtn: { value: "Preview" },
            nextReplaceBtn: { value: "Next" },
            preReplaceBtn: { value: "Preview" },
            repalceBtn: { value: "Replace" },
            repalceAllBtn: { value: "Replace all" }
        },
        getEnd: "Has the search to the bottom!",
        getStart: "Has the search to the top!",
        countMsg: "Altogether replaced {#count} character(s)!"
    },
    snapscreen: {
        'static': {
            lang_showMsg: "You should install the UEditor screenshots program first!",
            lang_download: "Download!",
            lang_step1: "Step1:Download the program and then run it",
            lang_step2: "Step2:After complete install,try to click the button again"
        }
    },
    spechars: {
        'static': {},
        tsfh: "Special",
        lmsz: "Roman",
        szfh: "Numeral",
        rwfh: "Japanese",
        xlzm: "The Greek",
        ewzm: "Russian",
        pyzm: "Phonetic",
        yyyb: "English",
        zyzf: "Others"
    },
    'edittable': {
        'static': {
            'lang_tableStyle': 'Table style',
            'lang_insertCaption': 'Add table header row',
            'lang_insertTitle': 'Add table title row',
            'lang_insertTitleCol': 'Add table title col',
            'lang_tableSize': 'Automatically adjust table size',
            'lang_autoSizeContent': 'Adaptive by form text',
            'lang_orderbycontent': "Table of contents sortable",
            'lang_autoSizePage': 'Page width adaptive',
            'lang_example': 'Example',
            'lang_borderStyle': 'Table Border',
            'lang_color': 'Color:'
        },
        captionName: 'Caption',
        titleName: 'Title',
        cellsName: 'text',
        errorMsg: 'There are merged cells, can not sort.'
    },
    'edittip': {
        'static': {
            lang_delRow: 'Delete entire row',
            lang_delCol: 'Delete entire col'
        }
    },
    'edittd': {
        'static': {
            lang_tdBkColor: 'Background Color:'
        }
    },
    'formula': {
        'static': {
        }
    },
    wordimage: {
        'static': {
            lang_resave: "The re-save step",
            uploadBtn: { src: "upload.png", alt: "Upload" },
            clipboard: { style: "background: url(copy.png) -153px -1px no-repeat;" },
            lang_step: " 1. Click top button to copy the url and then open the dialog to paste it. 2. Open after choose photos uploaded process."
        },
        fileType: "Image",
        flashError: "Flash initialization failed!",
        netError: "Network error! Please try again!",
        copySuccess: "URL has been copied!",

        'flashI18n': {
            lang: encodeURI('{"UploadingState":"totalNum: ${a},uploadComplete: ${b}", "BeforeUpload":"waitingNum: ${a}", "ExceedSize":"Size exceed${a}", "ErrorInPreview":"Preview failed", "DefaultDescription":"Description", "LoadingImage":"Loading..."}'),
            uploadingTF: encodeURI('{"font":"Arial", "size":12, "color":"0x000", "bold":"true", "italic":"false", "underline":"false"}'),
            imageTF: encodeURI('{"font":"Arial", "size":11, "color":"red", "bold":"false", "italic":"false", "underline":"false"}'),
            textEncoding: "utf-8",
            addImageSkinURL: "addImage.png",
            allDeleteBtnUpSkinURL: "allDeleteBtnUpSkin.png",
            allDeleteBtnHoverSkinURL: "allDeleteBtnHoverSkin.png",
            rotateLeftBtnEnableSkinURL: "rotateLeftEnable.png",
            rotateLeftBtnDisableSkinURL: "rotateLeftDisable.png",
            rotateRightBtnEnableSkinURL: "rotateRightEnable.png",
            rotateRightBtnDisableSkinURL: "rotateRightDisable.png",
            deleteBtnEnableSkinURL: "deleteEnable.png",
            deleteBtnDisableSkinURL: "deleteDisable.png",
            backgroundURL: '',
            listBackgroundURL: '',
            buttonURL: 'button.png'
        }
    },
    'autosave': {
        'success': 'Local conservation success'
    }
};
