<template>
  <el-popover
    placement="bottom-end"
    width="180"
    trigger="click"
    popper-class="filter-popper-wrapper"
  >
    <div class="opts-wrapper">
      <div
        class="item"
        :class="{ splieLine: idx == 1 && cusSortable }"
        v-for="(o, idx) in _opts"
        :key="o.value"
        @click="handleCommand(o)"
      >
        <svg-icon
          :icon-class="o.svgName"
          className="icon-mini"
          style="margin-right: 5px;"
        ></svg-icon>
        <template v-if="o.value == 'frozen'">
          <span class="txt">{{ o.label }}</span>
          <i v-show="isFixed" class="el-icon-check color-primary"></i>
        </template>
        <template v-else-if="o.value == 'descending'">
          <span class="txt">{{ o.label }}</span>
          <i v-show="sortType == 'descending'" class="el-icon-check color-primary"></i>
        </template>
        <template v-else-if="o.value == 'ascending'">
          <span class="txt">{{ o.label }}</span>
          <i v-show="sortType == 'ascending'" class="el-icon-check color-primary"></i>
        </template>
        <span v-else class="txt">{{ o.label }}</span>
      </div>
    </div>
    <i slot="reference" class="el-icon-more more pointer" @click.stop></i>
  </el-popover>
</template>

<script>
export default {
  computed: {
    _opts() {
      let result = this.opts;
      if(!this.cusSortable) {
        result = result.filter(s => s.value != 'descending' && s.value != 'ascending')
      }

      /**
       * 冻结列不支持排序
       */
      if (this.isFixed) {
        result = result.filter(s => s.value != "left" && s.value != "right");
      }

      /**
       * 不显示左移
       */
      if (!this.isShowLeft) {
        result = result.filter(s => s.value != "left");
      }

      /**
       * 不显示有百移
       */
      if (!this.isShowRight) {
        result = result.filter(s => s.value != "right");
      }

      /**
       * 不显示隐藏
       */
      if(!this.isShowHide) {
        result = result.filter(s => s.value != "hide");
      }

      return result;
    },
  },
  props: {
    /**
     * 是否为冻结列
     */
    isFixed: {
      type: Boolean,
      default: false,
    },
    /**
     * 排序方式：ascending、descending、''（升序、降序、不排序）
     */
    sortType: {
      type: String,
      default: "",
    },
    /**
     * 是否显示“左移”按钮
     */
    isShowLeft: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示“右移”按钮
     */
    isShowRight: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否显示“隐藏”按钮
     */
    isShowHide: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否支持排序
     */
    cusSortable: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      opts: [
        { value: "descending", label: "降序排序", svgName: "jiangxu" },
        { value: "ascending", label: "升序排序", svgName: "shenxu" },
        { value: "frozen", label: "冻结列", svgName: "dongjielie" },
        { value: "left", label: "左移", svgName: "zuoyi" },
        { value: "right", label: "右移", svgName: "youyi" },
        { value: "hide", label: "隐藏列", svgName: "yincanglie" },
      ],
    };
  },
  methods: {
    handleCommand(opt) {
      let cmd = opt.value;
      if (opt.value == "frozen" && this.isFixed) {
        //取消冻结
        cmd = "un-frozen";
      } else if (
        //取消升序、取消降序
        (opt.value == "ascending" && this.sortType == "ascending") ||
        (opt.value == "descending" && this.sortType == "descending")
      ) {
        cmd = `un-${this.sortType}`;
      }
      this.$emit("colOptBtnClick", cmd);
    },
  },
};
</script>

<style lang="scss" scoped>
.opts-wrapper {
  padding: 5px 0;
  .item {
    padding: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    .txt {
      line-height: 14px;
      flex: 1;
    }

    &:hover {
      background: $bg-color-2;
    }

    &.splieLine {
      border-bottom: 1px solid $border-color-light;
    }
  }
}

.more {
  transform: rotate(90deg);
}
</style>
