import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + '/HyperTag/'


export function getHyperParentTagList(params) {
  return request({
    url: serviceAreaName + "GetHyperParentTagList",
    method: "get",
    params,
  });
}
export function addOrEditParentTag(data) {
  return request({
    url: serviceAreaName + "AddOrEditParentTag",
    method: "post",
    data,
  });
}
export function deleteParentTag(data) {
  return request({
    url: serviceAreaName + "DeleteParentTag",
    method: "post",
    data,
  });
}
export function editParentTagSort(data) {
  return request({
    url: serviceAreaName + "EditParentTagSort",
    method: "post",
    data,
  });
}
export function getListPage(data) {
  return request({
    url: serviceAreaName + "GetListPage",
    method: "post",
    data,
  });
}
export function addOrEditTag(data) {
  return request({
    url: serviceAreaName + "AddOrEditTag",
    method: "post",
    data,
  });
}
export function getDetailApi(params) {
  return request({
    url: serviceAreaName + "GetDetail",
    method: "get",
    params,
  });
}
export function deleteTagApi(data) {
  return request({
    url: serviceAreaName + "DeleteTag",
    method: "post",
    data,
  });
}
