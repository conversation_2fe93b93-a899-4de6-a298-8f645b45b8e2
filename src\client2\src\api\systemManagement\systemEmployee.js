
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/SystemEmployee/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function getCommonEmployeeList(data) {
    return request({
        url: serviceAreaName + 'GetCommonEmployees',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}


export function resetPassword(params) {
    return request({
        url: serviceAreaName + 'ResetPassword',
        method: 'get',
        params
    })
}

export function getSuperior(params) {
    return request({
        url: serviceAreaName + 'GetSuperior',
        method: 'get',
        params
    })
}


export function setPrincipal(data) {
    return request({
        url: serviceAreaName + 'SetPrincipal',
        method: 'post',
        data
    })
}


export function adjustDepartment(data) {
    return request({
        url: serviceAreaName + 'AdjustDepartment',
        method: 'post',
        data
    })
}


export function importEmployee(data) {
    return request({
        url: serviceAreaName + 'ImportEmployee',
        method: 'post',
        data
    })
}

export function startExportExcel(params) {
    return request({
        url: serviceAreaName + 'StartExportExcel',
        method: 'get',
        params
    })
}



// ********************************员工档案接口*********************************//

export function editEmployeeRecord(data) {
    return request({
        url: serviceAreaName + 'EditEmployeeRecord',
        method: 'post',
        data
    })
}

export function getEmployeeRecordDetails(params) {
    return request({
        url: serviceAreaName + 'GetEmployeeRecordDetails',
        method: 'get',
        params
    })
}