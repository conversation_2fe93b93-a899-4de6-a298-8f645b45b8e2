import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/Position/GetPositionsByName',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/Position/EditPosition',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/Position/AddPosition',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + '/Position/DeletePositions',
        method: 'post',
        data
    })
}

export function getPositions(params) {
    return request({
        url: serviceAreaName + '/Position/GetPositionsByOrgId',
        method: 'get',
        params
    })
}