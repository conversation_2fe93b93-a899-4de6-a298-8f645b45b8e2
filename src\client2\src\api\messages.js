import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/Messages/GetListPage',
        method: 'post',
        data
    })
}


export function getListById(params) {
    return request({
        url: serviceAreaName + '/Messages/GetMessageWithDetail',
        method: 'post',
        params
    })
}



export function del(data) {
    return request({
        url: serviceAreaName + '/Messages/Delete',
        method: 'post',
        data
    })
}


//编辑消息
export function editMessage(data) {
    return request({
        url: serviceAreaName + '/Messages/EditMessage',
        method: 'post',
        data
    })
}

//根据消息ID获取任务ID
export function getTaskIdById(params) {
    return request({
        url: serviceAreaName + '/Messages/GetTaskManagementMessageDetail',
        method: 'get',
        params
    })
}



