{"version": 3, "sources": ["wangEditor.js"], "names": ["global", "factory", "exports", "module", "define", "amd", "wang<PERSON><PERSON><PERSON>", "this", "createElemByHTML", "html", "div", "document", "createElement", "innerHTML", "children", "isDOMList", "selector", "HTMLCollection", "NodeList", "querySelectorAll", "result", "Dom<PERSON><PERSON>", "nodeType", "selector<PERSON><PERSON><PERSON>", "Array", "replace", "trim", "indexOf", "length", "i", "$", "obj<PERSON>or<PERSON>ach", "obj", "fn", "key", "hasOwnProperty", "call", "arrFor<PERSON>ach", "fakeArr", "item", "getRandom", "prefix", "Math", "random", "toString", "slice", "replaceHtmlSymbol", "isFunction", "Bold", "editor", "$elem", "type", "_active", "DropList", "menu", "opt", "_this", "$container", "$title", "titleHtml", "replaceLang", "addClass", "append", "list", "onClick", "_emptyFn", "$list", "for<PERSON>ach", "elemHtml", "value", "$li", "on", "e", "hideTimeoutId", "setTimeout", "hide", "_rendered", "_show", "Head", "droplist", "width", "_command", "FontSize", "FontName", "config", "fontNames", "map", "fontName", "Panel", "Link", "Italic", "Redo", "StrikeThrough", "Underline", "Undo", "List", "Justify", "ForeColor", "colors", "color", "BackColor", "Quote", "Code", "Emoticon", "Table", "Video", "Image", "imgMenuId", "Menus", "menus", "getPasteText", "clipboardData", "originalEvent", "pasteText", "window", "getData", "getPasteHtml", "filterStyle", "ignoreImg", "pasteHtml", "docSplitHtml", "split", "getPasteImgs", "items", "test", "push", "getAsFile", "getChildrenJSON", "childNodes", "curE<PERSON>", "elemResult", "textContent", "tag", "nodeName", "toLowerCase", "attrData", "attrList", "attributes", "attrList<PERSON>ength", "attr", "name", "attrs", "Text", "Command", "API", "_current<PERSON><PERSON>e", "Progress", "_time", "_isShow", "_isRender", "_timeoutId", "$textContainer", "$textContainerElem", "$bar", "UploadImg", "Editor", "toolbarSelector", "textSelector", "Error", "id", "editorId", "customConfig", "eventList", "prototype", "constructor", "elem", "clone", "deep", "cloneList", "cloneNode", "get", "index", "first", "last", "types", "addEventListener", "target", "matches", "off", "removeEventListener", "val", "getAttribute", "setAttribute", "className", "arr", "filter", "join", "removeClass", "css", "currentStyle", "style", "styleArr", "resultArr", "show", "$children", "child", "append<PERSON><PERSON><PERSON>", "remove", "parent", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "isContain", "$child", "contains", "getSizeData", "getBoundingClientRect", "getNodeName", "find", "text", "focus", "parentUntil", "_currentElem", "results", "equal", "insertBefore", "$referenceNode", "referenceNode", "parentNode", "insertAfter", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "offAll", "emotions", "title", "content", "alt", "src", "zIndex", "debug", "linkCheck", "link", "linkImgCheck", "pasteFilterStyle", "pasteIgnoreImg", "pasteTextHandle", "showLinkImg", "linkImgCallback", "url", "uploadImgMaxSize", "uploadImgShowBase64", "uploadFileName", "uploadImgParams", "uploadImgHeaders", "withCredentials", "uploadImgTimeout", "uploadImgHooks", "before", "xhr", "files", "success", "fail", "error", "timeout", "qiniu", "UA", "_ua", "navigator", "userAgent", "isWebkit", "isIE", "isSeleEmpty", "selection", "isSelectionEmpty", "createEmptyRange", "cmd", "do", "collapseRange", "restoreSelection", "tryChangeActive", "queryCommandState", "str", "lang<PERSON>rgs", "reg", "clearTimeout", "$menuELem", "menuHeight", "height", "showTimeoutId", "$selectionElem", "getSelectionContainerElem", "$textElem", "cmdValue", "queryCommandValue", "emptyFn", "_isCreatedPanelMenus", "$body", "$closeBtn", "$tabTitleContainer", "$tabContentContainer", "tabs", "tabTitleArr", "tabContentArr", "tab", "tabIndex", "tpl", "$content", "_index", "stopPropagation", "events", "event", "$inputs", "_hideOther<PERSON><PERSON><PERSON>", "panel", "$linkelem", "createRangeByElem", "_createPanel", "getSelectionText", "inputLinkId", "inputTextId", "btnOkId", "btnDelId", "delBtnDisplay", "$link", "$text", "_insertLink", "_delLink", "selectionText", "checkResult", "alert", "$selectionELem", "$parent", "$targetELem", "$startElem", "getSelectionStartElem", "$endElem", "getSelectionEndElem", "$code", "textId", "btnId", "_insertCode", "_updateCode", "$parentElem", "tabConfig", "emotData", "emotType", "faceHtml", "$target", "insertHtml", "_insert", "emotHtml", "_createEditPanel", "_createInsertPanel", "btnInsertId", "textRowNum", "textColNum", "row<PERSON>um", "parseInt", "colNum", "r", "c", "_this2", "addRowBtnId", "addColBtnId", "delRowBtnId", "delColBtnId", "delTableBtnId", "_addRow", "_addCol", "_delRow", "_delCol", "_delTable", "_getLocationData", "$tr", "$tds", "tdLength", "td", "$tbody", "$trs", "tr<PERSON><PERSON><PERSON>", "tr", "locationData", "trData", "$currentTr", "tdData", "newTr", "tdIndex", "$currentTd", "$table", "textValId", "width30", "width50", "width100", "delBtn", "tabsConfig", "$img", "_selectedImg", "uploadImg", "upTriggerId", "upFileId", "linkUrlId", "linkBtnId", "$file", "fileElem", "click", "fileList", "$linkUrl", "insertLinkImg", "tabsConfigResult", "uploadImgServer", "customUploadImg", "FileReader", "MenuConstructors", "bold", "head", "fontSize", "italic", "redo", "strikeThrough", "underline", "undo", "justify", "foreColor", "backColor", "quote", "code", "emoticon", "table", "video", "image", "init", "<PERSON><PERSON>ey", "MenuConstructor", "_addToToolbar", "_bindEvent", "$toolbarElem", "getRange", "changeActive", "clear", "initSelection", "getJSON", "_saveRangeRealTime", "_enterKeyHandle", "_clear<PERSON><PERSON>le", "_pasteHandle", "_tabHandle", "_imgHandle", "_drag<PERSON><PERSON>le", "saveRange", "insertEmptyP", "$p", "pHandle", "codeHandle", "selectionNodeName", "parentNodeName", "queryCommandSupported", "_willBreakCode", "preventDefault", "_startOffset", "startOffset", "codeLength", "keyCode", "txtHtml", "canDo", "now", "Date", "flag", "pasteTime", "resetTime", "ex", "pasteFiles", "img", "dataTransfer", "_useStyleWithCSS", "execCommand", "_name", "_execCommand", "change", "_insertHTML", "range", "insertNode", "deleteContents", "pasteHTML", "_insertElem", "_range", "getSelection", "rangeCount", "getRangeAt", "$containerElem", "toStart", "collapse", "commonAncestorContainer", "startContainer", "endContainer", "endOffset", "removeAllRanges", "addRange", "setEnd", "<PERSON><PERSON><PERSON><PERSON>", "createRange", "selectNodeContents", "selectNode", "progress", "timeoutId", "_hide", "_typeof", "Symbol", "iterator", "_alert", "alertInfo", "debugInfo", "customAlert", "onload", "callback", "onerror", "<PERSON>ab<PERSON>", "_this3", "maxSize", "maxSizeM", "max<PERSON><PERSON><PERSON>", "uploadImgMaxLength", "uploadImgParamsWithUrl", "hooks", "resultFiles", "errInfo", "file", "size", "bind", "formdata", "FormData", "uploadImgServerArr", "uploadImgServerHash", "XMLHttpRequest", "open", "ontimeout", "upload", "onprogress", "percent", "progressBar", "lengthComputable", "loaded", "total", "onreadystatechange", "readyState", "status", "responseText", "JSON", "parse", "customInsert", "errno", "data", "beforeResult", "prevent", "msg", "setRequestHeader", "send", "reader", "readAsDataURL", "_initConfig", "Object", "assign", "langConfig", "lang", "RegExp", "_initDom", "$toolbarSelector", "config$$1", "toolbarElemId", "textElemId", "compositionEnd", "onfocus", "onblur", "isFocus", "<PERSON><PERSON><PERSON><PERSON>", "isToolbar", "isMenu", "_initCommand", "_initSelectionAPI", "_initUploadImg", "_initMenus", "_initText", "txt", "newLine", "$last", "onChangeTimeoutId", "beforeChangeHtml", "onchangeTimeout", "onchange", "currentHtml", "create", "_offAllEvent", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "to", "arguments", "nextSource", "<PERSON><PERSON><PERSON>", "Element", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "s", "ownerDocument", "getElementsByTagName"], "mappings": "CAAC,SAAUA,EAAQC,GACC,gBAAZC,UAA0C,mBAAXC,QAAyBA,OAAOD,QAAUD,IAC9D,kBAAXG,SAAyBA,OAAOC,IAAMD,OAAOH,GACnDD,EAAOM,WAAaL,KACpBM,KAAM,WAAe,YAoDvB,SAASC,GAAiBC,GACtB,GAAIC,OAAM,EAGV,OAFAA,GAAMC,SAASC,cAAc,OAC7BF,EAAIG,UAAYJ,EACTC,EAAII,SAIf,QAASC,GAAUC,GACf,QAAKA,IAGDA,YAAoBC,iBAAkBD,YAAoBE,WAOlE,QAASC,GAAiBH,GACtB,GAAII,GAAST,SAASQ,iBAAiBH,EACvC,OAAID,GAAUK,GACHA,GAECA,GAQhB,QAASC,GAAWL,GAChB,GAAKA,EAAL,CAKA,GAAIA,YAAoBK,GACpB,MAAOL,EAGXT,MAAKS,SAAWA,CAChB,IAAIM,GAAWN,EAASM,SAGpBC,IACa,KAAbD,EAEAC,GAAkBP,GACE,IAAbM,EAEPC,GAAkBP,GACXD,EAAUC,IAAaA,YAAoBQ,OAElDD,EAAiBP,EACU,gBAAbA,KAEdA,EAAWA,EAASS,QAAQ,SAAU,IAAIC,OAGtCH,EAF0B,IAA1BP,EAASW,QAAQ,KAEAnB,EAAiBQ,GAGjBG,EAAiBH,GAI1C,IAAIY,GAASL,EAAeK,MAC5B,KAAKA,EAED,MAAOrB,KAIX,IAAIsB,OAAI,EACR,KAAKA,EAAI,EAAGA,EAAID,EAAQC,IACpBtB,KAAKsB,GAAKN,EAAeM,EAE7BtB,MAAKqB,OAASA,GAuYlB,QAASE,GAAEd,GACP,MAAO,IAAIK,GAAWL,GAyN1B,QAASe,GAAWC,EAAKC,GACrB,GAAIC,OAAM,EAEV,KAAKA,IAAOF,GACR,GAAIA,EAAIG,eAAeD,KAEJ,IADND,EAAGG,KAAKJ,EAAKE,EAAKF,EAAIE,IAE3B,MAOhB,QAASG,GAAWC,EAASL,GACzB,GAAIJ,OAAI,GACJU,MAAO,GAEPX,EAASU,EAAQV,QAAU,CAC/B,KAAKC,EAAI,EAAGA,EAAID,IACZW,EAAOD,EAAQT,IAEA,IADNI,EAAGG,KAAKE,EAASC,EAAMV,IAFZA,MAU5B,QAASW,GAAUC,GACf,MAAOA,GAASC,KAAKC,SAASC,WAAWC,MAAM,GAInD,QAASC,GAAkBrC,GACvB,MAAY,OAARA,EACO,GAEJA,EAAKgB,QAAQ,MAAO,QAAQA,QAAQ,MAAO,QAAQA,QAAQ,MAAO,UAAUA,QAAQ,gBAAiB,SAOhH,QAASsB,GAAWd,GAChB,MAAqB,kBAAPA,GAOlB,QAASe,GAAKC,GACV1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,qFACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EAuEnB,QAASC,GAASC,EAAMC,GACpB,GAAIC,GAAQjD,KAGR0C,EAASK,EAAKL,MAClB1C,MAAK+C,KAAOA,EACZ/C,KAAKgD,IAAMA,CAEX,IAAIE,GAAa3B,EAAE,oCAGf4B,EAASH,EAAIG,OACbC,MAAY,EACZD,KAEAC,EAAYD,EAAOjD,OACnBkD,EAAYC,EAAYX,EAAQU,GAChCD,EAAOjD,KAAKkD,GAEZD,EAAOG,SAAS,gBAChBJ,EAAWK,OAAOJ,GAGtB,IAAIK,GAAOR,EAAIQ,SACXZ,EAAOI,EAAIJ,MAAQ,OACnBa,EAAUT,EAAIS,SAAWC,EAGzBC,EAAQpC,EAAE,eAA0B,SAATqB,EAAkB,WAAa,aAAe,UAC7EM,GAAWK,OAAOI,GAClBH,EAAKI,QAAQ,SAAU5B,GACnB,GAAIW,GAAQX,EAAKW,MAGbkB,EAAWlB,EAAMzC,MACrB2D,GAAWR,EAAYX,EAAQmB,GAC/BlB,EAAMzC,KAAK2D,EAEX,IAAIC,GAAQ9B,EAAK8B,MACbC,EAAMxC,EAAE,6BACRoB,KACAoB,EAAIR,OAAOZ,GACXgB,EAAMJ,OAAOQ,GACbA,EAAIC,GAAG,QAAS,SAAUC,GACtBR,EAAQK,GAGRb,EAAMiB,cAAgBC,WAAW,WAC7BlB,EAAMmB,QACP,QAMflB,EAAWc,GAAG,aAAc,SAAUC,GAClChB,EAAMiB,cAAgBC,WAAW,WAC7BlB,EAAMmB,QACP,KAIPpE,KAAKkD,WAAaA,EAGlBlD,KAAKqE,WAAY,EACjBrE,KAAKsE,OAAQ,EA2DjB,QAASC,GAAK7B,GACV,GAAIO,GAAQjD,IAEZA,MAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,+DACfvB,KAAK4C,KAAO,WAGZ5C,KAAK6C,SAAU,EAGf7C,KAAKwE,SAAW,GAAI1B,GAAS9C,MACzByE,MAAO,IACPtB,OAAQ5B,EAAE,eACVqB,KAAM,OACNY,OAASb,MAAOpB,EAAE,eAAgBuC,MAAO,SAAYnB,MAAOpB,EAAE,eAAgBuC,MAAO,SAAYnB,MAAOpB,EAAE,eAAgBuC,MAAO,SAAYnB,MAAOpB,EAAE,eAAgBuC,MAAO,SAAYnB,MAAOpB,EAAE,eAAgBuC,MAAO,SAAYnB,MAAOpB,EAAE,aAAcuC,MAAO,QACnQL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MA4C3B,QAASa,GAASjC,GACd,GAAIO,GAAQjD,IAEZA,MAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,mEACfvB,KAAK4C,KAAO,WAGZ5C,KAAK6C,SAAU,EAGf7C,KAAKwE,SAAW,GAAI1B,GAAS9C,MACzByE,MAAO,IACPtB,OAAQ5B,EAAE,aACVqB,KAAM,OACNY,OAASb,MAAOpB,EAAE,oDAAqDuC,MAAO,MAASnB,MAAOpB,EAAE,gDAAiDuC,MAAO,MAASnB,MAAOpB,EAAE,uBAAwBuC,MAAO,MAASnB,MAAOpB,EAAE,gDAAiDuC,MAAO,MAASnB,MAAOpB,EAAE,oDAAqDuC,MAAO,MAASnB,MAAOpB,EAAE,sDAAuDuC,MAAO,MACjbL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAqB3B,QAASc,GAASlC,GACd,GAAIO,GAAQjD,IAEZA,MAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,6DACfvB,KAAK4C,KAAO,WAGZ5C,KAAK6C,SAAU,CAGf,IAAIgC,GAASnC,EAAOmC,OAChBC,EAAYD,EAAOC,aAGvB9E,MAAKwE,SAAW,GAAI1B,GAAS9C,MACzByE,MAAO,IACPtB,OAAQ5B,EAAE,aACVqB,KAAM,OACNY,KAAMsB,EAAUC,IAAI,SAAUC,GAC1B,OAASrC,MAAOpB,EAAE,6BAA+ByD,EAAW,MAAQA,EAAW,WAAYlB,MAAOkB,KAEtGvB,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAyB3B,QAASmB,GAAMlC,EAAMC,GACjBhD,KAAK+C,KAAOA,EACZ/C,KAAKgD,IAAMA,EAyLf,QAASkC,GAAKxC,GACV1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,6DACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EAsJnB,QAASsC,GAAOzC,GACZ1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,uFACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EA+CnB,QAASuC,GAAK1C,GACV1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,qFACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EAsBnB,QAASwC,GAAc3C,GACnB1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,8FACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EA+CnB,QAASyC,GAAU5C,GACf1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,0FACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EA+CnB,QAAS0C,GAAK7C,GACV1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,qFACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EAsBnB,QAAS2C,GAAK9C,GACV,GAAIO,GAAQjD,IAEZA,MAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,8DACfvB,KAAK4C,KAAO,WAGZ5C,KAAK6C,SAAU,EAGf7C,KAAKwE,SAAW,GAAI1B,GAAS9C,MACzByE,MAAO,IACPtB,OAAQ5B,EAAE,eACVqB,KAAM,OACNY,OAASb,MAAOpB,EAAE,4DAA6DuC,MAAO,sBAAyBnB,MAAOpB,EAAE,oDAAqDuC,MAAO,wBACpLL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MA2D3B,QAAS2B,GAAQ/C,GACb,GAAIO,GAAQjD,IAEZA,MAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,uEACfvB,KAAK4C,KAAO,WAGZ5C,KAAK6C,SAAU,EAGf7C,KAAKwE,SAAW,GAAI1B,GAAS9C,MACzByE,MAAO,IACPtB,OAAQ5B,EAAE,eACVqB,KAAM,OACNY,OAASb,MAAOpB,EAAE,2DAA4DuC,MAAO,gBAAmBnB,MAAOpB,EAAE,6DAA8DuC,MAAO,kBAAqBnB,MAAOpB,EAAE,4DAA6DuC,MAAO,iBACxRL,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAoB3B,QAAS4B,GAAUhD,GACf,GAAIO,GAAQjD,IAEZA,MAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,gEACfvB,KAAK4C,KAAO,UAGZ,IAAIiC,GAASnC,EAAOmC,OAChBc,EAASd,EAAOc,UAGpB3F,MAAK6C,SAAU,EAGf7C,KAAKwE,SAAW,GAAI1B,GAAS9C,MACzByE,MAAO,IACPtB,OAAQ5B,EAAE,eACVqB,KAAM,eACNY,KAAMmC,EAAOZ,IAAI,SAAUa,GACvB,OAASjD,MAAOpB,EAAE,mBAAqBqE,EAAQ,oCAAqC9B,MAAO8B,KAE/FnC,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAoB3B,QAAS+B,GAAUnD,GACf,GAAIO,GAAQjD,IAEZA,MAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,oEACfvB,KAAK4C,KAAO,UAGZ,IAAIiC,GAASnC,EAAOmC,OAChBc,EAASd,EAAOc,UAGpB3F,MAAK6C,SAAU,EAGf7C,KAAKwE,SAAW,GAAI1B,GAAS9C,MACzByE,MAAO,IACPtB,OAAQ5B,EAAE,cACVqB,KAAM,eACNY,KAAMmC,EAAOZ,IAAI,SAAUa,GACvB,OAASjD,MAAOpB,EAAE,mBAAqBqE,EAAQ,wCAAyC9B,MAAO8B,KAEnGnC,QAAS,SAAiBK,GAEtBb,EAAMyB,SAASZ,MAoB3B,QAASgC,GAAMpD,GACX1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,4FACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EA8DnB,QAASkD,GAAKrD,GACV1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,yFACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EAiInB,QAASmD,GAAStD,GACd1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,sFACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EAqGnB,QAASoD,GAAMvD,GACX1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,+DACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EAmVnB,QAASqD,GAAMxD,GACX1C,KAAK0C,OAASA,EACd1C,KAAK2C,MAAQpB,EAAE,6DACfvB,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EAqEnB,QAASsD,GAAMzD,GACX1C,KAAK0C,OAASA,CACd,IAAI0D,GAAYnE,EAAU,UAC1BjC,MAAK2C,MAAQpB,EAAE,6BAA+B6E,EAAY,0CAC1D1D,EAAO0D,UAAYA,EACnBpG,KAAK4C,KAAO,QAGZ5C,KAAK6C,SAAU,EA2PnB,QAASwD,GAAM3D,GACX1C,KAAK0C,OAASA,EACd1C,KAAKsG,SA0HT,QAASC,GAAatC,GAClB,GAAIuC,GAAgBvC,EAAEuC,eAAiBvC,EAAEwC,eAAiBxC,EAAEwC,cAAcD,cACtEE,MAAY,EAOhB,OALIA,GADiB,MAAjBF,EACYG,OAAOH,eAAiBG,OAAOH,cAAcI,QAAQ,QAErDJ,EAAcI,QAAQ,cAG/BrE,EAAkBmE,GAI7B,QAASG,GAAa5C,EAAG6C,EAAaC,GAClC,GAAIP,GAAgBvC,EAAEuC,eAAiBvC,EAAEwC,eAAiBxC,EAAEwC,cAAcD,cACtEE,MAAY,GACZM,MAAY,EAUhB,IATqB,MAAjBR,EACAE,EAAYC,OAAOH,eAAiBG,OAAOH,cAAcI,QAAQ,SAEjEF,EAAYF,EAAcI,QAAQ,cAClCI,EAAYR,EAAcI,QAAQ,eAEjCI,GAAaN,IACdM,EAAY,MAAQzE,EAAkBmE,GAAa,QAElDM,EAAL,CAKA,GAAIC,GAAeD,EAAUE,MAAM,UAyBnC,OAxB4B,KAAxBD,EAAa5F,SACb2F,EAAYC,EAAa,IAI7BD,EAAYA,EAAU9F,QAAQ,6BAA8B,IAE5D8F,EAAYA,EAAU9F,QAAQ,eAAgB,IAE9C8F,EAAYA,EAAU9F,QAAQ,+BAAgC,IAE1D6F,IAEAC,EAAYA,EAAU9F,QAAQ,cAAe,KAK7C8F,EAFAF,EAEYE,EAAU9F,QAAQ,oCAAqC,IAGvD8F,EAAU9F,QAAQ,4BAA6B,KAOnE,QAASiG,GAAalD,GAClB,GAAIpD,KAEJ,IADU0F,EAAatC,GAGnB,MAAOpD,EAGX,IAAI2F,GAAgBvC,EAAEuC,eAAiBvC,EAAEwC,eAAiBxC,EAAEwC,cAAcD,kBACtEY,EAAQZ,EAAcY,KAC1B,OAAKA,IAIL5F,EAAW4F,EAAO,SAAUzF,EAAKmC,GAC7B,GAAIlB,GAAOkB,EAAMlB,IACb,UAASyE,KAAKzE,IACd/B,EAAOyG,KAAKxD,EAAMyD,eAInB1G,GAVIA,EAkBf,QAAS2G,GAAgB7E,GACrB,GAAI9B,KAoCJ,QAnCgB8B,EAAM8E,kBACZ7D,QAAQ,SAAU8D,GACxB,GAAIC,OAAa,GACb5G,EAAW2G,EAAQ3G,QASvB,IANiB,IAAbA,IACA4G,EAAaD,EAAQE,YACrBD,EAAapF,EAAkBoF,IAIlB,IAAb5G,EAAgB,CAChB4G,KAGAA,EAAWE,IAAMH,EAAQI,SAASC,aAKlC,KAAK,GAHDC,MACAC,EAAWP,EAAQQ,eACnBC,EAAiBF,EAAS5G,QAAU,EAC/BC,EAAI,EAAGA,EAAI6G,EAAgB7G,IAAK,CACrC,GAAI8G,GAAOH,EAAS3G,EACpB0G,GAASV,MACLe,KAAMD,EAAKC,KACXvE,MAAOsE,EAAKtE,QAGpB6D,EAAWW,MAAQN,EAEnBL,EAAWpH,SAAWiH,EAAgBjG,EAAEmG,IAG5C7G,EAAOyG,KAAKK,KAET9G,EAIX,QAAS0H,GAAK7F,GACV1C,KAAK0C,OAASA,EAwflB,QAAS8F,GAAQ9F,GACb1C,KAAK0C,OAASA,EAqGlB,QAAS+F,GAAI/F,GACT1C,KAAK0C,OAASA,EACd1C,KAAK0I,cAAgB,KAmLzB,QAASC,GAASjG,GACd1C,KAAK0C,OAASA,EACd1C,KAAK4I,MAAQ,EACb5I,KAAK6I,SAAU,EACf7I,KAAK8I,WAAY,EACjB9I,KAAK+I,WAAa,EAClB/I,KAAKgJ,eAAiBtG,EAAOuG,mBAC7BjJ,KAAKkJ,KAAO3H,EAAE,oCAgElB,QAAS4H,GAAUzG,GACf1C,KAAK0C,OAASA,EA8TlB,QAAS0G,GAAOC,EAAiBC,GAC7B,GAAuB,MAAnBD,EAEA,KAAM,IAAIE,OAAM,2BAGpBvJ,MAAKwJ,GAAK,cAAgBC,IAE1BzJ,KAAKqJ,gBAAkBA,EACvBrJ,KAAKsJ,aAAeA,EAGpBtJ,KAAK0J,gBA7uIT,GA2EIC,KAsDJ7I,GAAW8I,WACPC,YAAa/I,EAGb8C,QAAS,SAAiBlC,GACtB,GAAIJ,OAAI,EACR,KAAKA,EAAI,EAAGA,EAAItB,KAAKqB,OAAQC,IAAK,CAC9B,GAAIwI,GAAO9J,KAAKsB,EAEhB,KAAe,IADFI,EAAGG,KAAKiI,EAAMA,EAAMxI,GAE7B,MAGR,MAAOtB,OAIX+J,MAAO,SAAeC,GAClB,GAAIC,KAIJ,OAHAjK,MAAK4D,QAAQ,SAAUkG,GACnBG,EAAU3C,KAAKwC,EAAKI,YAAYF,MAE7BzI,EAAE0I,IAIbE,IAAK,SAAaC,GACd,GAAI/I,GAASrB,KAAKqB,MAIlB,OAHI+I,IAAS/I,IACT+I,GAAgB/I,GAEbE,EAAEvB,KAAKoK,KAIlBC,MAAO,WACH,MAAOrK,MAAKmK,IAAI,IAIpBG,KAAM,WACF,GAAIjJ,GAASrB,KAAKqB,MAClB,OAAOrB,MAAKmK,IAAI9I,EAAS,IAI7B2C,GAAI,SAAYpB,EAAMnC,EAAUiB,GAEvBA,IACDA,EAAKjB,EACLA,EAAW,KAIf,IAAI8J,KAGJ,OAFAA,GAAQ3H,EAAKsE,MAAM,OAEZlH,KAAK4D,QAAQ,SAAUkG,GAC1BS,EAAM3G,QAAQ,SAAUhB,GACpB,GAAKA,EAAL,CAWA,GANA+G,EAAUrC,MACNwC,KAAMA,EACNlH,KAAMA,EACNlB,GAAIA,KAGHjB,EAGD,WADAqJ,GAAKU,iBAAiB5H,EAAMlB,EAKhCoI,GAAKU,iBAAiB5H,EAAM,SAAUqB,GAClC,GAAIwG,GAASxG,EAAEwG,MACXA,GAAOC,QAAQjK,IACfiB,EAAGG,KAAK4I,EAAQxG,WAQpC0G,IAAK,SAAa/H,EAAMlB,GACpB,MAAO1B,MAAK4D,QAAQ,SAAUkG,GAC1BA,EAAKc,oBAAoBhI,EAAMlB,MAKvC0G,KAAM,SAAczG,EAAKkJ,GACrB,MAAW,OAAPA,EAEO7K,KAAK,GAAG8K,aAAanJ,GAGrB3B,KAAK4D,QAAQ,SAAUkG,GAC1BA,EAAKiB,aAAapJ,EAAKkJ,MAMnCvH,SAAU,SAAkB0H,GACxB,MAAKA,GAGEhL,KAAK4D,QAAQ,SAAUkG,GAC1B,GAAImB,OAAM,EACNnB,GAAKkB,WAELC,EAAMnB,EAAKkB,UAAU9D,MAAM,MAC3B+D,EAAMA,EAAIC,OAAO,SAAUlJ,GACvB,QAASA,EAAKb,SAGd8J,EAAI7J,QAAQ4J,GAAa,GACzBC,EAAI3D,KAAK0D,GAGblB,EAAKkB,UAAYC,EAAIE,KAAK,MAE1BrB,EAAKkB,UAAYA,IAjBdhL,MAuBfoL,YAAa,SAAqBJ,GAC9B,MAAKA,GAGEhL,KAAK4D,QAAQ,SAAUkG,GAC1B,GAAImB,OAAM,EACNnB,GAAKkB,YAELC,EAAMnB,EAAKkB,UAAU9D,MAAM,MAC3B+D,EAAMA,EAAIC,OAAO,SAAUlJ,GAGvB,UAFAA,EAAOA,EAAKb,SAECa,IAASgJ,KAM1BlB,EAAKkB,UAAYC,EAAIE,KAAK,QAhBvBnL,MAsBfqL,IAAK,SAAa1J,EAAKkJ,GACnB,GAAIS,GAAe3J,EAAM,IAAMkJ,EAAM,GACrC,OAAO7K,MAAK4D,QAAQ,SAAUkG,GAC1B,GAAIyB,IAASzB,EAAKgB,aAAa,UAAY,IAAI3J,OAC3CqK,MAAW,GACXC,IACAF,IAEAC,EAAWD,EAAMrE,MAAM,KACvBsE,EAAS5H,QAAQ,SAAU5B,GAEvB,GAAIiJ,GAAMjJ,EAAKkF,MAAM,KAAKnC,IAAI,SAAUzD,GACpC,MAAOA,GAAEH,QAEM,KAAf8J,EAAI5J,QACJoK,EAAUnE,KAAK2D,EAAI,GAAK,IAAMA,EAAI,MAI1CQ,EAAYA,EAAU1G,IAAI,SAAU/C,GAChC,MAA0B,KAAtBA,EAAKZ,QAAQO,GACN2J,EAEAtJ,IAGXyJ,EAAUrK,QAAQkK,GAAgB,GAClCG,EAAUnE,KAAKgE,GAGnBxB,EAAKiB,aAAa,QAASU,EAAUN,KAAK,QAG1CrB,EAAKiB,aAAa,QAASO,MAMvCI,KAAM,WACF,MAAO1L,MAAKqL,IAAI,UAAW,UAI/BjH,KAAM,WACF,MAAOpE,MAAKqL,IAAI,UAAW,SAI/B9K,SAAU,WACN,GAAIuJ,GAAO9J,KAAK,EAChB,OAAK8J,GAIEvI,EAAEuI,EAAKvJ,UAHH,MAOfkH,WAAY,WACR,GAAIqC,GAAO9J,KAAK,EAChB,OAAK8J,GAIEvI,EAAEuI,EAAKrC,YAHH,MAOflE,OAAQ,SAAgBoI,GACpB,MAAO3L,MAAK4D,QAAQ,SAAUkG,GAC1B6B,EAAU/H,QAAQ,SAAUgI,GACxB9B,EAAK+B,YAAYD,QAM7BE,OAAQ,WACJ,MAAO9L,MAAK4D,QAAQ,SAAUkG,GAC1B,GAAIA,EAAKgC,OACLhC,EAAKgC,aACF,CACH,GAAIC,GAASjC,EAAKkC,aAClBD,IAAUA,EAAOE,YAAYnC,OAMzCoC,UAAW,SAAmBC,GAC1B,GAAIrC,GAAO9J,KAAK,GACZ4L,EAAQO,EAAO,EACnB,OAAOrC,GAAKsC,SAASR,IAIzBS,YAAa,WAET,MADWrM,MAAK,GACJsM,yBAIhBC,YAAa,WAET,MADWvM,MAAK,GACJ8H,UAIhB0E,KAAM,SAAc/L,GAEhB,MAAOc,GADIvB,KAAK,GACFY,iBAAiBH,KAInCgM,KAAM,SAAc5B,GAChB,MAAKA,GAQM7K,KAAK4D,QAAQ,SAAUkG,GAC1BA,EAAKxJ,UAAYuK,IAPV7K,KAAK,GACJM,UAAUY,QAAQ,SAAU,WACpC,MAAO,MAWnBhB,KAAM,SAAc4D,GAChB,GAAIgG,GAAO9J,KAAK,EAChB,OAAa,OAAT8D,EACOgG,EAAKxJ,WAEZwJ,EAAKxJ,UAAYwD,EACV9D,OAKf6K,IAAK,WAED,MADW7K,MAAK,GACJ8D,MAAM3C,QAItBuL,MAAO,WACH,MAAO1M,MAAK4D,QAAQ,SAAUkG,GAC1BA,EAAK4C,WAKbX,OAAQ,WAEJ,MAAOxK,GADIvB,KAAK,GACFgM,gBAIlBW,YAAa,SAAqBlM,EAAUmM,GACxC,GAAIC,GAAUzM,SAASQ,iBAAiBH,GACpCY,EAASwL,EAAQxL,MACrB,KAAKA,EAED,MAAO,KAGX,IAAIyI,GAAO8C,GAAgB5M,KAAK,EAChC,IAAsB,SAAlB8J,EAAKhC,SACL,MAAO,KAGX,IAAIiE,GAASjC,EAAKkC,cACd1K,MAAI,EACR,KAAKA,EAAI,EAAGA,EAAID,EAAQC,IACpB,GAAIyK,IAAWc,EAAQvL,GAEnB,MAAOC,GAAEwK,EAKjB,OAAO/L,MAAK2M,YAAYlM,EAAUsL,IAItCe,MAAO,SAAenK,GAClB,MAAuB,KAAnBA,EAAM5B,SACCf,KAAK,KAAO2C,EAEZ3C,KAAK,KAAO2C,EAAM,IAKjCoK,aAAc,SAAsBtM,GAChC,GAAIuM,GAAiBzL,EAAEd,GACnBwM,EAAgBD,EAAe,EACnC,OAAKC,GAGEjN,KAAK4D,QAAQ,SAAUkG,GACbmD,EAAcC,WACpBH,aAAajD,EAAMmD,KAJnBjN,MASfmN,YAAa,SAAqB1M,GAC9B,GAAIuM,GAAiBzL,EAAEd,GACnBwM,EAAgBD,EAAe,EACnC,OAAKC,GAGEjN,KAAK4D,QAAQ,SAAUkG,GAC1B,GAAIiC,GAASkB,EAAcC,UACvBnB,GAAOqB,YAAcH,EAErBlB,EAAOF,YAAY/B,GAGnBiC,EAAOgB,aAAajD,EAAMmD,EAAcI,eATrCrN,OAqBnBuB,EAAE+L,OAAS,WACP3D,EAAU/F,QAAQ,SAAU5B,GACxB,GAAI8H,GAAO9H,EAAK8H,KACZlH,EAAOZ,EAAKY,KACZlB,EAAKM,EAAKN,EAEdoI,GAAKc,oBAAoBhI,EAAMlB,KAQvC,IAAImD,IAGAyB,OAAQ,OAAQ,OAAQ,WAAY,WAAY,SAAU,YAAa,gBAAiB,YAAa,YAAa,OAAQ,OAAQ,UAAW,QAAS,WAAY,QAAS,QAAS,QAAS,OAAQ,OAAQ,QAE7MxB,WAAY,KAAM,OAAQ,QAAS,SAAU,WAE7Ca,QAAS,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAa5G4H,WAEIC,MAAO,KAEP5K,KAAM,QAEN6K,UACIC,IAAK,OACLC,IAAK,yFAELD,IAAK,OACLC,IAAK,qFAELD,IAAK,MACLC,IAAK,qFAITH,MAAO,KAEP5K,KAAM,QAEN6K,UACIE,IAAK,uFACLD,IAAK,UAELC,IAAK,qFACLD,IAAK,SAELC,IAAK,oFACLD,IAAK,WAITF,MAAO,QAEP5K,KAAM,QAEN6K,QAAS,2DAA2DvG,MAAM,QAI9E0G,OAAQ,IAGRC,OAAO,EAGPC,UAAW,SAAmBrB,EAAMsB,GAGhC,OAAO,GAKXC,aAAc,SAAsBL,GAEhC,OAAO,GAKXM,kBAAkB,EAGlBC,gBAAgB,EAIhBC,gBAAiB,SAAyBV,GAEtC,MAAOA,IAUXW,aAAa,EAGbC,gBAAiB,SAAyBC,KAK1CC,iBAAkB,QAMlBC,qBAAqB,EAMrBC,eAAgB,GAGhBC,mBAKAC,oBAKAC,iBAAiB,EAGjBC,iBAAkB,IAGlBC,gBASIC,OAAQ,SAAgBC,EAAKtM,EAAQuM,KASrCC,QAAS,SAAiBF,EAAKtM,EAAQ7B,KAGvCsO,KAAM,SAAcH,EAAKtM,EAAQ7B,KAGjCuO,MAAO,SAAeJ,EAAKtM,KAG3B2M,QAAS,SAAiBL,EAAKtM,MAMnC4M,OAAO,GASPC,GACAC,IAAKC,UAAUC,UAGfC,SAAU,WAEN,MADU,UACCtI,KAAKrH,KAAKwP,MAIzBI,KAAM,WACF,MAAO,iBAAmBjJ,SAoElClE,GAAKmH,WACDC,YAAapH,EAGbgB,QAAS,SAAiBQ,GAGtB,GAAIvB,GAAS1C,KAAK0C,OACdmN,EAAcnN,EAAOoN,UAAUC,kBAE/BF,IAEAnN,EAAOoN,UAAUE,mBAIrBtN,EAAOuN,IAAIC,GAAG,QAEVL,IAEAnN,EAAOoN,UAAUK,gBACjBzN,EAAOoN,UAAUM,qBAKzBC,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,KACbD,GAAOuN,IAAIK,kBAAkB,SAC7BtQ,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,gBAS9B,IAAI/H,GAAc,SAAUX,EAAQ6N,GAChC,GAAIC,GAAW9N,EAAOmC,OAAO2L,aACzB3P,EAAS0P,CAab,OAXAC,GAAS5M,QAAQ,SAAU5B,GACvB,GAAIyO,GAAMzO,EAAKyO,IACX5F,EAAM7I,EAAK6I,GAEX4F,GAAIpJ,KAAKxG,KACTA,EAASA,EAAOK,QAAQuP,EAAK,WACzB,MAAO5F,QAKZhK,GAMP6C,EAAW,YAyEfZ,GAAS8G,WACLC,YAAa/G,EAGb4I,KAAM,WACE1L,KAAKkE,eAELwM,aAAa1Q,KAAKkE,cAGtB,IAAInB,GAAO/C,KAAK+C,KACZ4N,EAAY5N,EAAKJ,MACjBO,EAAalD,KAAKkD,UACtB,KAAIlD,KAAKsE,MAAT,CAGA,GAAItE,KAAKqE,UAELnB,EAAWwI,WACR,CAEH,GAAIkF,GAAaD,EAAUtE,cAAcwE,QAAU,EAC/CpM,EAAQzE,KAAKgD,IAAIyB,OAAS,GAC9BvB,GAAWmI,IAAI,aAAcuF,EAAa,MAAMvF,IAAI,QAAS5G,EAAQ,MAGrEkM,EAAUpN,OAAOL,GACjBlD,KAAKqE,WAAY,EAIrBrE,KAAKsE,OAAQ,IAIjBF,KAAM,WACEpE,KAAK8Q,eAELJ,aAAa1Q,KAAK8Q,cAGtB,IAAI5N,GAAalD,KAAKkD,UACjBlD,MAAKsE,QAIVpB,EAAWkB,OACXpE,KAAKsE,OAAQ,KAgCrBC,EAAKqF,WACDC,YAAatF,EAGbG,SAAU,SAAkBZ,GACxB,GAAIpB,GAAS1C,KAAK0C,OAEdqO,EAAiBrO,EAAOoN,UAAUkB,2BAClCtO,GAAOuO,UAAUnE,MAAMiE,IAM3BrO,EAAOuN,IAAIC,GAAG,cAAepM,IAIjCuM,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,MACb8N,EAAM,MACNS,EAAWxO,EAAOuN,IAAIkB,kBAAkB,cACxCV,GAAIpJ,KAAK6J,IACTlR,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,iBAkC9BzG,EAASiF,WACLC,YAAalF,EAGbD,SAAU,SAAkBZ,GACX9D,KAAK0C,OACXuN,IAAIC,GAAG,WAAYpM,KAuClCc,EAASgF,WACLC,YAAajF,EAEbF,SAAU,SAAkBZ,GACX9D,KAAK0C,OACXuN,IAAIC,GAAG,WAAYpM,IAQlC,IAAIsN,GAAU,aAGVC,IASJpM,GAAM2E,WACFC,YAAa5E,EAGbyG,KAAM,WACF,GAAIzI,GAAQjD,KAER+C,EAAO/C,KAAK+C,IAChB,MAAIsO,EAAqBjQ,QAAQ2B,IAAS,GAA1C,CAKA,GAAIL,GAASK,EAAKL,OACd4O,EAAQ/P,EAAE,QACV0H,EAAqBvG,EAAOuG,mBAC5BjG,EAAMhD,KAAKgD,IAGXE,EAAa3B,EAAE,2CACfkD,EAAQzB,EAAIyB,OAAS,GACzBvB,GAAWmI,IAAI,QAAS5G,EAAQ,MAAM4G,IAAI,eAAgB,EAAI5G,GAAS,EAAI,KAG3E,IAAI8M,GAAYhQ,EAAE,iDAClB2B,GAAWK,OAAOgO,GAClBA,EAAUvN,GAAG,QAAS,WAClBf,EAAMmB,QAIV,IAAIoN,GAAqBjQ,EAAE,yCACvBkQ,EAAuBlQ,EAAE,4CAC7B2B,GAAWK,OAAOiO,GAAoBjO,OAAOkO,EAG7C,IAAIZ,GAAS7N,EAAI6N,MACbA,IACAY,EAAqBpG,IAAI,SAAUwF,EAAS,MAAMxF,IAAI,aAAc,OAIxE,IAAIqG,GAAO1O,EAAI0O,SACXC,KACAC,IACJF,GAAK9N,QAAQ,SAAUiO,EAAKC,GACxB,GAAKD,EAAL,CAGA,GAAIrE,GAAQqE,EAAIrE,OAAS,GACrBuE,EAAMF,EAAIE,KAAO,EAGrBvE,GAAQnK,EAAYX,EAAQ8K,GAC5BuE,EAAM1O,EAAYX,EAAQqP,EAG1B,IAAI5O,GAAS5B,EAAE,wBAA0BiM,EAAQ,QACjDgE,GAAmBjO,OAAOJ,EAC1B,IAAI6O,GAAWzQ,EAAEwQ,EACjBN,GAAqBlO,OAAOyO,GAG5B7O,EAAO8O,OAASH,EAChBH,EAAYrK,KAAKnE,GACjByO,EAActK,KAAK0K,GAGF,IAAbF,GACA3O,EAAON,SAAU,EACjBM,EAAOG,SAAS,eAEhB0O,EAAS5N,OAIbjB,EAAOa,GAAG,QAAS,SAAUC,GACrBd,EAAON,UAIX8O,EAAY/N,QAAQ,SAAUT,GAC1BA,EAAON,SAAU,EACjBM,EAAOiI,YAAY,gBAEvBwG,EAAchO,QAAQ,SAAUoO,GAC5BA,EAAS5N,SAIbjB,EAAON,SAAU,EACjBM,EAAOG,SAAS,cAChB0O,EAAStG,aAKjBxI,EAAWc,GAAG,QAAS,SAAUC,GAE7BA,EAAEiO,oBAENZ,EAAMtN,GAAG,QAAS,SAAUC,GACxBhB,EAAMmB,SAIV6E,EAAmB1F,OAAOL,GAG1BwO,EAAK9N,QAAQ,SAAUiO,EAAKzH,GACxB,GAAKyH,EAAL,EAGaA,EAAIM,YACVvO,QAAQ,SAAUwO,GACrB,GAAI3R,GAAW2R,EAAM3R,SACjBmC,EAAOwP,EAAMxP,KACblB,EAAK0Q,EAAM1Q,IAAM0P,CACNQ,GAAcxH,GACpBoC,KAAK/L,GAAUuD,GAAGpB,EAAM,SAAUqB,GACvCA,EAAEiO,kBACexQ,EAAGuC,IAGhBhB,EAAMmB,aAOtB,IAAIiO,GAAUnP,EAAWsJ,KAAK,4BAC1B6F,GAAQhR,QACRgR,EAAQlI,IAAI,GAAGuC,QAInB1M,KAAKkD,WAAaA,EAGlBlD,KAAKsS,mBAELjB,EAAqB/J,KAAKvE,KAI9BqB,KAAM,WACF,GAAIrB,GAAO/C,KAAK+C,KACZG,EAAalD,KAAKkD,UAClBA,IACAA,EAAW4I,SAIfuF,EAAuBA,EAAqBnG,OAAO,SAAUlJ,GACzD,MAAIA,KAASe,KASrBuP,iBAAkB,WACTjB,EAAqBhQ,QAG1BgQ,EAAqBzN,QAAQ,SAAUb,GACnC,GAAIwP,GAAQxP,EAAKwP,SACbA,GAAMnO,MACNmO,EAAMnO,WAoBtBc,EAAK0E,WACDC,YAAa3E,EAGbzB,QAAS,SAAiBQ,GACtB,GAAIvB,GAAS1C,KAAK0C,OACd8P,MAAY,EAEhB,IAAIxS,KAAK6C,QAAS,CAGd,KADA2P,EAAY9P,EAAOoN,UAAUkB,6BAEzB,MAGJtO,GAAOoN,UAAU2C,kBAAkBD,GACnC9P,EAAOoN,UAAUM,mBAEjBpQ,KAAK0S,aAAaF,EAAU/F,OAAQ+F,EAAUpK,KAAK,aAG/C1F,GAAOoN,UAAUC,mBAEjB/P,KAAK0S,aAAa,GAAI,IAGtB1S,KAAK0S,aAAahQ,EAAOoN,UAAU6C,mBAAoB,KAMnED,aAAc,SAAsBjG,EAAMsB,GACtC,GAAI9K,GAAQjD,KAGR4S,EAAc3Q,EAAU,cACxB4Q,EAAc5Q,EAAU,cACxB6Q,EAAU7Q,EAAU,UACpB8Q,EAAW9Q,EAAU,WAGrB+Q,EAAgBhT,KAAK6C,QAAU,eAAiB,OAGhD0P,EAAQ,GAAItN,GAAMjF,MAClByE,MAAO,IAEPiN,OAEIlE,MAAO,KAEPuE,IAAK,iDAAmDc,EAAc,sCAAwCpG,EAAO,uEAA6FmG,EAAc,sCAAwC7E,EAAO,kJAAoJ+E,EAAU,4EAAwFC,EAAW,uCAAyCC,EAAgB,sFAEzkBb,SAGI1R,SAAU,IAAMqS,EAChBlQ,KAAM,QACNlB,GAAI,WAEA,GAAIuR,GAAQ1R,EAAE,IAAMqR,GAChBM,EAAQ3R,EAAE,IAAMsR,GAChB9E,EAAOkF,EAAMpI,MACb4B,EAAOyG,EAAMrI,KAIjB,OAHA5H,GAAMkQ,YAAY1G,EAAMsB,IAGjB,KAKXtN,SAAU,IAAMsS,EAChBnQ,KAAM,QACNlB,GAAI,WAKA,MAHAuB,GAAMmQ,YAGC,QAQvBb,GAAM7G,OAGN1L,KAAKuS,MAAQA,GAIjBa,SAAU,WACN,GAAKpT,KAAK6C,QAAV,CAGA,GAAIH,GAAS1C,KAAK0C,MAElB,IADqBA,EAAOoN,UAAUkB,4BACtC,CAGA,GAAIqC,GAAgB3Q,EAAOoN,UAAU6C,kBACrCjQ,GAAOuN,IAAIC,GAAG,aAAc,SAAWmD,EAAgB,cAI3DF,YAAa,SAAqB1G,EAAMsB,GACpC,GAAIrL,GAAS1C,KAAK0C,OACdmC,EAASnC,EAAOmC,OAChBiJ,EAAYjJ,EAAOiJ,UACnBwF,GAAc,CACdxF,IAAkC,kBAAdA,KACpBwF,EAAcxF,EAAUrB,EAAMsB,KAEd,IAAhBuF,EACA5Q,EAAOuN,IAAIC,GAAG,aAAc,YAAcnC,EAAO,qBAAuBtB,EAAO,QAE/E8G,MAAMD,IAKdjD,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,MACb6Q,EAAiB9Q,EAAOoN,UAAUkB,2BACjCwC,KAGgC,MAAjCA,EAAejH,eACfvM,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,kBAmB9BjG,EAAOyE,WACHC,YAAa1E,EAGb1B,QAAS,SAAiBQ,GAGtB,GAAIvB,GAAS1C,KAAK0C,OACdmN,EAAcnN,EAAOoN,UAAUC,kBAE/BF,IAEAnN,EAAOoN,UAAUE,mBAIrBtN,EAAOuN,IAAIC,GAAG,UAEVL,IAEAnN,EAAOoN,UAAUK,gBACjBzN,EAAOoN,UAAUM,qBAKzBC,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,KACbD,GAAOuN,IAAIK,kBAAkB,WAC7BtQ,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,iBAmB9BhG,EAAKwE,WACDC,YAAazE,EAGb3B,QAAS,SAAiBQ,GAGTjE,KAAK0C,OAGXuN,IAAIC,GAAG,UAkBtB7K,EAAcuE,WACVC,YAAaxE,EAGb5B,QAAS,SAAiBQ,GAGtB,GAAIvB,GAAS1C,KAAK0C,OACdmN,EAAcnN,EAAOoN,UAAUC,kBAE/BF,IAEAnN,EAAOoN,UAAUE,mBAIrBtN,EAAOuN,IAAIC,GAAG,iBAEVL,IAEAnN,EAAOoN,UAAUK,gBACjBzN,EAAOoN,UAAUM,qBAKzBC,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,KACbD,GAAOuN,IAAIK,kBAAkB,kBAC7BtQ,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,iBAmB9B9F,EAAUsE,WACNC,YAAavE,EAGb7B,QAAS,SAAiBQ,GAGtB,GAAIvB,GAAS1C,KAAK0C,OACdmN,EAAcnN,EAAOoN,UAAUC,kBAE/BF,IAEAnN,EAAOoN,UAAUE,mBAIrBtN,EAAOuN,IAAIC,GAAG,aAEVL,IAEAnN,EAAOoN,UAAUK,gBACjBzN,EAAOoN,UAAUM,qBAKzBC,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,KACbD,GAAOuN,IAAIK,kBAAkB,cAC7BtQ,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,iBAmB9B7F,EAAKqE,WACDC,YAAatE,EAGb9B,QAAS,SAAiBQ,GAGTjE,KAAK0C,OAGXuN,IAAIC,GAAG,UAgCtB1K,EAAKoE,WACDC,YAAarE,EAGbd,SAAU,SAAkBZ,GACxB,GAAIpB,GAAS1C,KAAK0C,OACduO,EAAYvO,EAAOuO,SAEvB,IADAvO,EAAOoN,UAAUM,oBACb1N,EAAOuN,IAAIK,kBAAkBxM,GAAjC,CAGApB,EAAOuN,IAAIC,GAAGpM,EAGd,IAAIiN,GAAiBrO,EAAOoN,UAAUkB,2BAItC,IAHqC,OAAjCD,EAAexE,gBACfwE,EAAiBA,EAAehF,WAEkB,IAAlD,WAAW1E,KAAK0J,EAAexE,iBAG/BwE,EAAejE,MAAMmE,GAAzB,CAIA,GAAIwC,GAAU1C,EAAehF,QACzB0H,GAAQ3G,MAAMmE,KAKlBF,EAAe5D,YAAYsG,GAC3BA,EAAQ3H,aAIZuE,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,KACbD,GAAOuN,IAAIK,kBAAkB,wBAA0B5N,EAAOuN,IAAIK,kBAAkB,sBACpFtQ,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,iBAiC9B3F,EAAQmE,WACJC,YAAapE,EAGbf,SAAU,SAAkBZ,GACX9D,KAAK0C,OACXuN,IAAIC,GAAGpM,KAsCtB4B,EAAUkE,WACNC,YAAanE,EAGbhB,SAAU,SAAkBZ,GACX9D,KAAK0C,OACXuN,IAAIC,GAAG,YAAapM,KAsCnC+B,EAAU+D,WACNC,YAAahE,EAGbnB,SAAU,SAAkBZ,GACX9D,KAAK0C,OACXuN,IAAIC,GAAG,YAAapM,KAkBnCgC,EAAM8D,WACFC,YAAa/D,EAEbrC,QAAS,SAAiBQ,GACtB,GAAIvB,GAAS1C,KAAK0C,OACdqO,EAAiBrO,EAAOoN,UAAUkB,4BAClClJ,EAAWiJ,EAAexE,aAE9B,KAAKgD,EAAGK,OAQJ,YAPiB,eAAb9H,EAEApF,EAAOuN,IAAIC,GAAG,cAAe,OAG7BxN,EAAOuN,IAAIC,GAAG,cAAe,gBAMrC,IAAIzC,OAAU,GACViG,MAAc,EAClB,IAAiB,MAAb5L,EAMA,MAJA2F,GAAUsD,EAAetE,OACzBiH,EAAcnS,EAAE,eAAiBkM,EAAU,iBAC3CiG,EAAYvG,YAAY4D,OACxBA,GAAejF,QAGF,gBAAbhE,IAEA2F,EAAUsD,EAAetE,OACzBiH,EAAcnS,EAAE,MAAQkM,EAAU,QAClCiG,EAAYvG,YAAY4D,GACxBA,EAAejF,WAIvBuE,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,MACb8N,EAAM,gBACNS,EAAWxO,EAAOuN,IAAIkB,kBAAkB,cACxCV,GAAIpJ,KAAK6J,IACTlR,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,iBAmB9BrF,EAAK6D,WACDC,YAAa9D,EAEbtC,QAAS,SAAiBQ,GACtB,GAAIvB,GAAS1C,KAAK0C,OACdiR,EAAajR,EAAOoN,UAAU8D,wBAC9BC,EAAWnR,EAAOoN,UAAUgE,sBAC5BjE,EAAcnN,EAAOoN,UAAUC,mBAC/BsD,EAAgB3Q,EAAOoN,UAAU6C,mBACjCoB,MAAQ,EAEZ,OAAKJ,GAAW7G,MAAM+G,GAKjBhE,OAUD7P,KAAK6C,QAEL7C,KAAK0S,aAAaiB,EAAWzT,QAG7BF,KAAK0S,iBAbLqB,EAAQxS,EAAE,SAAW8R,EAAgB,WACrC3Q,EAAOuN,IAAIC,GAAG,aAAc6D,GAC5BrR,EAAOoN,UAAU2C,kBAAkBsB,GAAO,OAC1CrR,GAAOoN,UAAUM,wBARjB1N,GAAOoN,UAAUM,oBAsBzBsC,aAAc,SAAsB5O,GAChC,GAAIb,GAAQjD,IAGZ8D,GAAQA,GAAS,EACjB,IAAIlB,GAAQkB,EAAgB,OAAR,MAChBkQ,EAAS/R,EAAU,SACnBgS,EAAQhS,EAAU,OAElBsQ,EAAQ,GAAItN,GAAMjF,MAClByE,MAAO,IAEPiN,OAEIlE,MAAO,OAEPuE,IAAK,gDAAkDiC,EAAS,4BAA8BlQ,EAAQ,oHAAsHmQ,EAAQ,yFAEpO9B,SAGI1R,SAAU,IAAMwT,EAChBrR,KAAM,QACNlB,GAAI,WACA,GAAIwR,GAAQ3R,EAAE,IAAMyS,GAChBvH,EAAOyG,EAAMrI,OAASqI,EAAMhT,MAWhC,OAVAuM,GAAOlK,EAAkBkK,GACZ,QAAT7J,EAEAK,EAAMiR,YAAYzH,GAGlBxJ,EAAMkR,YAAY1H,IAIf,QAQvB8F,GAAM7G,OAGN1L,KAAKuS,MAAQA,GAIjB2B,YAAa,SAAqBpQ,GACjB9D,KAAK0C,OACXuN,IAAIC,GAAG,aAAc,cAAgBpM,EAAQ,6BAIxDqQ,YAAa,SAAqBrQ,GAC9B,GAAIpB,GAAS1C,KAAK0C,OACd8Q,EAAiB9Q,EAAOoN,UAAUkB,2BACjCwC,KAGLA,EAAetT,KAAK4D,GACpBpB,EAAOoN,UAAUM,qBAIrBC,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,MACb6Q,EAAiB9Q,EAAOoN,UAAUkB,2BACtC,IAAKwC,EAAL,CAGA,GAAIY,GAAcZ,EAAezH,QACI,UAAjCyH,EAAejH,eAA0D,QAA9B6H,EAAY7H,eACvDvM,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,kBAmB9BpF,EAAS4D,WACLC,YAAa7D,EAEbvC,QAAS,WACLzD,KAAK0S,gBAGTA,aAAc,WACV,GAAIzP,GAAQjD,KAER0C,EAAS1C,KAAK0C,OACdmC,EAASnC,EAAOmC,OAEhB0I,EAAW1I,EAAO0I,aAGlB8G,IACJ9G,GAAS3J,QAAQ,SAAU0Q,GACvB,GAAIC,GAAWD,EAAS1R,KACpB6K,EAAU6G,EAAS7G,YAGnB+G,EAAW,EAGE,WAAbD,GACA9G,EAAQ7J,QAAQ,SAAU5B,GAClBA,IACAwS,GAAY,0BAA4BxS,EAAO,aAK1C,UAAbuS,GACA9G,EAAQ7J,QAAQ,SAAU5B,GACtB,GAAI2L,GAAM3L,EAAK2L,IACXD,EAAM1L,EAAK0L,GACXC,KAEA6G,GAAY,oCAAsC7G,EAAM,UAAYD,EAAM,6BAKtF2G,EAAU/M,MACNkG,MAAO8G,EAAS9G,MAChBuE,IAAK,uCAAyCyC,EAAW,SACzDrC,SACI1R,SAAU,gBACVmC,KAAM,QACNlB,GAAI,SAAYuC,GACZ,GAAIwG,GAASxG,EAAEwG,OACXgK,EAAUlT,EAAEkJ,GACZ3C,EAAW2M,EAAQlI,cAEnBmI,MAAa,EAWjB,OARIA,GAFa,QAAb5M,EAEa2M,EAAQ1I,SAAS7L,OAGjB,SAAWuU,EAAQvU,OAAS,UAG7C+C,EAAM0R,QAAQD,IAEP,QAMvB,IAAInC,GAAQ,GAAItN,GAAMjF,MAClByE,MAAO,IACPoM,OAAQ,IAERa,KAAM2C,GAIV9B,GAAM7G,OAGN1L,KAAKuS,MAAQA,GAIjBoC,QAAS,SAAiBC,GACT5U,KAAK0C,OACXuN,IAAIC,GAAG,aAAc0E,KAkBpC3O,EAAM2D,WACFC,YAAa5D,EAEbxC,QAAS,WACDzD,KAAK6C,QAEL7C,KAAK6U,mBAGL7U,KAAK8U,sBAKbA,mBAAoB,WAChB,GAAI7R,GAAQjD,KAGR+U,EAAc9S,EAAU,OACxB+S,EAAa/S,EAAU,OACvBgT,EAAahT,EAAU,OAEvBsQ,EAAQ,GAAItN,GAAMjF,MAClByE,MAAO,IAEPiN,OAEIlE,MAAO,OAEPuE,IAAK,sJAAkKiD,EAAa,0IAAiJC,EAAa,wOAA8PF,EAAc,0FAE9lB5C,SAEI1R,SAAU,IAAMsU,EAChBnS,KAAM,QACNlB,GAAI,WACA,GAAIwT,GAASC,SAAS5T,EAAE,IAAMyT,GAAYnK,OACtCuK,EAASD,SAAS5T,EAAE,IAAM0T,GAAYpK,MAQ1C,OANIqK,IAAUE,GAAUF,EAAS,GAAKE,EAAS,GAE3CnS,EAAM0R,QAAQO,EAAQE,IAInB,QAQvB7C,GAAM7G,OAGN1L,KAAKuS,MAAQA,GAIjBoC,QAAS,SAAiBO,EAAQE,GAE9B,GAAIC,OAAI,GACJC,MAAI,GACJpV,EAAO,iEACX,KAAKmV,EAAI,EAAGA,EAAIH,EAAQG,IAAK,CAEzB,GADAnV,GAAQ,OACE,IAANmV,EACA,IAAKC,EAAI,EAAGA,EAAIF,EAAQE,IACpBpV,GAAQ,sBAGZ,KAAKoV,EAAI,EAAGA,EAAIF,EAAQE,IACpBpV,GAAQ,iBAGhBA,IAAQ,QAEZA,GAAQ,qBAGR,IAAIwC,GAAS1C,KAAK0C,MAClBA,GAAOuN,IAAIC,GAAG,aAAchQ,GAG5BwC,EAAOuN,IAAIC,GAAG,wBAAwB,GACtCxN,EAAOuN,IAAIC,GAAG,4BAA4B,IAI9C2E,iBAAkB,WACd,GAAIU,GAASvV,KAGTwV,EAAcvT,EAAU,WACxBwT,EAAcxT,EAAU,WACxByT,EAAczT,EAAU,WACxB0T,EAAc1T,EAAU,WACxB2T,EAAgB3T,EAAU,YAGlB,IAAIgD,GAAMjF,MAClByE,MAAO,IAEPiN,OAEIlE,MAAO,OAEPuE,IAAK,4LAA8LyD,EAAc,wEAAyFE,EAAc,4EAA6FD,EAAc,wEAAyFE,EAAc,wKAAyLC,EAAgB,+FAEntBzD,SAEI1R,SAAU,IAAM+U,EAChB5S,KAAM,QACNlB,GAAI,WAGA,MAFA6T,GAAOM,WAEA,KAIXpV,SAAU,IAAMgV,EAChB7S,KAAM,QACNlB,GAAI,WAGA,MAFA6T,GAAOO,WAEA,KAIXrV,SAAU,IAAMiV,EAChB9S,KAAM,QACNlB,GAAI,WAGA,MAFA6T,GAAOQ,WAEA,KAIXtV,SAAU,IAAMkV,EAChB/S,KAAM,QACNlB,GAAI,WAGA,MAFA6T,GAAOS,WAEA,KAIXvV,SAAU,IAAMmV,EAChBhT,KAAM,QACNlB,GAAI,WAGA,MAFA6T,GAAOU,aAEA,SAMjBvK,QAIVwK,iBAAkB,WACd,GAAIrV,MACA6B,EAAS1C,KAAK0C,OACd8Q,EAAiB9Q,EAAOoN,UAAUkB,2BACtC,IAAKwC,EAAL,CAGA,GAAI1L,GAAW0L,EAAejH,aAC9B,IAAiB,OAAbzE,GAAkC,OAAbA,EAAzB,CAKA,GAAIqO,GAAM3C,EAAezH,SACrBqK,EAAOD,EAAI5V,WACX8V,EAAWD,EAAK/U,MACpB+U,GAAKxS,QAAQ,SAAU0S,EAAIlM,GACvB,GAAIkM,IAAO9C,EAAe,GAOtB,MALA3S,GAAOyV,IACHlM,MAAOA,EACPN,KAAMwM,EACNjV,OAAQgV,IAEL,GAKf,IAAIE,GAASJ,EAAIpK,SACbyK,EAAOD,EAAOhW,WACdkW,EAAWD,EAAKnV,MAcpB,OAbAmV,GAAK5S,QAAQ,SAAU8S,EAAItM,GACvB,GAAIsM,IAAOP,EAAI,GAOX,MALAtV,GAAO6V,IACHtM,MAAOA,EACPN,KAAM4M,EACNrV,OAAQoV,IAEL,IAKR5V,KAIXgV,QAAS,WAEL,GAAIc,GAAe3W,KAAKkW,kBACxB,IAAKS,EAAL,CAGA,GAAIC,GAASD,EAAaD,GACtBG,EAAatV,EAAEqV,EAAO9M,MACtBgN,EAASH,EAAaL,GACtBD,EAAWS,EAAOzV,OAGlB0V,EAAQ3W,SAASC,cAAc,MAC/B0R,EAAM,GACNzQ,MAAI,EACR,KAAKA,EAAI,EAAGA,EAAI+U,EAAU/U,IACtByQ,GAAO,iBAEXgF,GAAMzW,UAAYyR,EAElBxQ,EAAEwV,GAAO5J,YAAY0J,KAIzBf,QAAS,WAEL,GAAIa,GAAe3W,KAAKkW,kBACxB,IAAKS,EAAL,CAGA,GAAIC,GAASD,EAAaD,GACtBI,EAASH,EAAaL,GACtBU,EAAUF,EAAO1M,KACJ7I,GAAEqV,EAAO9M,MACCiC,SACNxL,WAGhBqD,QAAQ,SAAU8S,GACnB,GAAIP,GAAM5U,EAAEmV,GACRN,EAAOD,EAAI5V,WACX0W,EAAab,EAAKjM,IAAI6M,GACtB3O,EAAO4O,EAAW1K,cAAcxE,aAIpCxG,GADYnB,SAASC,cAAcgI,IAC1B8E,YAAY8J,OAK7BlB,QAAS,WAEL,GAAIY,GAAe3W,KAAKkW,kBACxB,IAAKS,EAAL,CAIiBpV,EADJoV,EAAaD,GACA5M,MACfgC,WAIfkK,QAAS,WAEL,GAAIW,GAAe3W,KAAKkW,kBACxB,IAAKS,EAAL,CAGA,GAAIC,GAASD,EAAaD,GACtBI,EAASH,EAAaL,GACtBU,EAAUF,EAAO1M,KACJ7I,GAAEqV,EAAO9M,MACCiC,SACNxL,WAGhBqD,QAAQ,SAAU8S,GACTnV,EAAEmV,GACGnW,WACO4J,IAAI6M,GAEflL,aAKnBmK,UAAW,WACP,GAAIvT,GAAS1C,KAAK0C,OACd8Q,EAAiB9Q,EAAOoN,UAAUkB,2BACtC,IAAKwC,EAAL,CAGA,GAAI0D,GAAS1D,EAAe7G,YAAY,QACnCuK,IAGLA,EAAOpL,WAIXuE,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,MACb6Q,EAAiB9Q,EAAOoN,UAAUkB,2BACtC,IAAKwC,EAAL,CAGA,GAAI1L,GAAW0L,EAAejH,aACb,QAAbzE,GAAkC,OAAbA,GACrB9H,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,kBAmB9BlF,EAAM0D,WACFC,YAAa3D,EAEbzC,QAAS,WACLzD,KAAK0S,gBAGTA,aAAc,WACV,GAAIzP,GAAQjD,KAGRmX,EAAYlV,EAAU,YACtBgS,EAAQhS,EAAU,OAGlBsQ,EAAQ,GAAItN,GAAMjF,MAClByE,MAAO,IAEPiN,OAEIlE,MAAO,OAEPuE,IAAK,6CAA+CoF,EAAY,mLAAyMlD,EAAQ,0FAEjR9B,SACI1R,SAAU,IAAMwT,EAChBrR,KAAM,QACNlB,GAAI,WACA,GAAIwR,GAAQ3R,EAAE,IAAM4V,GAChBtM,EAAMqI,EAAMrI,MAAM1J,MAWtB,OANI0J,IAEA5H,EAAM0R,QAAQ9J,IAIX,QAQvB0H,GAAM7G,OAGN1L,KAAKuS,MAAQA,GAIjBoC,QAAS,SAAiB9J,GACT7K,KAAK0C,OACXuN,IAAIC,GAAG,aAAcrF,EAAM,iBAoB1C1E,EAAMyD,WACFC,YAAa1D,EAEb1C,QAAS,WACQzD,KAAK0C,OACEmC,OACTyK,QAGPtP,KAAK6C,QACL7C,KAAK6U,mBAEL7U,KAAK8U,uBAIbD,iBAAkB,WACd,GAAInS,GAAS1C,KAAK0C,OAGd0U,EAAUnV,EAAU,YACpBoV,EAAUpV,EAAU,YACpBqV,EAAWrV,EAAU,aACrBsV,EAAStV,EAAU,WAGnBuV,IACAhK,MAAO,OACPuE,IAAK,mSAA8TqF,EAAU,oEAAsEC,EAAU,oEAAsEC,EAAW,yJAA2JC,EAAS,uFAClpBpF,SACI1R,SAAU,IAAM2W,EAChBxU,KAAM,QACNlB,GAAI,WACA,GAAI+V,GAAO/U,EAAOgV,YAKlB,OAJID,IACAA,EAAKpM,IAAI,YAAa,QAGnB,KAGX5K,SAAU,IAAM4W,EAChBzU,KAAM,QACNlB,GAAI,WACA,GAAI+V,GAAO/U,EAAOgV,YAKlB,OAJID,IACAA,EAAKpM,IAAI,YAAa,QAGnB,KAGX5K,SAAU,IAAM6W,EAChB1U,KAAM,QACNlB,GAAI,WACA,GAAI+V,GAAO/U,EAAOgV,YAKlB,OAJID,IACAA,EAAKpM,IAAI,YAAa,SAGnB,KAGX5K,SAAU,IAAM8W,EAChB3U,KAAM,QACNlB,GAAI,WACA,GAAI+V,GAAO/U,EAAOgV,YAKlB,OAJID,IACAA,EAAK3L,UAGF,OAMfyG,EAAQ,GAAItN,GAAMjF,MAClByE,MAAO,IACPiN,KAAM8F,GAEVjF,GAAM7G,OAGN1L,KAAKuS,MAAQA,GAGjBuC,mBAAoB,WAChB,GAAIpS,GAAS1C,KAAK0C,OACdiV,EAAYjV,EAAOiV,UACnB9S,EAASnC,EAAOmC,OAGhB+S,EAAc3V,EAAU,cACxB4V,EAAW5V,EAAU,WACrB6V,EAAY7V,EAAU,YACtB8V,EAAY9V,EAAU,YAGtBuV,IACAhK,MAAO,OACPuE,IAAK,oEAAsE6F,EAAc,oMAAsMC,EAAW,sJAC1S1F,SAEI1R,SAAU,IAAMmX,EAChBhV,KAAM,QACNlB,GAAI,WACA,GAAIsW,GAAQzW,EAAE,IAAMsW,GAChBI,EAAWD,EAAM,EACrB,KAAIC,EAIA,OAAO,CAHPA,GAASC,WAQjBzX,SAAU,IAAMoX,EAChBjV,KAAM,SACNlB,GAAI,WACA,GAAIsW,GAAQzW,EAAE,IAAMsW,GAChBI,EAAWD,EAAM,EACrB,KAAKC,EAED,OAAO,CAIX,IAAIE,GAAWF,EAAShJ,KAMxB,OALIkJ,GAAS9W,QACTsW,EAAUA,UAAUQ,IAIjB,OAKf3K,MAAO,OACPuE,IAAK,yCAA2C+F,EAAY,sJAA4KC,EAAY;yRACpP5F,SACI1R,SAAU,IAAMsX,EAChBnV,KAAM,QACNlB,GAAI,WACA,GAAI0W,GAAW7W,EAAE,IAAMuW,GACnBxJ,EAAM8J,EAASvN,MAAM1J,MAOzB,OALImN,IACAqJ,EAAUU,cAAc/J,IAIrB,OAOfgK,MACCzT,EAAO2J,qBAAuB3J,EAAO0T,iBAAmB1T,EAAO2T,kBAAoB7R,OAAO8R,YAE3FH,EAAiBhR,KAAKkQ,EAAW,IAEjC3S,EAAOuJ,aAEPkK,EAAiBhR,KAAKkQ,EAAW,GAIrC,IAAIjF,GAAQ,GAAItN,GAAMjF,MAClByE,MAAO,IACPiN,KAAM4G,GAEV/F,GAAM7G,OAGN1L,KAAKuS,MAAQA,GAIjBlC,gBAAiB,SAAyBpM,GACtC,GAAIvB,GAAS1C,KAAK0C,OACdC,EAAQ3C,KAAK2C,KACbD,GAAOgV,cACP1X,KAAK6C,SAAU,EACfF,EAAMW,SAAS,gBAEftD,KAAK6C,SAAU,EACfF,EAAMyI,YAAY,gBAU9B,IAAIsN,KAEJA,GAAiBC,KAAOlW,EAExBiW,EAAiBE,KAAOrU,EAExBmU,EAAiBG,SAAWlU,EAE5B+T,EAAiB1T,SAAWJ,EAE5B8T,EAAiB3K,KAAO7I,EAExBwT,EAAiBI,OAAS3T,EAE1BuT,EAAiBK,KAAO3T,EAExBsT,EAAiBM,cAAgB3T,EAEjCqT,EAAiBO,UAAY3T,EAE7BoT,EAAiBQ,KAAO3T,EAExBmT,EAAiBlV,KAAOgC,EAExBkT,EAAiBS,QAAU1T,EAE3BiT,EAAiBU,UAAY1T,EAE7BgT,EAAiBW,UAAYxT,EAE7B6S,EAAiBY,MAAQxT,EAEzB4S,EAAiBa,KAAOxT,EAExB2S,EAAiBc,SAAWxT,EAE5B0S,EAAiBe,MAAQxT,EAEzByS,EAAiBgB,MAAQxT,EAEzBwS,EAAiBiB,MAAQxT,EAYzBE,EAAMuD,WACFC,YAAaxD,EAGbuT,KAAM,WACF,GAAI3W,GAAQjD,KAER0C,EAAS1C,KAAK0C,SACLA,EAAOmC,YACKyB,WAGb1C,QAAQ,SAAUiW,GAC1B,GAAIC,GAAkBpB,EAAiBmB,EACnCC,IAA8C,kBAApBA,KAE1B7W,EAAMqD,MAAMuT,GAAW,GAAIC,GAAgBpX,MAKnD1C,KAAK+Z,gBAGL/Z,KAAKga,cAITD,cAAe,WACX,GAAIrX,GAAS1C,KAAK0C,OACduX,EAAevX,EAAOuX,aACtB3T,EAAQtG,KAAKsG,MACbzB,EAASnC,EAAOmC,OAEhB+I,EAAS/I,EAAO+I,OAAS,CAC7BpM,GAAW8E,EAAO,SAAU3E,EAAKoB,GAC7B,GAAIJ,GAAQI,EAAKJ,KACbA,KAEAA,EAAM0I,IAAI,UAAWuC,GACrBqM,EAAa1W,OAAOZ,OAMhCqX,WAAY,WACR,GAAI1T,GAAQtG,KAAKsG,MACb5D,EAAS1C,KAAK0C,MAClBlB,GAAW8E,EAAO,SAAU3E,EAAKoB,GAC7B,GAAIH,GAAOG,EAAKH,IAChB,IAAKA,EAAL,CAGA,GAAID,GAAQI,EAAKJ,MACb6B,EAAWzB,EAAKyB,QACRzB,GAAKwP,KAGJ,WAAT3P,GAAoBG,EAAKU,SACzBd,EAAMqB,GAAG,QAAS,SAAUC,GACW,MAA/BvB,EAAOoN,UAAUoK,YAGrBnX,EAAKU,QAAQQ,KAKR,aAATrB,GAAuB4B,GACvB7B,EAAMqB,GAAG,aAAc,SAAUC,GACM,MAA/BvB,EAAOoN,UAAUoK,aAIrB1V,EAASsM,cAAgB3M,WAAW,WAChCK,EAASkH,QACV,QACJ1H,GAAG,aAAc,SAAUC,GAE1BO,EAASN,cAAgBC,WAAW,WAChCK,EAASJ,QACV,KAKE,UAATxB,GAAoBG,EAAKU,SACzBd,EAAMqB,GAAG,QAAS,SAAUC,GACxBA,EAAEiO,kBACiC,MAA/BxP,EAAOoN,UAAUoK,YAIrBnX,EAAKU,QAAQQ,SAO7BkW,aAAc,WAEV3Y,EADYxB,KAAKsG,MACC,SAAU3E,EAAKoB,GACzBA,EAAKsN,iBACLlM,WAAW,WACPpB,EAAKsN,mBACN,SAkJnB9H,EAAKqB,WACDC,YAAatB,EAGbqR,KAAM,WAEF5Z,KAAKga,cAITI,MAAO,WACHpa,KAAKE,KAAK,gBAIdA,KAAM,SAAc2K,GAChB,GAAInI,GAAS1C,KAAK0C,OACduO,EAAYvO,EAAOuO,UACnB/Q,MAAO,EACX,IAAW,MAAP2K,EAIA,MAHA3K,GAAO+Q,EAAU/Q,OAEjBA,EAAOA,EAAKgB,QAAQ,WAAY,IACzBhB,CAEP+Q,GAAU/Q,KAAK2K,GAGfnI,EAAO2X,iBAKfC,QAAS,WAGL,MAAO9S,GAFMxH,KAAK0C,OACKuO,YAK3BxE,KAAM,SAAc5B,GAChB,GAAInI,GAAS1C,KAAK0C,OACduO,EAAYvO,EAAOuO,UACnBxE,MAAO,EACX,IAAW,MAAP5B,EAIA,MAHA4B,GAAOwE,EAAUxE,OAEjBA,EAAOA,EAAKvL,QAAQ,WAAY,IACzBuL,CAEPwE,GAAUxE,KAAK,MAAQ5B,EAAM,QAG7BnI,EAAO2X,iBAKf9W,OAAQ,SAAgBrD,GACpB,GAAIwC,GAAS1C,KAAK0C,MACFA,GAAOuO,UACb1N,OAAOhC,EAAErB,IAGnBwC,EAAO2X,iBAIXL,WAAY,WAERha,KAAKua,qBAGLva,KAAKwa,kBAGLxa,KAAKya,eAGLza,KAAK0a,eAGL1a,KAAK2a,aAGL3a,KAAK4a,aAGL5a,KAAK6a,eAITN,mBAAoB,WAKhB,QAASO,GAAU7W,GAEfvB,EAAOoN,UAAUgL,YAEjBpY,EAAO4D,MAAM6T,eARjB,GAAIzX,GAAS1C,KAAK0C,OACduO,EAAYvO,EAAOuO,SAUvBA,GAAUjN,GAAG,QAAS8W,GACtB7J,EAAUjN,GAAG,YAAa,SAAUC,GAEhCgN,EAAUjN,GAAG,aAAc8W,KAE/B7J,EAAUjN,GAAG,UAAW,SAAUC,GAC9B6W,IAEA7J,EAAUtG,IAAI,aAAcmQ,MAKpCN,gBAAiB,WAIb,QAASO,GAAahK,GAClB,GAAIiK,GAAKzZ,EAAE,cACXyZ,GAAGjO,aAAagE,GAChBrO,EAAOoN,UAAU2C,kBAAkBuI,GAAI,GACvCtY,EAAOoN,UAAUM,mBACjBW,EAAejF,SAInB,QAASmP,GAAQhX,GACb,GAAI8M,GAAiBrO,EAAOoN,UAAUkB,4BAClCoD,EAAcrD,EAAehF,QAEjC,IAA2B,sBAAvBqI,EAAYlU,OAIZ,WADA6a,GAAahK,EAIjB,IAAKqD,EAAYtH,MAAMmE,GAAvB,CAMiB,MADFF,EAAexE,gBAM1BwE,EAAetE,QAMnBsO,EAAahK,KAajB,QAASmK,GAAWjX,GAChB,GAAI8M,GAAiBrO,EAAOoN,UAAUkB,2BACtC,IAAKD,EAAL,CAGA,GAAIqD,GAAcrD,EAAehF,SAC7BoP,EAAoBpK,EAAexE,cACnC6O,EAAiBhH,EAAY7H,aAEjC,IAA0B,SAAtB4O,GAAmD,QAAnBC,GAK/B1Y,EAAOuN,IAAIoL,sBAAsB,cAAtC,CAMA,IAA8B,IAA1B3Y,EAAO4Y,eAAyB,CAGhC,GAAIN,GAAKzZ,EAAE,cASX,OARAyZ,GAAG7N,YAAYiH,GACf1R,EAAOoN,UAAU2C,kBAAkBuI,GAAI,GACvCtY,EAAOoN,UAAUM,mBAGjB1N,EAAO4Y,gBAAiB,MAExBrX,GAAEsX,iBAIN,GAAIC,GAAe9Y,EAAOoN,UAAUoK,WAAWuB,WAG/C/Y,GAAOuN,IAAIC,GAAG,aAAc,MAC5BxN,EAAOoN,UAAUgL,YACbpY,EAAOoN,UAAUoK,WAAWuB,cAAgBD,GAE5C9Y,EAAOuN,IAAIC,GAAG,aAAc,KAGhC,IAAIwL,GAAa3K,EAAe7Q,OAAOmB,MACnCqB,GAAOoN,UAAUoK,WAAWuB,YAAc,IAAMC,IAGhDhZ,EAAO4Y,gBAAiB,GAI5BrX,EAAEsX,mBA1GN,GAAI7Y,GAAS1C,KAAK0C,OACduO,EAAYvO,EAAOuO,SA0CvBA,GAAUjN,GAAG,QAAS,SAAUC,GACV,KAAdA,EAAE0X,SAKNV,EAAQhX,KA4DZgN,EAAUjN,GAAG,UAAW,SAAUC,GAC9B,GAAkB,KAAdA,EAAE0X,QAIF,YADAjZ,EAAO4Y,gBAAiB,EAI5BJ,GAAWjX,MAKnBwW,aAAc,WACV,GAAI/X,GAAS1C,KAAK0C,OACduO,EAAYvO,EAAOuO,SAEvBA,GAAUjN,GAAG,UAAW,SAAUC,GAC9B,GAAkB,IAAdA,EAAE0X,QAAN,CAIA,MAAgB,gBADF1K,EAAU/Q,OAAO6H,cAAc5G,WAGzC8C,GAAEsX,qBAFN,MAOJtK,EAAUjN,GAAG,QAAS,SAAUC,GAC5B,GAAkB,IAAdA,EAAE0X,QAAN,CAGA,GAAIX,OAAK,GACLY,EAAU3K,EAAU/Q,OAAO6H,cAAc5G,MAGxCya,IAAuB,SAAZA,IAEZZ,EAAKzZ,EAAE,gBACP0P,EAAU/Q,KAAK,IACf+Q,EAAU1N,OAAOyX,GACjBtY,EAAOoN,UAAU2C,kBAAkBuI,GAAI,GAAO,GAC9CtY,EAAOoN,UAAUM,wBAM7BsK,aAAc,WAWV,QAASmB,KACL,GAAIC,GAAMC,KAAKD,MACXE,GAAO,CAMX,OALIF,GAAMG,GAAa,MAEnBD,GAAO,GAEXC,EAAYH,EACLE,EAEX,QAASE,KACLD,EAAY,EArBhB,GAAIvZ,GAAS1C,KAAK0C,OACdmC,EAASnC,EAAOmC,OAChBoJ,EAAmBpJ,EAAOoJ,iBAC1BE,EAAkBtJ,EAAOsJ,gBACzBpH,EAAYlC,EAAOqJ,eACnB+C,EAAYvO,EAAOuO,UAInBgL,EAAY,CAgBhBhL,GAAUjN,GAAG,QAAS,SAAUC,GAC5B,IAAIsL,EAAGK,SAIH3L,EAAEsX,iBAIDM,KAAL,CAKA,GAAI7U,GAAYH,EAAa5C,EAAGgK,EAAkBlH,GAC9CL,EAAYH,EAAatC,EAC7ByC,GAAYA,EAAUxF,QAAQ,OAAQ,OAEtC,IAAI6P,GAAiBrO,EAAOoN,UAAUkB,2BACtC,IAAKD,EAAL,CAGA,GAAIjJ,GAAWiJ,EAAexE,aAG9B,IAAiB,SAAbzE,GAAoC,QAAbA,EAMvB,MALIqG,IAAmB3L,EAAW2L,KAE9BzH,EAAY,IAAMyH,EAAgBzH,IAAc,SAEpDhE,GAAOuN,IAAIC,GAAG,aAAc,MAAQxJ,EAAY,OAUpD,KAAKM,EAGD,WADAkV,IAGJ,KAGQ/N,GAAmB3L,EAAW2L,KAE9BnH,EAAY,IAAMmH,EAAgBnH,IAAc,KAEpDtE,EAAOuN,IAAIC,GAAG,aAAclJ,GAC9B,MAAOmV,GAEDhO,GAAmB3L,EAAW2L,KAE9BzH,EAAY,IAAMyH,EAAgBzH,IAAc,KAEpDhE,EAAOuN,IAAIC,GAAG,aAAc,MAAQxJ,EAAY,aAKxDuK,EAAUjN,GAAG,QAAS,SAAUC,GAC5B,IAAIsL,EAAGK,SAGH3L,EAAEsX,iBAIDM,KAAL,CAKA,GAAIO,GAAajV,EAAalD,EAC9B,IAAKmY,GAAeA,EAAW/a,OAA/B,CAKA,GAAI0P,GAAiBrO,EAAOoN,UAAUkB,2BACtC,IAAKD,EAAL,CAGA,GAAIjJ,GAAWiJ,EAAexE,aAG9B,IAAiB,SAAbzE,GAAoC,QAAbA,EAA3B,CAKgBpF,EAAOiV,UACbA,UAAUyE,UAK5BzB,WAAY,WACR,GAAIjY,GAAS1C,KAAK0C,MACFA,GAAOuO,UAEbjN,GAAG,UAAW,SAAUC,GAC9B,GAAkB,IAAdA,EAAE0X,SAGDjZ,EAAOuN,IAAIoL,sBAAsB,cAAtC,CAIA,GAAItK,GAAiBrO,EAAOoN,UAAUkB,2BACtC,IAAKD,EAAL,CAGA,GAAIqD,GAAcrD,EAAehF,SAC7BoP,EAAoBpK,EAAexE,cACnC6O,EAAiBhH,EAAY7H,aAEP,UAAtB4O,GAAmD,QAAnBC,EAEhC1Y,EAAOuN,IAAIC,GAAG,aAAc,QAG5BxN,EAAOuN,IAAIC,GAAG,aAAc,4BAGhCjM,EAAEsX,sBAKVX,WAAY,WACR,GAAIlY,GAAS1C,KAAK0C,OACduO,EAAYvO,EAAOuO,SAGvBA,GAAUjN,GAAG,QAAS,MAAO,SAAUC,GACnC,GAAIoY,GAAMrc,KACNyX,EAAOlW,EAAE8a,EAEiB,OAA1B5E,EAAKrP,KAAK,cAMd1F,EAAOgV,aAAeD,EAGtB/U,EAAOoN,UAAU2C,kBAAkBgF,GACnC/U,EAAOoN,UAAUM,sBAIrBa,EAAUjN,GAAG,eAAgB,SAAUC,GAC/BA,EAAEwG,OAAOC,QAAQ,SAKrBhI,EAAOgV,aAAe,SAK9BmD,YAAa,WACT,GAAInY,GAAS1C,KAAK0C,MAGFnB,GAAEnB,UACR4D,GAAG,oCAAqC,SAAUC,GACxDA,EAAEsX,mBAIU7Y,EAAOuO,UACbjN,GAAG,OAAQ,SAAUC,GAC3BA,EAAEsX,gBACF,IAAItM,GAAQhL,EAAEqY,cAAgBrY,EAAEqY,aAAarN,KACxCA,IAAUA,EAAM5N,QAKLqB,EAAOiV,UACbA,UAAU1I,OAehCzG,EAAQoB,WACJC,YAAarB,EAGb0H,GAAI,SAAa7H,EAAMvE,GACnB,GAAIpB,GAAS1C,KAAK0C,MASlB,IANKA,EAAO6Z,mBACRnc,SAASoc,YAAY,eAAgB,MAAM,GAC3C9Z,EAAO6Z,kBAAmB,GAIzB7Z,EAAOoN,UAAUoK,WAAtB,CAKAxX,EAAOoN,UAAUM,kBAGjB,IAAIqM,GAAQ,IAAMpU,CACdrI,MAAKyc,GAELzc,KAAKyc,GAAO3Y,GAGZ9D,KAAK0c,aAAarU,EAAMvE,GAI5BpB,EAAO4D,MAAM6T,eAGbzX,EAAOoN,UAAUgL,YACjBpY,EAAOoN,UAAUM,mBAGjB1N,EAAOia,QAAUja,EAAOia,WAI5BC,YAAa,SAAqB1c,GAC9B,GAAIwC,GAAS1C,KAAK0C,OACdma,EAAQna,EAAOoN,UAAUoK,UAEzBla,MAAKqb,sBAAsB,cAE3Brb,KAAK0c,aAAa,aAAcxc,GACzB2c,EAAMC,YAEbD,EAAME,iBACNF,EAAMC,WAAWvb,EAAErB,GAAM,KAClB2c,EAAMG,WAEbH,EAAMG,UAAU9c,IAKxB+c,YAAa,SAAqBta,GAC9B,GAAID,GAAS1C,KAAK0C,OACdma,EAAQna,EAAOoN,UAAUoK,UAEzB2C,GAAMC,aACND,EAAME,iBACNF,EAAMC,WAAWna,EAAM,MAK/B+Z,aAAc,SAAsBrU,EAAMvE,GACtC1D,SAASoc,YAAYnU,GAAM,EAAOvE,IAItCqN,kBAAmB,SAA2B9I,GAC1C,MAAOjI,UAAS+Q,kBAAkB9I,IAItCiI,kBAAmB,SAA2BjI,GAC1C,MAAOjI,UAASkQ,kBAAkBjI,IAItCgT,sBAAuB,SAA+BhT,GAClD,MAAOjI,UAASib,sBAAsBhT,KAe9CI,EAAImB,WACAC,YAAapB,EAGbyR,SAAU,WACN,MAAOla,MAAK0I,eAIhBoS,UAAW,SAAmBoC,GAC1B,GAAIA,EAGA,YADAld,KAAK0I,cAAgBwU,EAKzB,IAAIpN,GAAYnJ,OAAOwW,cACvB,IAA6B,IAAzBrN,EAAUsN,WAAd,CAGA,GAAIP,GAAQ/M,EAAUuN,WAAW,GAG7BC,EAAiBtd,KAAKgR,0BAA0B6L,EACpD,IAAKS,GAK0C,UAA3CA,EAAelV,KAAK,qBAAkCkV,EAAe3Q,YAAY,2BAArF,CAIa3M,KAAK0C,OACKuO,UACT/E,UAAUoR,KAEpBtd,KAAK0I,cAAgBmU,MAK7B1M,cAAe,SAAuBoN,GACnB,MAAXA,IAEAA,GAAU,EAEd,IAAIV,GAAQ7c,KAAK0I,aACbmU,IACAA,EAAMW,SAASD,IAKvB5K,iBAAkB,WAEd,MADY3S,MAAK0I,cAEN1I,KAAK0I,cAAcrG,WAEnB,IAKf2O,0BAA2B,SAAmC6L,GAC1DA,EAAQA,GAAS7c,KAAK0I,aACtB,IAAIoB,OAAO,EACX,IAAI+S,EAEA,MADA/S,GAAO+S,EAAMY,wBACNlc,EAAoB,IAAlBuI,EAAK/I,SAAiB+I,EAAOA,EAAKoD,aAGnD0G,sBAAuB,SAA+BiJ,GAClDA,EAAQA,GAAS7c,KAAK0I,aACtB,IAAIoB,OAAO,EACX,IAAI+S,EAEA,MADA/S,GAAO+S,EAAMa,eACNnc,EAAoB,IAAlBuI,EAAK/I,SAAiB+I,EAAOA,EAAKoD,aAGnD4G,oBAAqB,SAA6B+I,GAC9CA,EAAQA,GAAS7c,KAAK0I,aACtB,IAAIoB,OAAO,EACX,IAAI+S,EAEA,MADA/S,GAAO+S,EAAMc,aACNpc,EAAoB,IAAlBuI,EAAK/I,SAAiB+I,EAAOA,EAAKoD,aAKnD6C,iBAAkB,WACd,GAAI8M,GAAQ7c,KAAK0I,aACjB,UAAImU,IAASA,EAAMa,gBACXb,EAAMa,iBAAmBb,EAAMc,cAC3Bd,EAAMpB,cAAgBoB,EAAMe,YAS5CxN,iBAAkB,WACd,GAAIN,GAAYnJ,OAAOwW,cACvBrN,GAAU+N,kBACV/N,EAAUgO,SAAS9d,KAAK0I,gBAI5BsH,iBAAkB,WACd,GAAItN,GAAS1C,KAAK0C,OACdma,EAAQ7c,KAAKka,WACbvX,MAAQ,EAEZ,IAAKka,GAIA7c,KAAK+P,mBAKV,IAEQR,EAAGI,YAEHjN,EAAOuN,IAAIC,GAAG,aAAc,WAE5B2M,EAAMkB,OAAOlB,EAAMc,aAAcd,EAAMe,UAAY,GAEnD5d,KAAK8a,UAAU+B,KAEfla,EAAQpB,EAAE,4BACVmB,EAAOuN,IAAIC,GAAG,aAAcvN,GAC5B3C,KAAKyS,kBAAkB9P,GAAO,IAEpC,MAAOwZ,MAMb1J,kBAAmB,SAA2B9P,EAAO4a,EAASS,GAI1D,GAAKrb,EAAMtB,OAAX,CAIA,GAAIyI,GAAOnH,EAAM,GACbka,EAAQzc,SAAS6d,aAEjBD,GACAnB,EAAMqB,mBAAmBpU,GAEzB+S,EAAMsB,WAAWrU,GAGE,iBAAZyT,IACPV,EAAMW,SAASD,GAInBvd,KAAK8a,UAAU+B,MAkBvBlU,EAASiB,WACLC,YAAalB,EAEb+C,KAAM,SAAc0S,GAChB,GAAInb,GAAQjD,IAGZ,KAAIA,KAAK6I,QAAT,CAGA7I,KAAK6I,SAAU,CAGf,IAAIK,GAAOlJ,KAAKkJ,IAChB,IAAKlJ,KAAK8I,UAIN9I,KAAK8I,WAAY,MAJA,CACI9I,KAAKgJ,eACXzF,OAAO2F,GAMtB6S,KAAKD,MAAQ9b,KAAK4I,MAAQ,KACtBwV,GAAY,IACZlV,EAAKmC,IAAI,QAAoB,IAAX+S,EAAiB,KACnCpe,KAAK4I,MAAQmT,KAAKD,MAK1B,IAAIuC,GAAYre,KAAK+I,UACjBsV,IACA3N,aAAa2N,GAEjBA,EAAYla,WAAW,WACnBlB,EAAMqb,SACP,OAGPA,MAAO,WACQte,KAAKkJ,KACX4C,SAGL9L,KAAK4I,MAAQ,EACb5I,KAAK6I,SAAU,EACf7I,KAAK8I,WAAY,GAIzB,IAAIyV,GAA4B,kBAAXC,SAAoD,gBAApBA,QAAOC,SAAwB,SAAUhd,GAC5F,aAAcA,IACZ,SAAUA,GACZ,MAAOA,IAAyB,kBAAX+c,SAAyB/c,EAAIoI,cAAgB2U,QAAU/c,IAAQ+c,OAAO5U,UAAY,eAAkBnI,GAa3H0H,GAAUS,WACNC,YAAaV,EAGbuV,OAAQ,SAAgBC,EAAWC,GAC/B,GAAIlc,GAAS1C,KAAK0C,OACdmL,EAAQnL,EAAOmC,OAAOgJ,MACtBgR,EAAcnc,EAAOmC,OAAOga,WAEhC,IAAIhR,EACA,KAAM,IAAItE,OAAM,gBAAkBqV,GAAaD,GAE3CE,IAAsC,kBAAhBA,GACtBA,EAAYF,GAEZpL,MAAMoL,IAMlBtG,cAAe,SAAuBtK,GAClC,GAAIwH,GAASvV,IAEb,IAAK+N,EAAL,CAGA,GAAIrL,GAAS1C,KAAK0C,OACdmC,EAASnC,EAAOmC,OAGhBmJ,EAAenJ,EAAOmJ,aACtBsF,MAAc,EAClB,IAAItF,GAAwC,kBAAjBA,IAEI,iBAD3BsF,EAActF,EAAaD,IAIvB,WADAwF,OAAMD,EAKd5Q,GAAOuN,IAAIC,GAAG,aAAc,aAAenC,EAAO,8BAGlD,IAAIsO,GAAMjc,SAASC,cAAc,MACjCgc,GAAIyC,OAAS,WACT,GAAIC,GAAWla,EAAOwJ,eAClB0Q,IAAgC,kBAAbA,IACnBA,EAAShR,GAGbsO,EAAM,MAEVA,EAAI2C,QAAU,WACV3C,EAAM,KAEN9G,EAAOmJ,OAAO,SAAU,6BAA2F3Q,EAAO,cAG9HsO,EAAI4C,QAAU,WACV5C,EAAM,MAEVA,EAAI1O,IAAMI,IAId4J,UAAW,SAAmB1I,GAC1B,GAAIiQ,GAASlf,IAEb,IAAKiP,GAAUA,EAAM5N,OAArB,CAKA,GAAIqB,GAAS1C,KAAK0C,OACdmC,EAASnC,EAAOmC,OAChB0T,EAAkB1T,EAAO0T,gBACzB/J,EAAsB3J,EAAO2J,oBAE7B2Q,EAAUta,EAAO0J,iBACjB6Q,EAAWD,EAAU,KAAO,KAC5BE,EAAYxa,EAAOya,oBAAsB,IACzC7Q,EAAiB5J,EAAO4J,gBAAkB,GAC1CC,EAAkB7J,EAAO6J,oBACzB6Q,EAAyB1a,EAAO0a,uBAChC5Q,EAAmB9J,EAAO8J,qBAC1B6Q,EAAQ3a,EAAOiK,mBACfO,EAAUxK,EAAOgK,kBAAoB,IACrCD,EAAkB/J,EAAO+J,eACN,OAAnBA,IACAA,GAAkB,EAEtB,IAAI4J,GAAkB3T,EAAO2T,eAE7B,IAAKA,GAEID,GAAoB/J,EAF7B,CAQA,GAAIiR,MACAC,IAyBJ,IAxBA5d,EAAWmN,EAAO,SAAU0Q,GACxB,GAAItX,GAAOsX,EAAKtX,KACZuX,EAAOD,EAAKC,IAGhB,IAAKvX,GAASuX,EAId,OAAqD,IAAjD,kCAAkCvY,KAAKgB,OAEvCqX,GAAQpY,KAAK,IAAWe,EAAO,SAG/B8W,EAAUS,MAEVF,GAAQpY,KAAK,IAAWe,EAAO,OAAwB+W,EAAW,SAKtEK,GAAYnY,KAAKqY,KAGjBD,EAAQre,OAER,WADArB,MAAK0e,OAAO,cAAgBgB,EAAQvU,KAAK,MAG7C,IAAIsU,EAAYpe,OAASge,EAErB,WADArf,MAAK0e,OAAO,SAAWW,EAAY,MAKvC,IAAI7G,GAA8C,kBAApBA,GAI1B,WAHAA,GAAgBiH,EAAazf,KAAKqY,cAAcwH,KAAK7f,MAOzD,IAAI8f,GAAW,GAAIC,SAOnB,IANAje,EAAW2d,EAAa,SAAUE,GAC9B,GAAItX,GAAOoG,GAAkBkR,EAAKtX,IAClCyX,GAASvc,OAAO8E,EAAMsX,KAItBpH,GAA8C,gBAApBA,GAA8B,CAExD,GAAIyH,GAAqBzH,EAAgBrR,MAAM,IAC/CqR,GAAkByH,EAAmB,EACrC,IAAIC,GAAsBD,EAAmB,IAAM,EACnDxe,GAAWkN,EAAiB,SAAU/M,EAAKkJ,GAKnC0U,IACIhH,EAAgBnX,QAAQ,KAAO,EAC/BmX,GAAmB,IAEnBA,GAAmB,IAEvBA,EAAkBA,EAAkB5W,EAAM,IAAMkJ,GAIpDiV,EAASvc,OAAO5B,EAAKkJ,KAErBoV,IACA1H,GAAmB,IAAM0H,EAI7B,IAAIjR,GAAM,GAAIkR,eAqFd,IApFAlR,EAAImR,KAAK,OAAQ5H,GAGjBvJ,EAAIK,QAAUA,EACdL,EAAIoR,UAAY,WAERZ,EAAMnQ,SAAoC,kBAAlBmQ,GAAMnQ,SAC9BmQ,EAAMnQ,QAAQL,EAAKtM,GAGvBwc,EAAOR,OAAO,WAId1P,EAAIqR,SACJrR,EAAIqR,OAAOC,WAAa,SAAUrc,GAC9B,GAAIsc,OAAU,GAEVC,EAAc,GAAI7X,GAASjG,EAC3BuB,GAAEwc,mBACFF,EAAUtc,EAAEyc,OAASzc,EAAE0c,MACvBH,EAAY9U,KAAK6U,MAM7BvR,EAAI4R,mBAAqB,WACrB,GAAI/f,OAAS,EACb,IAAuB,IAAnBmO,EAAI6R,WAAkB,CACtB,GAAI7R,EAAI8R,OAAS,KAAO9R,EAAI8R,QAAU,IAQlC,MANItB,GAAMpQ,OAAgC,kBAAhBoQ,GAAMpQ,OAC5BoQ,EAAMpQ,MAAMJ,EAAKtM,OAIrBwc,GAAOR,OAAO,WAAY,qBAA4G1P,EAAI8R,OAK9I,IADAjgB,EAASmO,EAAI+R,aAC2D,gBAAjD,KAAXlgB,EAAyB,YAAc0d,EAAQ1d,IACvD,IACIA,EAASmgB,KAAKC,MAAMpgB,GACtB,MAAOsb,GAOL,MALIqD,GAAMrQ,MAA8B,kBAAfqQ,GAAMrQ,MAC3BqQ,EAAMrQ,KAAKH,EAAKtM,EAAQ7B,OAG5Bqe,GAAOR,OAAO,SAAU,qBAAuB7d,GAIvD,GAAK2e,EAAM0B,cAAgC,KAAhBrgB,EAAOsgB,MAQ3B,CACH,GAAI3B,EAAM0B,cAA8C,kBAAvB1B,GAAM0B,aAEnC1B,EAAM0B,aAAahC,EAAO7G,cAAcwH,KAAKX,GAASre,EAAQ6B,OAC3D,EAEQ7B,EAAOugB,UACbxd,QAAQ,SAAUmK,GACnBmR,EAAO7G,cAActK,KAKzByR,EAAMtQ,SAAoC,kBAAlBsQ,GAAMtQ,SAC9BsQ,EAAMtQ,QAAQF,EAAKtM,EAAQ7B,OApB3B2e,GAAMrQ,MAA8B,kBAAfqQ,GAAMrQ,MAC3BqQ,EAAMrQ,KAAKH,EAAKtM,EAAQ7B,GAI5Bqe,EAAOR,OAAO,SAAU,yBAA2B7d,EAAOsgB,SAsBlE3B,EAAMzQ,QAAkC,kBAAjByQ,GAAMzQ,OAAuB,CACpD,GAAIsS,GAAe7B,EAAMzQ,OAAOC,EAAKtM,EAAQ+c,EAC7C,IAAI4B,GAAgG,gBAAvD,KAAjBA,EAA+B,YAAc9C,EAAQ8C,KACzEA,EAAaC,QAGb,WADAthB,MAAK0e,OAAO2C,EAAaE,KAkBrC,MAXA/f,GAAWmN,EAAkB,SAAUhN,EAAKkJ,GACxCmE,EAAIwS,iBAAiB7f,EAAKkJ,KAI9BmE,EAAIJ,gBAAkBA,MAGtBI,GAAIyS,KAAK3B,GAOTtR,GACA1M,EAAWmN,EAAO,SAAU0Q,GACxB,GAAI1c,GAAQic,EACRwC,EAAS,GAAIjJ,WACjBiJ,GAAOC,cAAchC,GACrB+B,EAAO5C,OAAS,WACZ7b,EAAMoV,cAAcrY,KAAKa,cAY7C,IAAI4I,GAAW,CAmBfL,GAAOQ,WACHC,YAAaT,EAGbwY,YAAa,WAET,GAAInX,KACJzK,MAAK6E,OAASgd,OAAOC,OAAOrX,EAAQ5F,EAAQ7E,KAAK0J,aAGjD,IAAIqY,GAAa/hB,KAAK6E,OAAOmd,SACzBxR,IACJhP,GAAWugB,EAAY,SAAUpgB,EAAKkJ,GAGlC2F,EAASlJ,MACLmJ,IAAK,GAAIwR,QAAOtgB,EAAK,OACrBkJ,IAAKA,MAIb7K,KAAK6E,OAAO2L,SAAWA,GAI3B0R,SAAU,WACN,GAAIjf,GAAQjD,KAERqJ,EAAkBrJ,KAAKqJ,gBACvB8Y,EAAmB5gB,EAAE8H,GACrBC,EAAetJ,KAAKsJ,aAEpB8Y,EAAYpiB,KAAK6E,OACjB+I,EAASwU,EAAUxU,OAGnBqM,MAAe,GACfhR,MAAqB,GACrBgI,MAAY,GACZtF,MAAY,EAEI,OAAhBrC,GAEA2Q,EAAe1Y,EAAE,eACjB0H,EAAqB1H,EAAE,eAGvBoK,EAAYwW,EAAiB5hB,WAG7B4hB,EAAiB5e,OAAO0W,GAAc1W,OAAO0F,GAG7CgR,EAAa5O,IAAI,mBAAoB,WAAWA,IAAI,SAAU,kBAC9DpC,EAAmBoC,IAAI,SAAU,kBAAkBA,IAAI,aAAc,QAAQA,IAAI,SAAU,WAG3F4O,EAAekI,EACflZ,EAAqB1H,EAAE+H,GAEvBqC,EAAY1C,EAAmB1I,YAInC0Q,EAAY1P,EAAE,eACd0P,EAAU7I,KAAK,kBAAmB,QAAQiD,IAAI,QAAS,QAAQA,IAAI,SAAU,QAGzEM,GAAaA,EAAUtK,OACvB4P,EAAU1N,OAAOoI,GAEjBsF,EAAU1N,OAAOhC,EAAE,gBAIvB0H,EAAmB1F,OAAO0N,GAG1BgJ,EAAa3W,SAAS,eACtB2F,EAAmB3F,SAAS,sBAC5B2F,EAAmBoC,IAAI,UAAWuC,GAClCqD,EAAU3N,SAAS,WAGnB,IAAI+e,GAAgBpgB,EAAU,eAC9BgY,GAAa7R,KAAK,KAAMia,EACxB,IAAIC,GAAargB,EAAU,YAC3BgP,GAAU7I,KAAK,KAAMka,GAGrBtiB,KAAKia,aAAeA,EACpBja,KAAKiJ,mBAAqBA,EAC1BjJ,KAAKiR,UAAYA,EACjBjR,KAAKqiB,cAAgBA,EACrBriB,KAAKsiB,WAAaA,CAGlB,IAAIC,IAAiB,CACrBtZ,GAAmBjF,GAAG,mBAAoB,WAEtCue,GAAiB,IAErBtZ,EAAmBjF,GAAG,iBAAkB,WAEpCue,GAAiB,IAIrBtZ,EAAmBjF,GAAG,cAAe,WAEjCue,GAAkBtf,EAAM0Z,QAAU1Z,EAAM0Z,WAE5C1C,EAAajW,GAAG,QAAS,WACrBhE,KAAK2c,QAAU3c,KAAK2c,YAIpByF,EAAUI,SAAWJ,EAAUK,UAE/BziB,KAAK0iB,SAAU,EAEfnhB,EAAEnB,UAAU4D,GAAG,QAAS,SAAUC,GAE9B,GAAI0e,GAAU1R,EAAU/E,UAAU3K,EAAE0C,EAAEwG,SAGlCmY,EAAY3I,EAAa/N,UAAU3K,EAAE0C,EAAEwG,SACvCoY,EAAS5I,EAAa,IAAMhW,EAAEwG,MAElC,IAAKkY,EAWI1f,EAAMyf,SACPzf,EAAMuf,SAAWvf,EAAMuf,UAE3Bvf,EAAMyf,SAAU,MAdN,CAEV,GAAIE,IAAcC,EACd,MAGA5f,GAAMyf,SACNzf,EAAMwf,QAAUxf,EAAMwf,SAE1Bxf,EAAMyf,SAAU,OAYhCI,aAAc,WACV9iB,KAAKiQ,IAAM,GAAIzH,GAAQxI,OAI3B+iB,kBAAmB,WACf/iB,KAAK8P,UAAY,GAAIrH,GAAIzI,OAI7BgjB,eAAgB,WACZhjB,KAAK2X,UAAY,GAAIxO,GAAUnJ,OAInCijB,WAAY,WACRjjB,KAAKsG,MAAQ,GAAID,GAAMrG,MACvBA,KAAKsG,MAAMsT,QAIfsJ,UAAW,WACPljB,KAAKmjB,IAAM,GAAI5a,GAAKvI,MACpBA,KAAKmjB,IAAIvJ,QAIbS,cAAe,SAAuB+I,GAClC,GAAInS,GAAYjR,KAAKiR,UACjBtF,EAAYsF,EAAU1Q,UAC1B,KAAKoL,EAAUtK,OAIX,MAFA4P,GAAU1N,OAAOhC,EAAE,oBACnBvB,MAAKqa,eAIT,IAAIgJ,GAAQ1X,EAAUrB,MAEtB,IAAI8Y,EAAS,CAET,GAAIljB,GAAOmjB,EAAMnjB,OAAO6H,cACpBD,EAAWub,EAAM9W,aACrB,IAAa,SAATrM,GAA4B,UAATA,GAAkC,MAAb4H,EAIxC,MAFAmJ,GAAU1N,OAAOhC,EAAE,oBACnBvB,MAAKqa,gBAKbra,KAAK8P,UAAU2C,kBAAkB4Q,GAAO,GAAO,GAC/CrjB,KAAK8P,UAAUM,oBAInB4J,WAAY,WAER,GAAIsJ,GAAoB,EACpBC,EAAmBvjB,KAAKmjB,IAAIjjB,OAC5BkiB,EAAYpiB,KAAK6E,OAGjB2e,EAAkBpB,EAAUoB,mBAChCA,EAAkBrO,SAASqO,EAAiB,MACpBA,GAAmB,KACvCA,EAAkB,IAGtB,IAAIC,GAAWrB,EAAUqB,QACrBA,IAAgC,kBAAbA,KAKnBzjB,KAAK2c,OAAS,WAEV,GAAI+G,GAAc1jB,KAAKmjB,IAAIjjB,MAEvBwjB,GAAYriB,SAAWkiB,EAAiBliB,QAEpCqiB,IAAgBH,IAMpBD,GACA5S,aAAa4S,GAEjBA,EAAoBnf,WAAW,WAE3Bsf,EAASC,GACTH,EAAmBG,GACpBF,KAKX,IAAIf,GAASL,EAAUK,MACnBA,IAA4B,kBAAXA,KACjBziB,KAAKyiB,OAAS,WACV,GAAIiB,GAAc1jB,KAAKmjB,IAAIjjB,MAC3BuiB,GAAOiB,IAKf,IAAIlB,GAAUJ,EAAUI,OACpBA,IAA8B,kBAAZA,KAClBxiB,KAAKwiB,QAAU,WACXA,OAMZmB,OAAQ,WAEJ3jB,KAAK4hB,cAGL5hB,KAAKkiB,WAGLliB,KAAK8iB,eAGL9iB,KAAK+iB,oBAGL/iB,KAAKkjB,YAGLljB,KAAKijB,aAGLjjB,KAAKgjB,iBAGLhjB,KAAKqa,eAAc,GAGnBra,KAAKga,cAIT4J,aAAc,WACVriB,EAAE+L,UAKV,KACIlN,SACF,MAAO+b,GACL,KAAM,IAAI5S,OAAM,eAniJL,WAGiB,kBAAjBsY,QAAOC,SACdD,OAAOC,OAAS,SAAUrX,EAAQoZ,GAE9B,GAAc,MAAVpZ,EAEA,KAAM,IAAIqZ,WAAU,6CAKxB,KAAK,GAFDC,GAAKlC,OAAOpX,GAEPL,EAAQ,EAAGA,EAAQ4Z,UAAU3iB,OAAQ+I,IAAS,CACnD,GAAI6Z,GAAaD,UAAU5Z,EAE3B,IAAkB,MAAd6Z,EAEA,IAAK,GAAIC,KAAWD,GAEZpC,OAAOjY,UAAUhI,eAAeC,KAAKoiB,EAAYC,KACjDH,EAAGG,GAAWD,EAAWC,IAKzC,MAAOH,KAKVI,QAAQva,UAAUc,UACnByZ,QAAQva,UAAUc,QAAUyZ,QAAQva,UAAUwa,iBAAmBD,QAAQva,UAAUya,oBAAsBF,QAAQva,UAAU0a,mBAAqBH,QAAQva,UAAU2a,kBAAoBJ,QAAQva,UAAU4a,uBAAyB,SAAUC,GAGvO,IAFA,GAAI/Z,IAAW1K,KAAKI,UAAYJ,KAAK0kB,eAAe9jB,iBAAiB6jB,GACjEnjB,EAAIoJ,EAAQrJ,SACPC,GAAK,GAAKoJ,EAAQ1I,KAAKV,KAAOtB,OACvC,MAAOsB,IAAK,MAsgJxB,IAGIiK,GAAQnL,SAASC,cAAc,QAQnC,OAPAkL,GAAM3I,KAAO,WACb2I,EAAMjL,UALU;y9gBAMhBF,SAASukB,qBAAqB,QAAQ3iB,KAAK,GAAG6J,YAAYN,GAG9C5E,OAAO5G,YAAcqJ", "file": "wangEditor.min.js", "sourcesContent": ["(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(global.wangEditor = factory());\n}(this, (function () { 'use strict';\n\n/*\n    poly-fill\n*/\n\nvar polyfill = function () {\n\n    // Object.assign\n    if (typeof Object.assign != 'function') {\n        Object.assign = function (target, varArgs) {\n            // .length of function is 2\n            if (target == null) {\n                // TypeError if undefined or null\n                throw new TypeError('Cannot convert undefined or null to object');\n            }\n\n            var to = Object(target);\n\n            for (var index = 1; index < arguments.length; index++) {\n                var nextSource = arguments[index];\n\n                if (nextSource != null) {\n                    // Skip over if undefined or null\n                    for (var nextKey in nextSource) {\n                        // Avoid bugs when hasOwnProperty is shadowed\n                        if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                            to[nextKey] = nextSource[nextKey];\n                        }\n                    }\n                }\n            }\n            return to;\n        };\n    }\n\n    // IE 中兼容 Element.prototype.matches\n    if (!Element.prototype.matches) {\n        Element.prototype.matches = Element.prototype.matchesSelector || Element.prototype.mozMatchesSelector || Element.prototype.msMatchesSelector || Element.prototype.oMatchesSelector || Element.prototype.webkitMatchesSelector || function (s) {\n            var matches = (this.document || this.ownerDocument).querySelectorAll(s),\n                i = matches.length;\n            while (--i >= 0 && matches.item(i) !== this) {}\n            return i > -1;\n        };\n    }\n};\n\n/*\n    DOM 操作 API\n*/\n\n// 根据 html 代码片段创建 dom 对象\nfunction createElemByHTML(html) {\n    var div = void 0;\n    div = document.createElement('div');\n    div.innerHTML = html;\n    return div.children;\n}\n\n// 是否是 DOM List\nfunction isDOMList(selector) {\n    if (!selector) {\n        return false;\n    }\n    if (selector instanceof HTMLCollection || selector instanceof NodeList) {\n        return true;\n    }\n    return false;\n}\n\n// 封装 document.querySelectorAll\nfunction querySelectorAll(selector) {\n    var result = document.querySelectorAll(selector);\n    if (isDOMList(result)) {\n        return result;\n    } else {\n        return [result];\n    }\n}\n\n// 记录所有的事件绑定\nvar eventList = [];\n\n// 创建构造函数\nfunction DomElement(selector) {\n    if (!selector) {\n        return;\n    }\n\n    // selector 本来就是 DomElement 对象，直接返回\n    if (selector instanceof DomElement) {\n        return selector;\n    }\n\n    this.selector = selector;\n    var nodeType = selector.nodeType;\n\n    // 根据 selector 得出的结果（如 DOM，DOM List）\n    var selectorResult = [];\n    if (nodeType === 9) {\n        // document 节点\n        selectorResult = [selector];\n    } else if (nodeType === 1) {\n        // 单个 DOM 节点\n        selectorResult = [selector];\n    } else if (isDOMList(selector) || selector instanceof Array) {\n        // DOM List 或者数组\n        selectorResult = selector;\n    } else if (typeof selector === 'string') {\n        // 字符串\n        selector = selector.replace('/\\n/mg', '').trim();\n        if (selector.indexOf('<') === 0) {\n            // 如 <div>\n            selectorResult = createElemByHTML(selector);\n        } else {\n            // 如 #id .class\n            selectorResult = querySelectorAll(selector);\n        }\n    }\n\n    var length = selectorResult.length;\n    if (!length) {\n        // 空数组\n        return this;\n    }\n\n    // 加入 DOM 节点\n    var i = void 0;\n    for (i = 0; i < length; i++) {\n        this[i] = selectorResult[i];\n    }\n    this.length = length;\n}\n\n// 修改原型\nDomElement.prototype = {\n    constructor: DomElement,\n\n    // 类数组，forEach\n    forEach: function forEach(fn) {\n        var i = void 0;\n        for (i = 0; i < this.length; i++) {\n            var elem = this[i];\n            var result = fn.call(elem, elem, i);\n            if (result === false) {\n                break;\n            }\n        }\n        return this;\n    },\n\n    // clone\n    clone: function clone(deep) {\n        var cloneList = [];\n        this.forEach(function (elem) {\n            cloneList.push(elem.cloneNode(!!deep));\n        });\n        return $(cloneList);\n    },\n\n    // 获取第几个元素\n    get: function get(index) {\n        var length = this.length;\n        if (index >= length) {\n            index = index % length;\n        }\n        return $(this[index]);\n    },\n\n    // 第一个\n    first: function first() {\n        return this.get(0);\n    },\n\n    // 最后一个\n    last: function last() {\n        var length = this.length;\n        return this.get(length - 1);\n    },\n\n    // 绑定事件\n    on: function on(type, selector, fn) {\n        // selector 不为空，证明绑定事件要加代理\n        if (!fn) {\n            fn = selector;\n            selector = null;\n        }\n\n        // type 是否有多个\n        var types = [];\n        types = type.split(/\\s+/);\n\n        return this.forEach(function (elem) {\n            types.forEach(function (type) {\n                if (!type) {\n                    return;\n                }\n\n                // 记录下，方便后面解绑\n                eventList.push({\n                    elem: elem,\n                    type: type,\n                    fn: fn\n                });\n\n                if (!selector) {\n                    // 无代理\n                    elem.addEventListener(type, fn);\n                    return;\n                }\n\n                // 有代理\n                elem.addEventListener(type, function (e) {\n                    var target = e.target;\n                    if (target.matches(selector)) {\n                        fn.call(target, e);\n                    }\n                });\n            });\n        });\n    },\n\n    // 取消事件绑定\n    off: function off(type, fn) {\n        return this.forEach(function (elem) {\n            elem.removeEventListener(type, fn);\n        });\n    },\n\n    // 获取/设置 属性\n    attr: function attr(key, val) {\n        if (val == null) {\n            // 获取值\n            return this[0].getAttribute(key);\n        } else {\n            // 设置值\n            return this.forEach(function (elem) {\n                elem.setAttribute(key, val);\n            });\n        }\n    },\n\n    // 添加 class\n    addClass: function addClass(className) {\n        if (!className) {\n            return this;\n        }\n        return this.forEach(function (elem) {\n            var arr = void 0;\n            if (elem.className) {\n                // 解析当前 className 转换为数组\n                arr = elem.className.split(/\\s/);\n                arr = arr.filter(function (item) {\n                    return !!item.trim();\n                });\n                // 添加 class\n                if (arr.indexOf(className) < 0) {\n                    arr.push(className);\n                }\n                // 修改 elem.class\n                elem.className = arr.join(' ');\n            } else {\n                elem.className = className;\n            }\n        });\n    },\n\n    // 删除 class\n    removeClass: function removeClass(className) {\n        if (!className) {\n            return this;\n        }\n        return this.forEach(function (elem) {\n            var arr = void 0;\n            if (elem.className) {\n                // 解析当前 className 转换为数组\n                arr = elem.className.split(/\\s/);\n                arr = arr.filter(function (item) {\n                    item = item.trim();\n                    // 删除 class\n                    if (!item || item === className) {\n                        return false;\n                    }\n                    return true;\n                });\n                // 修改 elem.class\n                elem.className = arr.join(' ');\n            }\n        });\n    },\n\n    // 修改 css\n    css: function css(key, val) {\n        var currentStyle = key + ':' + val + ';';\n        return this.forEach(function (elem) {\n            var style = (elem.getAttribute('style') || '').trim();\n            var styleArr = void 0,\n                resultArr = [];\n            if (style) {\n                // 将 style 按照 ; 拆分为数组\n                styleArr = style.split(';');\n                styleArr.forEach(function (item) {\n                    // 对每项样式，按照 : 拆分为 key 和 value\n                    var arr = item.split(':').map(function (i) {\n                        return i.trim();\n                    });\n                    if (arr.length === 2) {\n                        resultArr.push(arr[0] + ':' + arr[1]);\n                    }\n                });\n                // 替换或者新增\n                resultArr = resultArr.map(function (item) {\n                    if (item.indexOf(key) === 0) {\n                        return currentStyle;\n                    } else {\n                        return item;\n                    }\n                });\n                if (resultArr.indexOf(currentStyle) < 0) {\n                    resultArr.push(currentStyle);\n                }\n                // 结果\n                elem.setAttribute('style', resultArr.join('; '));\n            } else {\n                // style 无值\n                elem.setAttribute('style', currentStyle);\n            }\n        });\n    },\n\n    // 显示\n    show: function show() {\n        return this.css('display', 'block');\n    },\n\n    // 隐藏\n    hide: function hide() {\n        return this.css('display', 'none');\n    },\n\n    // 获取子节点\n    children: function children() {\n        var elem = this[0];\n        if (!elem) {\n            return null;\n        }\n\n        return $(elem.children);\n    },\n\n    // 获取子节点（包括文本节点）\n    childNodes: function childNodes() {\n        var elem = this[0];\n        if (!elem) {\n            return null;\n        }\n\n        return $(elem.childNodes);\n    },\n\n    // 增加子节点\n    append: function append($children) {\n        return this.forEach(function (elem) {\n            $children.forEach(function (child) {\n                elem.appendChild(child);\n            });\n        });\n    },\n\n    // 移除当前节点\n    remove: function remove() {\n        return this.forEach(function (elem) {\n            if (elem.remove) {\n                elem.remove();\n            } else {\n                var parent = elem.parentElement;\n                parent && parent.removeChild(elem);\n            }\n        });\n    },\n\n    // 是否包含某个子节点\n    isContain: function isContain($child) {\n        var elem = this[0];\n        var child = $child[0];\n        return elem.contains(child);\n    },\n\n    // 尺寸数据\n    getSizeData: function getSizeData() {\n        var elem = this[0];\n        return elem.getBoundingClientRect(); // 可得到 bottom height left right top width 的数据\n    },\n\n    // 封装 nodeName\n    getNodeName: function getNodeName() {\n        var elem = this[0];\n        return elem.nodeName;\n    },\n\n    // 从当前元素查找\n    find: function find(selector) {\n        var elem = this[0];\n        return $(elem.querySelectorAll(selector));\n    },\n\n    // 获取当前元素的 text\n    text: function text(val) {\n        if (!val) {\n            // 获取 text\n            var elem = this[0];\n            return elem.innerHTML.replace(/<.*?>/g, function () {\n                return '';\n            });\n        } else {\n            // 设置 text\n            return this.forEach(function (elem) {\n                elem.innerHTML = val;\n            });\n        }\n    },\n\n    // 获取 html\n    html: function html(value) {\n        var elem = this[0];\n        if (value == null) {\n            return elem.innerHTML;\n        } else {\n            elem.innerHTML = value;\n            return this;\n        }\n    },\n\n    // 获取 value\n    val: function val() {\n        var elem = this[0];\n        return elem.value.trim();\n    },\n\n    // focus\n    focus: function focus() {\n        return this.forEach(function (elem) {\n            elem.focus();\n        });\n    },\n\n    // parent\n    parent: function parent() {\n        var elem = this[0];\n        return $(elem.parentElement);\n    },\n\n    // parentUntil 找到符合 selector 的父节点\n    parentUntil: function parentUntil(selector, _currentElem) {\n        var results = document.querySelectorAll(selector);\n        var length = results.length;\n        if (!length) {\n            // 传入的 selector 无效\n            return null;\n        }\n\n        var elem = _currentElem || this[0];\n        if (elem.nodeName === 'BODY') {\n            return null;\n        }\n\n        var parent = elem.parentElement;\n        var i = void 0;\n        for (i = 0; i < length; i++) {\n            if (parent === results[i]) {\n                // 找到，并返回\n                return $(parent);\n            }\n        }\n\n        // 继续查找\n        return this.parentUntil(selector, parent);\n    },\n\n    // 判断两个 elem 是否相等\n    equal: function equal($elem) {\n        if ($elem.nodeType === 1) {\n            return this[0] === $elem;\n        } else {\n            return this[0] === $elem[0];\n        }\n    },\n\n    // 将该元素插入到某个元素前面\n    insertBefore: function insertBefore(selector) {\n        var $referenceNode = $(selector);\n        var referenceNode = $referenceNode[0];\n        if (!referenceNode) {\n            return this;\n        }\n        return this.forEach(function (elem) {\n            var parent = referenceNode.parentNode;\n            parent.insertBefore(elem, referenceNode);\n        });\n    },\n\n    // 将该元素插入到某个元素后面\n    insertAfter: function insertAfter(selector) {\n        var $referenceNode = $(selector);\n        var referenceNode = $referenceNode[0];\n        if (!referenceNode) {\n            return this;\n        }\n        return this.forEach(function (elem) {\n            var parent = referenceNode.parentNode;\n            if (parent.lastChild === referenceNode) {\n                // 最后一个元素\n                parent.appendChild(elem);\n            } else {\n                // 不是最后一个元素\n                parent.insertBefore(elem, referenceNode.nextSibling);\n            }\n        });\n    }\n};\n\n// new 一个对象\nfunction $(selector) {\n    return new DomElement(selector);\n}\n\n// 解绑所有事件，用于销毁编辑器\n$.offAll = function () {\n    eventList.forEach(function (item) {\n        var elem = item.elem;\n        var type = item.type;\n        var fn = item.fn;\n        // 解绑\n        elem.removeEventListener(type, fn);\n    });\n};\n\n/*\n    配置信息\n*/\n\nvar config = {\n\n    // 默认菜单配置\n    menus: ['head', 'bold', 'fontSize', 'fontName', 'italic', 'underline', 'strikeThrough', 'foreColor', 'backColor', 'link', 'list', 'justify', 'quote', 'emoticon', 'image', 'table', 'video', 'code', 'undo', 'redo'],\n\n    fontNames: ['宋体', '微软雅黑', 'Arial', 'Tahoma', 'Verdana'],\n\n    colors: ['#000000', '#eeece0', '#1c487f', '#4d80bf', '#c24f4a', '#8baa4a', '#7b5ba1', '#46acc8', '#f9963b', '#ffffff'],\n\n    // // 语言配置\n    // lang: {\n    //     '设置标题': 'title',\n    //     '正文': 'p',\n    //     '链接文字': 'link text',\n    //     '链接': 'link',\n    //     '插入': 'insert',\n    //     '创建': 'init'\n    // },\n\n    // 表情\n    emotions: [{\n        // tab 的标题\n        title: '默认',\n        // type -> 'emoji' / 'image'\n        type: 'image',\n        // content -> 数组\n        content: [{\n            alt: '[坏笑]',\n            src: 'http://img.t.sinajs.cn/t4/appstyle/expression/ext/normal/50/pcmoren_huaixiao_org.png'\n        }, {\n            alt: '[舔屏]',\n            src: 'http://img.t.sinajs.cn/t4/appstyle/expression/ext/normal/40/pcmoren_tian_org.png'\n        }, {\n            alt: '[污]',\n            src: 'http://img.t.sinajs.cn/t4/appstyle/expression/ext/normal/3c/pcmoren_wu_org.png'\n        }]\n    }, {\n        // tab 的标题\n        title: '新浪',\n        // type -> 'emoji' / 'image'\n        type: 'image',\n        // content -> 数组\n        content: [{\n            src: 'http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/7a/shenshou_thumb.gif',\n            alt: '[草泥马]'\n        }, {\n            src: 'http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/60/horse2_thumb.gif',\n            alt: '[神马]'\n        }, {\n            src: 'http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/bc/fuyun_thumb.gif',\n            alt: '[浮云]'\n        }]\n    }, {\n        // tab 的标题\n        title: 'emoji',\n        // type -> 'emoji' / 'image'\n        type: 'emoji',\n        // content -> 数组\n        content: '😀 😃 😄 😁 😆 😅 😂 😊 😇 🙂 🙃 😉 😓 😪 😴 🙄 🤔 😬 🤐'.split(/\\s/)\n    }],\n\n    // 编辑区域的 z-index\n    zIndex: 10000,\n\n    // 是否开启 debug 模式（debug 模式下错误会 throw error 形式抛出）\n    debug: false,\n\n    // 插入链接时候的格式校验\n    linkCheck: function linkCheck(text, link) {\n        // text 是插入的文字\n        // link 是插入的链接\n        return true; // 返回 true 即表示成功\n        // return '校验失败' // 返回字符串即表示失败的提示信息\n    },\n\n    // 插入网络图片的校验\n    linkImgCheck: function linkImgCheck(src) {\n        // src 即图片的地址\n        return true; // 返回 true 即表示成功\n        // return '校验失败'  // 返回字符串即表示失败的提示信息\n    },\n\n    // 粘贴过滤样式，默认开启\n    pasteFilterStyle: true,\n\n    // 粘贴内容时，忽略图片。默认关闭\n    pasteIgnoreImg: false,\n\n    // 对粘贴的文字进行自定义处理，返回处理后的结果。编辑器会将处理后的结果粘贴到编辑区域中。\n    // IE 暂时不支持\n    pasteTextHandle: function pasteTextHandle(content) {\n        // content 即粘贴过来的内容（html 或 纯文本），可进行自定义处理然后返回\n        return content;\n    },\n\n    // onchange 事件\n    // onchange: function (html) {\n    //     // html 即变化之后的内容\n    //     console.log(html)\n    // },\n\n    // 是否显示添加网络图片的 tab\n    showLinkImg: true,\n\n    // 插入网络图片的回调\n    linkImgCallback: function linkImgCallback(url) {\n        // console.log(url)  // url 即插入图片的地址\n    },\n\n    // 默认上传图片 max size: 5M\n    uploadImgMaxSize: 5 * 1024 * 1024,\n\n    // 配置一次最多上传几个图片\n    // uploadImgMaxLength: 5,\n\n    // 上传图片，是否显示 base64 格式\n    uploadImgShowBase64: false,\n\n    // 上传图片，server 地址（如果有值，则 base64 格式的配置则失效）\n    // uploadImgServer: '/upload',\n\n    // 自定义配置 filename\n    uploadFileName: '',\n\n    // 上传图片的自定义参数\n    uploadImgParams: {\n        // token: 'abcdef12345'\n    },\n\n    // 上传图片的自定义header\n    uploadImgHeaders: {\n        // 'Accept': 'text/x-json'\n    },\n\n    // 配置 XHR withCredentials\n    withCredentials: false,\n\n    // 自定义上传图片超时时间 ms\n    uploadImgTimeout: 10000,\n\n    // 上传图片 hook \n    uploadImgHooks: {\n        // customInsert: function (insertLinkImg, result, editor) {\n        //     console.log('customInsert')\n        //     // 图片上传并返回结果，自定义插入图片的事件，而不是编辑器自动插入图片\n        //     const data = result.data1 || []\n        //     data.forEach(link => {\n        //         insertLinkImg(link)\n        //     })\n        // },\n        before: function before(xhr, editor, files) {\n            // 图片上传之前触发\n\n            // 如果返回的结果是 {prevent: true, msg: 'xxxx'} 则表示用户放弃上传\n            // return {\n            //     prevent: true,\n            //     msg: '放弃上传'\n            // }\n        },\n        success: function success(xhr, editor, result) {\n            // 图片上传并返回结果，图片插入成功之后触发\n        },\n        fail: function fail(xhr, editor, result) {\n            // 图片上传并返回结果，但图片插入错误时触发\n        },\n        error: function error(xhr, editor) {\n            // 图片上传出错时触发\n        },\n        timeout: function timeout(xhr, editor) {\n            // 图片上传超时时触发\n        }\n    },\n\n    // 是否上传七牛云，默认为 false\n    qiniu: false\n\n};\n\n/*\n    工具\n*/\n\n// 和 UA 相关的属性\nvar UA = {\n    _ua: navigator.userAgent,\n\n    // 是否 webkit\n    isWebkit: function isWebkit() {\n        var reg = /webkit/i;\n        return reg.test(this._ua);\n    },\n\n    // 是否 IE\n    isIE: function isIE() {\n        return 'ActiveXObject' in window;\n    }\n};\n\n// 遍历对象\nfunction objForEach(obj, fn) {\n    var key = void 0,\n        result = void 0;\n    for (key in obj) {\n        if (obj.hasOwnProperty(key)) {\n            result = fn.call(obj, key, obj[key]);\n            if (result === false) {\n                break;\n            }\n        }\n    }\n}\n\n// 遍历类数组\nfunction arrForEach(fakeArr, fn) {\n    var i = void 0,\n        item = void 0,\n        result = void 0;\n    var length = fakeArr.length || 0;\n    for (i = 0; i < length; i++) {\n        item = fakeArr[i];\n        result = fn.call(fakeArr, item, i);\n        if (result === false) {\n            break;\n        }\n    }\n}\n\n// 获取随机数\nfunction getRandom(prefix) {\n    return prefix + Math.random().toString().slice(2);\n}\n\n// 替换 html 特殊字符\nfunction replaceHtmlSymbol(html) {\n    if (html == null) {\n        return '';\n    }\n    return html.replace(/</gm, '&lt;').replace(/>/gm, '&gt;').replace(/\"/gm, '&quot;').replace(/(\\r\\n|\\r|\\n)/g, '<br/>');\n}\n\n// 返回百分比的格式\n\n\n// 判断是不是 function\nfunction isFunction(fn) {\n    return typeof fn === 'function';\n}\n\n/*\n    bold-menu\n*/\n// 构造函数\nfunction Bold(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-bold\"></i>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nBold.prototype = {\n    constructor: Bold,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n\n        if (isSeleEmpty) {\n            // 选区是空的，插入并选中一个“空白”\n            editor.selection.createEmptyRange();\n        }\n\n        // 执行 bold 命令\n        editor.cmd.do('bold');\n\n        if (isSeleEmpty) {\n            // 需要将选取折叠起来\n            editor.selection.collapseRange();\n            editor.selection.restoreSelection();\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('bold')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    替换多语言\n */\n\nvar replaceLang = function (editor, str) {\n    var langArgs = editor.config.langArgs || [];\n    var result = str;\n\n    langArgs.forEach(function (item) {\n        var reg = item.reg;\n        var val = item.val;\n\n        if (reg.test(result)) {\n            result = result.replace(reg, function () {\n                return val;\n            });\n        }\n    });\n\n    return result;\n};\n\n/*\n    droplist\n*/\nvar _emptyFn = function _emptyFn() {};\n\n// 构造函数\nfunction DropList(menu, opt) {\n    var _this = this;\n\n    // droplist 所依附的菜单\n    var editor = menu.editor;\n    this.menu = menu;\n    this.opt = opt;\n    // 容器\n    var $container = $('<div class=\"w-e-droplist\"></div>');\n\n    // 标题\n    var $title = opt.$title;\n    var titleHtml = void 0;\n    if ($title) {\n        // 替换多语言\n        titleHtml = $title.html();\n        titleHtml = replaceLang(editor, titleHtml);\n        $title.html(titleHtml);\n\n        $title.addClass('w-e-dp-title');\n        $container.append($title);\n    }\n\n    var list = opt.list || [];\n    var type = opt.type || 'list'; // 'list' 列表形式（如“标题”菜单） / 'inline-block' 块状形式（如“颜色”菜单）\n    var onClick = opt.onClick || _emptyFn;\n\n    // 加入 DOM 并绑定事件\n    var $list = $('<ul class=\"' + (type === 'list' ? 'w-e-list' : 'w-e-block') + '\"></ul>');\n    $container.append($list);\n    list.forEach(function (item) {\n        var $elem = item.$elem;\n\n        // 替换多语言\n        var elemHtml = $elem.html();\n        elemHtml = replaceLang(editor, elemHtml);\n        $elem.html(elemHtml);\n\n        var value = item.value;\n        var $li = $('<li class=\"w-e-item\"></li>');\n        if ($elem) {\n            $li.append($elem);\n            $list.append($li);\n            $li.on('click', function (e) {\n                onClick(value);\n\n                // 隐藏\n                _this.hideTimeoutId = setTimeout(function () {\n                    _this.hide();\n                }, 0);\n            });\n        }\n    });\n\n    // 绑定隐藏事件\n    $container.on('mouseleave', function (e) {\n        _this.hideTimeoutId = setTimeout(function () {\n            _this.hide();\n        }, 0);\n    });\n\n    // 记录属性\n    this.$container = $container;\n\n    // 基本属性\n    this._rendered = false;\n    this._show = false;\n}\n\n// 原型\nDropList.prototype = {\n    constructor: DropList,\n\n    // 显示（插入DOM）\n    show: function show() {\n        if (this.hideTimeoutId) {\n            // 清除之前的定时隐藏\n            clearTimeout(this.hideTimeoutId);\n        }\n\n        var menu = this.menu;\n        var $menuELem = menu.$elem;\n        var $container = this.$container;\n        if (this._show) {\n            return;\n        }\n        if (this._rendered) {\n            // 显示\n            $container.show();\n        } else {\n            // 加入 DOM 之前先定位位置\n            var menuHeight = $menuELem.getSizeData().height || 0;\n            var width = this.opt.width || 100; // 默认为 100\n            $container.css('margin-top', menuHeight + 'px').css('width', width + 'px');\n\n            // 加入到 DOM\n            $menuELem.append($container);\n            this._rendered = true;\n        }\n\n        // 修改属性\n        this._show = true;\n    },\n\n    // 隐藏（移除DOM）\n    hide: function hide() {\n        if (this.showTimeoutId) {\n            // 清除之前的定时显示\n            clearTimeout(this.showTimeoutId);\n        }\n\n        var $container = this.$container;\n        if (!this._show) {\n            return;\n        }\n        // 隐藏并需改属性\n        $container.hide();\n        this._show = false;\n    }\n};\n\n/*\n    menu - header\n*/\n// 构造函数\nfunction Head(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-header\"></i></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 100,\n        $title: $('<p>设置标题</p>'),\n        type: 'list', // droplist 以列表形式展示\n        list: [{ $elem: $('<h1>H1</h1>'), value: '<h1>' }, { $elem: $('<h2>H2</h2>'), value: '<h2>' }, { $elem: $('<h3>H3</h3>'), value: '<h3>' }, { $elem: $('<h4>H4</h4>'), value: '<h4>' }, { $elem: $('<h5>H5</h5>'), value: '<h5>' }, { $elem: $('<p>正文</p>'), value: '<p>' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 Head 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nHead.prototype = {\n    constructor: Head,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n\n        var $selectionElem = editor.selection.getSelectionContainerElem();\n        if (editor.$textElem.equal($selectionElem)) {\n            // 不能选中多行来设置标题，否则会出现问题\n            // 例如选中的是 <p>xxx</p><p>yyy</p> 来设置标题，设置之后会成为 <h1>xxx<br>yyy</h1> 不符合预期\n            return;\n        }\n\n        editor.cmd.do('formatBlock', value);\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var reg = /^h/i;\n        var cmdValue = editor.cmd.queryCommandValue('formatBlock');\n        if (reg.test(cmdValue)) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - fontSize\n*/\n\n// 构造函数\nfunction FontSize(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-text-heigh\"></i></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 160,\n        $title: $('<p>字号</p>'),\n        type: 'list', // droplist 以列表形式展示\n        list: [{ $elem: $('<span style=\"font-size: x-small;\">x-small</span>'), value: '1' }, { $elem: $('<span style=\"font-size: small;\">small</span>'), value: '2' }, { $elem: $('<span>normal</span>'), value: '3' }, { $elem: $('<span style=\"font-size: large;\">large</span>'), value: '4' }, { $elem: $('<span style=\"font-size: x-large;\">x-large</span>'), value: '5' }, { $elem: $('<span style=\"font-size: xx-large;\">xx-large</span>'), value: '6' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 FontSize 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nFontSize.prototype = {\n    constructor: FontSize,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        editor.cmd.do('fontSize', value);\n    }\n};\n\n/*\n    menu - fontName\n*/\n\n// 构造函数\nfunction FontName(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-font\"></i></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 获取配置的字体\n    var config = editor.config;\n    var fontNames = config.fontNames || [];\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 100,\n        $title: $('<p>字体</p>'),\n        type: 'list', // droplist 以列表形式展示\n        list: fontNames.map(function (fontName) {\n            return { $elem: $('<span style=\"font-family: ' + fontName + ';\">' + fontName + '</span>'), value: fontName };\n        }),\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 FontName 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nFontName.prototype = {\n    constructor: FontName,\n\n    _command: function _command(value) {\n        var editor = this.editor;\n        editor.cmd.do('fontName', value);\n    }\n};\n\n/*\n    panel\n*/\n\nvar emptyFn = function emptyFn() {};\n\n// 记录已经显示 panel 的菜单\nvar _isCreatedPanelMenus = [];\n\n// 构造函数\nfunction Panel(menu, opt) {\n    this.menu = menu;\n    this.opt = opt;\n}\n\n// 原型\nPanel.prototype = {\n    constructor: Panel,\n\n    // 显示（插入DOM）\n    show: function show() {\n        var _this = this;\n\n        var menu = this.menu;\n        if (_isCreatedPanelMenus.indexOf(menu) >= 0) {\n            // 该菜单已经创建了 panel 不能再创建\n            return;\n        }\n\n        var editor = menu.editor;\n        var $body = $('body');\n        var $textContainerElem = editor.$textContainerElem;\n        var opt = this.opt;\n\n        // panel 的容器\n        var $container = $('<div class=\"w-e-panel-container\"></div>');\n        var width = opt.width || 300; // 默认 300px\n        $container.css('width', width + 'px').css('margin-left', (0 - width) / 2 + 'px');\n\n        // 添加关闭按钮\n        var $closeBtn = $('<i class=\"w-e-icon-close w-e-panel-close\"></i>');\n        $container.append($closeBtn);\n        $closeBtn.on('click', function () {\n            _this.hide();\n        });\n\n        // 准备 tabs 容器\n        var $tabTitleContainer = $('<ul class=\"w-e-panel-tab-title\"></ul>');\n        var $tabContentContainer = $('<div class=\"w-e-panel-tab-content\"></div>');\n        $container.append($tabTitleContainer).append($tabContentContainer);\n\n        // 设置高度\n        var height = opt.height;\n        if (height) {\n            $tabContentContainer.css('height', height + 'px').css('overflow-y', 'auto');\n        }\n\n        // tabs\n        var tabs = opt.tabs || [];\n        var tabTitleArr = [];\n        var tabContentArr = [];\n        tabs.forEach(function (tab, tabIndex) {\n            if (!tab) {\n                return;\n            }\n            var title = tab.title || '';\n            var tpl = tab.tpl || '';\n\n            // 替换多语言\n            title = replaceLang(editor, title);\n            tpl = replaceLang(editor, tpl);\n\n            // 添加到 DOM\n            var $title = $('<li class=\"w-e-item\">' + title + '</li>');\n            $tabTitleContainer.append($title);\n            var $content = $(tpl);\n            $tabContentContainer.append($content);\n\n            // 记录到内存\n            $title._index = tabIndex;\n            tabTitleArr.push($title);\n            tabContentArr.push($content);\n\n            // 设置 active 项\n            if (tabIndex === 0) {\n                $title._active = true;\n                $title.addClass('w-e-active');\n            } else {\n                $content.hide();\n            }\n\n            // 绑定 tab 的事件\n            $title.on('click', function (e) {\n                if ($title._active) {\n                    return;\n                }\n                // 隐藏所有的 tab\n                tabTitleArr.forEach(function ($title) {\n                    $title._active = false;\n                    $title.removeClass('w-e-active');\n                });\n                tabContentArr.forEach(function ($content) {\n                    $content.hide();\n                });\n\n                // 显示当前的 tab\n                $title._active = true;\n                $title.addClass('w-e-active');\n                $content.show();\n            });\n        });\n\n        // 绑定关闭事件\n        $container.on('click', function (e) {\n            // 点击时阻止冒泡\n            e.stopPropagation();\n        });\n        $body.on('click', function (e) {\n            _this.hide();\n        });\n\n        // 添加到 DOM\n        $textContainerElem.append($container);\n\n        // 绑定 opt 的事件，只有添加到 DOM 之后才能绑定成功\n        tabs.forEach(function (tab, index) {\n            if (!tab) {\n                return;\n            }\n            var events = tab.events || [];\n            events.forEach(function (event) {\n                var selector = event.selector;\n                var type = event.type;\n                var fn = event.fn || emptyFn;\n                var $content = tabContentArr[index];\n                $content.find(selector).on(type, function (e) {\n                    e.stopPropagation();\n                    var needToHide = fn(e);\n                    // 执行完事件之后，是否要关闭 panel\n                    if (needToHide) {\n                        _this.hide();\n                    }\n                });\n            });\n        });\n\n        // focus 第一个 elem\n        var $inputs = $container.find('input[type=text],textarea');\n        if ($inputs.length) {\n            $inputs.get(0).focus();\n        }\n\n        // 添加到属性\n        this.$container = $container;\n\n        // 隐藏其他 panel\n        this._hideOtherPanels();\n        // 记录该 menu 已经创建了 panel\n        _isCreatedPanelMenus.push(menu);\n    },\n\n    // 隐藏（移除DOM）\n    hide: function hide() {\n        var menu = this.menu;\n        var $container = this.$container;\n        if ($container) {\n            $container.remove();\n        }\n\n        // 将该 menu 记录中移除\n        _isCreatedPanelMenus = _isCreatedPanelMenus.filter(function (item) {\n            if (item === menu) {\n                return false;\n            } else {\n                return true;\n            }\n        });\n    },\n\n    // 一个 panel 展示时，隐藏其他 panel\n    _hideOtherPanels: function _hideOtherPanels() {\n        if (!_isCreatedPanelMenus.length) {\n            return;\n        }\n        _isCreatedPanelMenus.forEach(function (menu) {\n            var panel = menu.panel || {};\n            if (panel.hide) {\n                panel.hide();\n            }\n        });\n    }\n};\n\n/*\n    menu - link\n*/\n// 构造函数\nfunction Link(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-link\"></i></div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nLink.prototype = {\n    constructor: Link,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        var editor = this.editor;\n        var $linkelem = void 0;\n\n        if (this._active) {\n            // 当前选区在链接里面\n            $linkelem = editor.selection.getSelectionContainerElem();\n            if (!$linkelem) {\n                return;\n            }\n            // 将该元素都包含在选取之内，以便后面整体替换\n            editor.selection.createRangeByElem($linkelem);\n            editor.selection.restoreSelection();\n            // 显示 panel\n            this._createPanel($linkelem.text(), $linkelem.attr('href'));\n        } else {\n            // 当前选区不在链接里面\n            if (editor.selection.isSelectionEmpty()) {\n                // 选区是空的，未选中内容\n                this._createPanel('', '');\n            } else {\n                // 选中内容了\n                this._createPanel(editor.selection.getSelectionText(), '');\n            }\n        }\n    },\n\n    // 创建 panel\n    _createPanel: function _createPanel(text, link) {\n        var _this = this;\n\n        // panel 中需要用到的id\n        var inputLinkId = getRandom('input-link');\n        var inputTextId = getRandom('input-text');\n        var btnOkId = getRandom('btn-ok');\n        var btnDelId = getRandom('btn-del');\n\n        // 是否显示“删除链接”\n        var delBtnDisplay = this._active ? 'inline-block' : 'none';\n\n        // 初始化并显示 panel\n        var panel = new Panel(this, {\n            width: 300,\n            // panel 中可包含多个 tab\n            tabs: [{\n                // tab 的标题\n                title: '链接',\n                // 模板\n                tpl: '<div>\\n                            <input id=\"' + inputTextId + '\" type=\"text\" class=\"block\" value=\"' + text + '\" placeholder=\"\\u94FE\\u63A5\\u6587\\u5B57\"/></td>\\n                            <input id=\"' + inputLinkId + '\" type=\"text\" class=\"block\" value=\"' + link + '\" placeholder=\"http://...\"/></td>\\n                            <div class=\"w-e-button-container\">\\n                                <button id=\"' + btnOkId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                                <button id=\"' + btnDelId + '\" class=\"gray right\" style=\"display:' + delBtnDisplay + '\">\\u5220\\u9664\\u94FE\\u63A5</button>\\n                            </div>\\n                        </div>',\n                // 事件绑定\n                events: [\n                // 插入链接\n                {\n                    selector: '#' + btnOkId,\n                    type: 'click',\n                    fn: function fn() {\n                        // 执行插入链接\n                        var $link = $('#' + inputLinkId);\n                        var $text = $('#' + inputTextId);\n                        var link = $link.val();\n                        var text = $text.val();\n                        _this._insertLink(text, link);\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                },\n                // 删除链接\n                {\n                    selector: '#' + btnDelId,\n                    type: 'click',\n                    fn: function fn() {\n                        // 执行删除链接\n                        _this._delLink();\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // tab end\n            ] // tabs end\n        });\n\n        // 显示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 删除当前链接\n    _delLink: function _delLink() {\n        if (!this._active) {\n            return;\n        }\n        var editor = this.editor;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var selectionText = editor.selection.getSelectionText();\n        editor.cmd.do('insertHTML', '<span>' + selectionText + '</span>');\n    },\n\n    // 插入链接\n    _insertLink: function _insertLink(text, link) {\n        var editor = this.editor;\n        var config = editor.config;\n        var linkCheck = config.linkCheck;\n        var checkResult = true; // 默认为 true\n        if (linkCheck && typeof linkCheck === 'function') {\n            checkResult = linkCheck(text, link);\n        }\n        if (checkResult === true) {\n            editor.cmd.do('insertHTML', '<a href=\"' + link + '\" target=\"_blank\">' + text + '</a>');\n        } else {\n            alert(checkResult);\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        if ($selectionELem.getNodeName() === 'A') {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    italic-menu\n*/\n// 构造函数\nfunction Italic(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-italic\"></i>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nItalic.prototype = {\n    constructor: Italic,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n\n        if (isSeleEmpty) {\n            // 选区是空的，插入并选中一个“空白”\n            editor.selection.createEmptyRange();\n        }\n\n        // 执行 italic 命令\n        editor.cmd.do('italic');\n\n        if (isSeleEmpty) {\n            // 需要将选取折叠起来\n            editor.selection.collapseRange();\n            editor.selection.restoreSelection();\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('italic')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    redo-menu\n*/\n// 构造函数\nfunction Redo(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-redo\"></i>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nRedo.prototype = {\n    constructor: Redo,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n\n        // 执行 redo 命令\n        editor.cmd.do('redo');\n    }\n};\n\n/*\n    strikeThrough-menu\n*/\n// 构造函数\nfunction StrikeThrough(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-strikethrough\"></i>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nStrikeThrough.prototype = {\n    constructor: StrikeThrough,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n\n        if (isSeleEmpty) {\n            // 选区是空的，插入并选中一个“空白”\n            editor.selection.createEmptyRange();\n        }\n\n        // 执行 strikeThrough 命令\n        editor.cmd.do('strikeThrough');\n\n        if (isSeleEmpty) {\n            // 需要将选取折叠起来\n            editor.selection.collapseRange();\n            editor.selection.restoreSelection();\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('strikeThrough')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    underline-menu\n*/\n// 构造函数\nfunction Underline(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-underline\"></i>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nUnderline.prototype = {\n    constructor: Underline,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n\n        if (isSeleEmpty) {\n            // 选区是空的，插入并选中一个“空白”\n            editor.selection.createEmptyRange();\n        }\n\n        // 执行 underline 命令\n        editor.cmd.do('underline');\n\n        if (isSeleEmpty) {\n            // 需要将选取折叠起来\n            editor.selection.collapseRange();\n            editor.selection.restoreSelection();\n        }\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('underline')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    undo-menu\n*/\n// 构造函数\nfunction Undo(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-undo\"></i>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nUndo.prototype = {\n    constructor: Undo,\n\n    // 点击事件\n    onClick: function onClick(e) {\n        // 点击菜单将触发这里\n\n        var editor = this.editor;\n\n        // 执行 undo 命令\n        editor.cmd.do('undo');\n    }\n};\n\n/*\n    menu - list\n*/\n// 构造函数\nfunction List(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-list2\"></i></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 120,\n        $title: $('<p>设置列表</p>'),\n        type: 'list', // droplist 以列表形式展示\n        list: [{ $elem: $('<span><i class=\"w-e-icon-list-numbered\"></i> 有序列表</span>'), value: 'insertOrderedList' }, { $elem: $('<span><i class=\"w-e-icon-list2\"></i> 无序列表</span>'), value: 'insertUnorderedList' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 List 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nList.prototype = {\n    constructor: List,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        editor.selection.restoreSelection();\n        if (editor.cmd.queryCommandState(value)) {\n            return;\n        }\n        editor.cmd.do(value);\n\n        // 验证列表是否被包裹在 <p> 之内\n        var $selectionElem = editor.selection.getSelectionContainerElem();\n        if ($selectionElem.getNodeName() === 'LI') {\n            $selectionElem = $selectionElem.parent();\n        }\n        if (/^ol|ul$/i.test($selectionElem.getNodeName()) === false) {\n            return;\n        }\n        if ($selectionElem.equal($textElem)) {\n            // 证明是顶级标签，没有被 <p> 包裹\n            return;\n        }\n        var $parent = $selectionElem.parent();\n        if ($parent.equal($textElem)) {\n            // $parent 是顶级标签，不能删除\n            return;\n        }\n\n        $selectionElem.insertAfter($parent);\n        $parent.remove();\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor.cmd.queryCommandState('insertUnOrderedList') || editor.cmd.queryCommandState('insertOrderedList')) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - justify\n*/\n// 构造函数\nfunction Justify(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-paragraph-left\"></i></div>');\n    this.type = 'droplist';\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 100,\n        $title: $('<p>对齐方式</p>'),\n        type: 'list', // droplist 以列表形式展示\n        list: [{ $elem: $('<span><i class=\"w-e-icon-paragraph-left\"></i> 靠左</span>'), value: 'justifyLeft' }, { $elem: $('<span><i class=\"w-e-icon-paragraph-center\"></i> 居中</span>'), value: 'justifyCenter' }, { $elem: $('<span><i class=\"w-e-icon-paragraph-right\"></i> 靠右</span>'), value: 'justifyRight' }],\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 List 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nJustify.prototype = {\n    constructor: Justify,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        editor.cmd.do(value);\n    }\n};\n\n/*\n    menu - Forecolor\n*/\n// 构造函数\nfunction ForeColor(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-pencil2\"></i></div>');\n    this.type = 'droplist';\n\n    // 获取配置的颜色\n    var config = editor.config;\n    var colors = config.colors || [];\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 120,\n        $title: $('<p>文字颜色</p>'),\n        type: 'inline-block', // droplist 内容以 block 形式展示\n        list: colors.map(function (color) {\n            return { $elem: $('<i style=\"color:' + color + ';\" class=\"w-e-icon-pencil2\"></i>'), value: color };\n        }),\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 ForeColor 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nForeColor.prototype = {\n    constructor: ForeColor,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        editor.cmd.do('foreColor', value);\n    }\n};\n\n/*\n    menu - BackColor\n*/\n// 构造函数\nfunction BackColor(editor) {\n    var _this = this;\n\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-paint-brush\"></i></div>');\n    this.type = 'droplist';\n\n    // 获取配置的颜色\n    var config = editor.config;\n    var colors = config.colors || [];\n\n    // 当前是否 active 状态\n    this._active = false;\n\n    // 初始化 droplist\n    this.droplist = new DropList(this, {\n        width: 120,\n        $title: $('<p>背景色</p>'),\n        type: 'inline-block', // droplist 内容以 block 形式展示\n        list: colors.map(function (color) {\n            return { $elem: $('<i style=\"color:' + color + ';\" class=\"w-e-icon-paint-brush\"></i>'), value: color };\n        }),\n        onClick: function onClick(value) {\n            // 注意 this 是指向当前的 BackColor 对象\n            _this._command(value);\n        }\n    });\n}\n\n// 原型\nBackColor.prototype = {\n    constructor: BackColor,\n\n    // 执行命令\n    _command: function _command(value) {\n        var editor = this.editor;\n        editor.cmd.do('backColor', value);\n    }\n};\n\n/*\n    menu - quote\n*/\n// 构造函数\nfunction Quote(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-quotes-left\"></i>\\n        </div>');\n    this.type = 'click';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nQuote.prototype = {\n    constructor: Quote,\n\n    onClick: function onClick(e) {\n        var editor = this.editor;\n        var $selectionElem = editor.selection.getSelectionContainerElem();\n        var nodeName = $selectionElem.getNodeName();\n\n        if (!UA.isIE()) {\n            if (nodeName === 'BLOCKQUOTE') {\n                // 撤销 quote\n                editor.cmd.do('formatBlock', '<P>');\n            } else {\n                // 转换为 quote\n                editor.cmd.do('formatBlock', '<BLOCKQUOTE>');\n            }\n            return;\n        }\n\n        // IE 中不支持 formatBlock <BLOCKQUOTE> ，要用其他方式兼容\n        var content = void 0,\n            $targetELem = void 0;\n        if (nodeName === 'P') {\n            // 将 P 转换为 quote\n            content = $selectionElem.text();\n            $targetELem = $('<blockquote>' + content + '</blockquote>');\n            $targetELem.insertAfter($selectionElem);\n            $selectionElem.remove();\n            return;\n        }\n        if (nodeName === 'BLOCKQUOTE') {\n            // 撤销 quote\n            content = $selectionElem.text();\n            $targetELem = $('<p>' + content + '</p>');\n            $targetELem.insertAfter($selectionElem);\n            $selectionElem.remove();\n        }\n    },\n\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var reg = /^BLOCKQUOTE$/i;\n        var cmdValue = editor.cmd.queryCommandValue('formatBlock');\n        if (reg.test(cmdValue)) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - code\n*/\n// 构造函数\nfunction Code(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-terminal\"></i>\\n        </div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nCode.prototype = {\n    constructor: Code,\n\n    onClick: function onClick(e) {\n        var editor = this.editor;\n        var $startElem = editor.selection.getSelectionStartElem();\n        var $endElem = editor.selection.getSelectionEndElem();\n        var isSeleEmpty = editor.selection.isSelectionEmpty();\n        var selectionText = editor.selection.getSelectionText();\n        var $code = void 0;\n\n        if (!$startElem.equal($endElem)) {\n            // 跨元素选择，不做处理\n            editor.selection.restoreSelection();\n            return;\n        }\n        if (!isSeleEmpty) {\n            // 选取不是空，用 <code> 包裹即可\n            $code = $('<code>' + selectionText + '</code>');\n            editor.cmd.do('insertElem', $code);\n            editor.selection.createRangeByElem($code, false);\n            editor.selection.restoreSelection();\n            return;\n        }\n\n        // 选取是空，且没有夸元素选择，则插入 <pre><code></code></prev>\n        if (this._active) {\n            // 选中状态，将编辑内容\n            this._createPanel($startElem.html());\n        } else {\n            // 未选中状态，将创建内容\n            this._createPanel();\n        }\n    },\n\n    _createPanel: function _createPanel(value) {\n        var _this = this;\n\n        // value - 要编辑的内容\n        value = value || '';\n        var type = !value ? 'new' : 'edit';\n        var textId = getRandom('texxt');\n        var btnId = getRandom('btn');\n\n        var panel = new Panel(this, {\n            width: 500,\n            // 一个 Panel 包含多个 tab\n            tabs: [{\n                // 标题\n                title: '插入代码',\n                // 模板\n                tpl: '<div>\\n                        <textarea id=\"' + textId + '\" style=\"height:145px;;\">' + value + '</textarea>\\n                        <div class=\"w-e-button-container\">\\n                            <button id=\"' + btnId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                        </div>\\n                    <div>',\n                // 事件绑定\n                events: [\n                // 插入代码\n                {\n                    selector: '#' + btnId,\n                    type: 'click',\n                    fn: function fn() {\n                        var $text = $('#' + textId);\n                        var text = $text.val() || $text.html();\n                        text = replaceHtmlSymbol(text);\n                        if (type === 'new') {\n                            // 新插入\n                            _this._insertCode(text);\n                        } else {\n                            // 编辑更新\n                            _this._updateCode(text);\n                        }\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // first tab end\n            ] // tabs end\n        }); // new Panel end\n\n        // 显示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 插入代码\n    _insertCode: function _insertCode(value) {\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', '<pre><code>' + value + '</code></pre><p><br></p>');\n    },\n\n    // 更新代码\n    _updateCode: function _updateCode(value) {\n        var editor = this.editor;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        $selectionELem.html(value);\n        editor.selection.restoreSelection();\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var $parentElem = $selectionELem.parent();\n        if ($selectionELem.getNodeName() === 'CODE' && $parentElem.getNodeName() === 'PRE') {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - emoticon\n*/\n// 构造函数\nfunction Emoticon(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\">\\n            <i class=\"w-e-icon-happy\"></i>\\n        </div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nEmoticon.prototype = {\n    constructor: Emoticon,\n\n    onClick: function onClick() {\n        this._createPanel();\n    },\n\n    _createPanel: function _createPanel() {\n        var _this = this;\n\n        var editor = this.editor;\n        var config = editor.config;\n        // 获取表情配置\n        var emotions = config.emotions || [];\n\n        // 创建表情 dropPanel 的配置\n        var tabConfig = [];\n        emotions.forEach(function (emotData) {\n            var emotType = emotData.type;\n            var content = emotData.content || [];\n\n            // 这一组表情最终拼接出来的 html\n            var faceHtml = '';\n\n            // emoji 表情\n            if (emotType === 'emoji') {\n                content.forEach(function (item) {\n                    if (item) {\n                        faceHtml += '<span class=\"w-e-item\">' + item + '</span>';\n                    }\n                });\n            }\n            // 图片表情\n            if (emotType === 'image') {\n                content.forEach(function (item) {\n                    var src = item.src;\n                    var alt = item.alt;\n                    if (src) {\n                        // 加一个 data-w-e 属性，点击图片的时候不再提示编辑图片\n                        faceHtml += '<span class=\"w-e-item\"><img src=\"' + src + '\" alt=\"' + alt + '\" data-w-e=\"1\"/></span>';\n                    }\n                });\n            }\n\n            tabConfig.push({\n                title: emotData.title,\n                tpl: '<div class=\"w-e-emoticon-container\">' + faceHtml + '</div>',\n                events: [{\n                    selector: 'span.w-e-item',\n                    type: 'click',\n                    fn: function fn(e) {\n                        var target = e.target;\n                        var $target = $(target);\n                        var nodeName = $target.getNodeName();\n\n                        var insertHtml = void 0;\n                        if (nodeName === 'IMG') {\n                            // 插入图片\n                            insertHtml = $target.parent().html();\n                        } else {\n                            // 插入 emoji\n                            insertHtml = '<span>' + $target.html() + '</span>';\n                        }\n\n                        _this._insert(insertHtml);\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            });\n        });\n\n        var panel = new Panel(this, {\n            width: 300,\n            height: 200,\n            // 一个 Panel 包含多个 tab\n            tabs: tabConfig\n        });\n\n        // 显示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 插入表情\n    _insert: function _insert(emotHtml) {\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', emotHtml);\n    }\n};\n\n/*\n    menu - table\n*/\n// 构造函数\nfunction Table(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-table2\"></i></div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nTable.prototype = {\n    constructor: Table,\n\n    onClick: function onClick() {\n        if (this._active) {\n            // 编辑现有表格\n            this._createEditPanel();\n        } else {\n            // 插入新表格\n            this._createInsertPanel();\n        }\n    },\n\n    // 创建插入新表格的 panel\n    _createInsertPanel: function _createInsertPanel() {\n        var _this = this;\n\n        // 用到的 id\n        var btnInsertId = getRandom('btn');\n        var textRowNum = getRandom('row');\n        var textColNum = getRandom('col');\n\n        var panel = new Panel(this, {\n            width: 250,\n            // panel 包含多个 tab\n            tabs: [{\n                // 标题\n                title: '插入表格',\n                // 模板\n                tpl: '<div>\\n                        <p style=\"text-align:left; padding:5px 0;\">\\n                            \\u521B\\u5EFA\\n                            <input id=\"' + textRowNum + '\" type=\"text\" value=\"5\" style=\"width:40px;text-align:center;\"/>\\n                            \\u884C\\n                            <input id=\"' + textColNum + '\" type=\"text\" value=\"5\" style=\"width:40px;text-align:center;\"/>\\n                            \\u5217\\u7684\\u8868\\u683C\\n                        </p>\\n                        <div class=\"w-e-button-container\">\\n                            <button id=\"' + btnInsertId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                        </div>\\n                    </div>',\n                // 事件绑定\n                events: [{\n                    // 点击按钮，插入表格\n                    selector: '#' + btnInsertId,\n                    type: 'click',\n                    fn: function fn() {\n                        var rowNum = parseInt($('#' + textRowNum).val());\n                        var colNum = parseInt($('#' + textColNum).val());\n\n                        if (rowNum && colNum && rowNum > 0 && colNum > 0) {\n                            // form 数据有效\n                            _this._insert(rowNum, colNum);\n                        }\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // first tab end\n            ] // tabs end\n        }); // panel end\n\n        // 展示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 插入表格\n    _insert: function _insert(rowNum, colNum) {\n        // 拼接 table 模板\n        var r = void 0,\n            c = void 0;\n        var html = '<table border=\"0\" width=\"100%\" cellpadding=\"0\" cellspacing=\"0\">';\n        for (r = 0; r < rowNum; r++) {\n            html += '<tr>';\n            if (r === 0) {\n                for (c = 0; c < colNum; c++) {\n                    html += '<th>&nbsp;</th>';\n                }\n            } else {\n                for (c = 0; c < colNum; c++) {\n                    html += '<td>&nbsp;</td>';\n                }\n            }\n            html += '</tr>';\n        }\n        html += '</table><p><br></p>';\n\n        // 执行命令\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', html);\n\n        // 防止 firefox 下出现 resize 的控制点\n        editor.cmd.do('enableObjectResizing', false);\n        editor.cmd.do('enableInlineTableEditing', false);\n    },\n\n    // 创建编辑表格的 panel\n    _createEditPanel: function _createEditPanel() {\n        var _this2 = this;\n\n        // 可用的 id\n        var addRowBtnId = getRandom('add-row');\n        var addColBtnId = getRandom('add-col');\n        var delRowBtnId = getRandom('del-row');\n        var delColBtnId = getRandom('del-col');\n        var delTableBtnId = getRandom('del-table');\n\n        // 创建 panel 对象\n        var panel = new Panel(this, {\n            width: 320,\n            // panel 包含多个 tab\n            tabs: [{\n                // 标题\n                title: '编辑表格',\n                // 模板\n                tpl: '<div>\\n                        <div class=\"w-e-button-container\" style=\"border-bottom:1px solid #f1f1f1;padding-bottom:5px;margin-bottom:5px;\">\\n                            <button id=\"' + addRowBtnId + '\" class=\"left\">\\u589E\\u52A0\\u884C</button>\\n                            <button id=\"' + delRowBtnId + '\" class=\"red left\">\\u5220\\u9664\\u884C</button>\\n                            <button id=\"' + addColBtnId + '\" class=\"left\">\\u589E\\u52A0\\u5217</button>\\n                            <button id=\"' + delColBtnId + '\" class=\"red left\">\\u5220\\u9664\\u5217</button>\\n                        </div>\\n                        <div class=\"w-e-button-container\">\\n                            <button id=\"' + delTableBtnId + '\" class=\"gray left\">\\u5220\\u9664\\u8868\\u683C</button>\\n                        </dv>\\n                    </div>',\n                // 事件绑定\n                events: [{\n                    // 增加行\n                    selector: '#' + addRowBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._addRow();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }, {\n                    // 增加列\n                    selector: '#' + addColBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._addCol();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }, {\n                    // 删除行\n                    selector: '#' + delRowBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._delRow();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }, {\n                    // 删除列\n                    selector: '#' + delColBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._delCol();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }, {\n                    // 删除表格\n                    selector: '#' + delTableBtnId,\n                    type: 'click',\n                    fn: function fn() {\n                        _this2._delTable();\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            }]\n        });\n        // 显示 panel\n        panel.show();\n    },\n\n    // 获取选中的单元格的位置信息\n    _getLocationData: function _getLocationData() {\n        var result = {};\n        var editor = this.editor;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var nodeName = $selectionELem.getNodeName();\n        if (nodeName !== 'TD' && nodeName !== 'TH') {\n            return;\n        }\n\n        // 获取 td index\n        var $tr = $selectionELem.parent();\n        var $tds = $tr.children();\n        var tdLength = $tds.length;\n        $tds.forEach(function (td, index) {\n            if (td === $selectionELem[0]) {\n                // 记录并跳出循环\n                result.td = {\n                    index: index,\n                    elem: td,\n                    length: tdLength\n                };\n                return false;\n            }\n        });\n\n        // 获取 tr index\n        var $tbody = $tr.parent();\n        var $trs = $tbody.children();\n        var trLength = $trs.length;\n        $trs.forEach(function (tr, index) {\n            if (tr === $tr[0]) {\n                // 记录并跳出循环\n                result.tr = {\n                    index: index,\n                    elem: tr,\n                    length: trLength\n                };\n                return false;\n            }\n        });\n\n        // 返回结果\n        return result;\n    },\n\n    // 增加行\n    _addRow: function _addRow() {\n        // 获取当前单元格的位置信息\n        var locationData = this._getLocationData();\n        if (!locationData) {\n            return;\n        }\n        var trData = locationData.tr;\n        var $currentTr = $(trData.elem);\n        var tdData = locationData.td;\n        var tdLength = tdData.length;\n\n        // 拼接即将插入的字符串\n        var newTr = document.createElement('tr');\n        var tpl = '',\n            i = void 0;\n        for (i = 0; i < tdLength; i++) {\n            tpl += '<td>&nbsp;</td>';\n        }\n        newTr.innerHTML = tpl;\n        // 插入\n        $(newTr).insertAfter($currentTr);\n    },\n\n    // 增加列\n    _addCol: function _addCol() {\n        // 获取当前单元格的位置信息\n        var locationData = this._getLocationData();\n        if (!locationData) {\n            return;\n        }\n        var trData = locationData.tr;\n        var tdData = locationData.td;\n        var tdIndex = tdData.index;\n        var $currentTr = $(trData.elem);\n        var $trParent = $currentTr.parent();\n        var $trs = $trParent.children();\n\n        // 遍历所有行\n        $trs.forEach(function (tr) {\n            var $tr = $(tr);\n            var $tds = $tr.children();\n            var $currentTd = $tds.get(tdIndex);\n            var name = $currentTd.getNodeName().toLowerCase();\n\n            // new 一个 td，并插入\n            var newTd = document.createElement(name);\n            $(newTd).insertAfter($currentTd);\n        });\n    },\n\n    // 删除行\n    _delRow: function _delRow() {\n        // 获取当前单元格的位置信息\n        var locationData = this._getLocationData();\n        if (!locationData) {\n            return;\n        }\n        var trData = locationData.tr;\n        var $currentTr = $(trData.elem);\n        $currentTr.remove();\n    },\n\n    // 删除列\n    _delCol: function _delCol() {\n        // 获取当前单元格的位置信息\n        var locationData = this._getLocationData();\n        if (!locationData) {\n            return;\n        }\n        var trData = locationData.tr;\n        var tdData = locationData.td;\n        var tdIndex = tdData.index;\n        var $currentTr = $(trData.elem);\n        var $trParent = $currentTr.parent();\n        var $trs = $trParent.children();\n\n        // 遍历所有行\n        $trs.forEach(function (tr) {\n            var $tr = $(tr);\n            var $tds = $tr.children();\n            var $currentTd = $tds.get(tdIndex);\n            // 删除\n            $currentTd.remove();\n        });\n    },\n\n    // 删除表格\n    _delTable: function _delTable() {\n        var editor = this.editor;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var $table = $selectionELem.parentUntil('table');\n        if (!$table) {\n            return;\n        }\n        $table.remove();\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        var $selectionELem = editor.selection.getSelectionContainerElem();\n        if (!$selectionELem) {\n            return;\n        }\n        var nodeName = $selectionELem.getNodeName();\n        if (nodeName === 'TD' || nodeName === 'TH') {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    menu - video\n*/\n// 构造函数\nfunction Video(editor) {\n    this.editor = editor;\n    this.$elem = $('<div class=\"w-e-menu\"><i class=\"w-e-icon-play\"></i></div>');\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nVideo.prototype = {\n    constructor: Video,\n\n    onClick: function onClick() {\n        this._createPanel();\n    },\n\n    _createPanel: function _createPanel() {\n        var _this = this;\n\n        // 创建 id\n        var textValId = getRandom('text-val');\n        var btnId = getRandom('btn');\n\n        // 创建 panel\n        var panel = new Panel(this, {\n            width: 350,\n            // 一个 panel 多个 tab\n            tabs: [{\n                // 标题\n                title: '插入视频',\n                // 模板\n                tpl: '<div>\\n                        <input id=\"' + textValId + '\" type=\"text\" class=\"block\" placeholder=\"\\u683C\\u5F0F\\u5982\\uFF1A<iframe src=... ></iframe>\"/>\\n                        <div class=\"w-e-button-container\">\\n                            <button id=\"' + btnId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                        </div>\\n                    </div>',\n                // 事件绑定\n                events: [{\n                    selector: '#' + btnId,\n                    type: 'click',\n                    fn: function fn() {\n                        var $text = $('#' + textValId);\n                        var val = $text.val().trim();\n\n                        // 测试用视频地址\n                        // <iframe height=498 width=510 src='http://player.youku.com/embed/XMjcwMzc3MzM3Mg==' frameborder=0 'allowfullscreen'></iframe>\n\n                        if (val) {\n                            // 插入视频\n                            _this._insert(val);\n                        }\n\n                        // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                        return true;\n                    }\n                }]\n            } // first tab end\n            ] // tabs end\n        }); // panel end\n\n        // 显示 panel\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 插入视频\n    _insert: function _insert(val) {\n        var editor = this.editor;\n        editor.cmd.do('insertHTML', val + '<p><br></p>');\n    }\n};\n\n/*\n    menu - img\n*/\n// 构造函数\nfunction Image(editor) {\n    this.editor = editor;\n    var imgMenuId = getRandom('w-e-img');\n    this.$elem = $('<div class=\"w-e-menu\" id=\"' + imgMenuId + '\"><i class=\"w-e-icon-image\"></i></div>');\n    editor.imgMenuId = imgMenuId;\n    this.type = 'panel';\n\n    // 当前是否 active 状态\n    this._active = false;\n}\n\n// 原型\nImage.prototype = {\n    constructor: Image,\n\n    onClick: function onClick() {\n        var editor = this.editor;\n        var config = editor.config;\n        if (config.qiniu) {\n            return;\n        }\n        if (this._active) {\n            this._createEditPanel();\n        } else {\n            this._createInsertPanel();\n        }\n    },\n\n    _createEditPanel: function _createEditPanel() {\n        var editor = this.editor;\n\n        // id\n        var width30 = getRandom('width-30');\n        var width50 = getRandom('width-50');\n        var width100 = getRandom('width-100');\n        var delBtn = getRandom('del-btn');\n\n        // tab 配置\n        var tabsConfig = [{\n            title: '编辑图片',\n            tpl: '<div>\\n                    <div class=\"w-e-button-container\" style=\"border-bottom:1px solid #f1f1f1;padding-bottom:5px;margin-bottom:5px;\">\\n                        <span style=\"float:left;font-size:14px;margin:4px 5px 0 5px;color:#333;\">\\u6700\\u5927\\u5BBD\\u5EA6\\uFF1A</span>\\n                        <button id=\"' + width30 + '\" class=\"left\">30%</button>\\n                        <button id=\"' + width50 + '\" class=\"left\">50%</button>\\n                        <button id=\"' + width100 + '\" class=\"left\">100%</button>\\n                    </div>\\n                    <div class=\"w-e-button-container\">\\n                        <button id=\"' + delBtn + '\" class=\"gray left\">\\u5220\\u9664\\u56FE\\u7247</button>\\n                    </dv>\\n                </div>',\n            events: [{\n                selector: '#' + width30,\n                type: 'click',\n                fn: function fn() {\n                    var $img = editor._selectedImg;\n                    if ($img) {\n                        $img.css('max-width', '30%');\n                    }\n                    // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                    return true;\n                }\n            }, {\n                selector: '#' + width50,\n                type: 'click',\n                fn: function fn() {\n                    var $img = editor._selectedImg;\n                    if ($img) {\n                        $img.css('max-width', '50%');\n                    }\n                    // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                    return true;\n                }\n            }, {\n                selector: '#' + width100,\n                type: 'click',\n                fn: function fn() {\n                    var $img = editor._selectedImg;\n                    if ($img) {\n                        $img.css('max-width', '100%');\n                    }\n                    // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                    return true;\n                }\n            }, {\n                selector: '#' + delBtn,\n                type: 'click',\n                fn: function fn() {\n                    var $img = editor._selectedImg;\n                    if ($img) {\n                        $img.remove();\n                    }\n                    // 返回 true，表示该事件执行完之后，panel 要关闭。否则 panel 不会关闭\n                    return true;\n                }\n            }]\n        }];\n\n        // 创建 panel 并显示\n        var panel = new Panel(this, {\n            width: 300,\n            tabs: tabsConfig\n        });\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    _createInsertPanel: function _createInsertPanel() {\n        var editor = this.editor;\n        var uploadImg = editor.uploadImg;\n        var config = editor.config;\n\n        // id\n        var upTriggerId = getRandom('up-trigger');\n        var upFileId = getRandom('up-file');\n        var linkUrlId = getRandom('link-url');\n        var linkBtnId = getRandom('link-btn');\n\n        // tabs 的配置\n        var tabsConfig = [{\n            title: '上传图片',\n            tpl: '<div class=\"w-e-up-img-container\">\\n                    <div id=\"' + upTriggerId + '\" class=\"w-e-up-btn\">\\n                        <i class=\"w-e-icon-upload2\"></i>\\n                    </div>\\n                    <div style=\"display:none;\">\\n                        <input id=\"' + upFileId + '\" type=\"file\" multiple=\"multiple\" accept=\"image/jpg,image/jpeg,image/png,image/gif,image/bmp\"/>\\n                    </div>\\n                </div>',\n            events: [{\n                // 触发选择图片\n                selector: '#' + upTriggerId,\n                type: 'click',\n                fn: function fn() {\n                    var $file = $('#' + upFileId);\n                    var fileElem = $file[0];\n                    if (fileElem) {\n                        fileElem.click();\n                    } else {\n                        // 返回 true 可关闭 panel\n                        return true;\n                    }\n                }\n            }, {\n                // 选择图片完毕\n                selector: '#' + upFileId,\n                type: 'change',\n                fn: function fn() {\n                    var $file = $('#' + upFileId);\n                    var fileElem = $file[0];\n                    if (!fileElem) {\n                        // 返回 true 可关闭 panel\n                        return true;\n                    }\n\n                    // 获取选中的 file 对象列表\n                    var fileList = fileElem.files;\n                    if (fileList.length) {\n                        uploadImg.uploadImg(fileList);\n                    }\n\n                    // 返回 true 可关闭 panel\n                    return true;\n                }\n            }]\n        }, // first tab end\n        {\n            title: '网络图片',\n            tpl: '<div>\\n                    <input id=\"' + linkUrlId + '\" type=\"text\" class=\"block\" placeholder=\"\\u56FE\\u7247\\u94FE\\u63A5\"/></td>\\n                    <div class=\"w-e-button-container\">\\n                        <button id=\"' + linkBtnId + '\" class=\"right\">\\u63D2\\u5165</button>\\n                    </div>\\n                </div>',\n            events: [{\n                selector: '#' + linkBtnId,\n                type: 'click',\n                fn: function fn() {\n                    var $linkUrl = $('#' + linkUrlId);\n                    var url = $linkUrl.val().trim();\n\n                    if (url) {\n                        uploadImg.insertLinkImg(url);\n                    }\n\n                    // 返回 true 表示函数执行结束之后关闭 panel\n                    return true;\n                }\n            }]\n        } // second tab end\n        ]; // tabs end\n\n        // 判断 tabs 的显示\n        var tabsConfigResult = [];\n        if ((config.uploadImgShowBase64 || config.uploadImgServer || config.customUploadImg) && window.FileReader) {\n            // 显示“上传图片”\n            tabsConfigResult.push(tabsConfig[0]);\n        }\n        if (config.showLinkImg) {\n            // 显示“网络图片”\n            tabsConfigResult.push(tabsConfig[1]);\n        }\n\n        // 创建 panel 并显示\n        var panel = new Panel(this, {\n            width: 300,\n            tabs: tabsConfigResult\n        });\n        panel.show();\n\n        // 记录属性\n        this.panel = panel;\n    },\n\n    // 试图改变 active 状态\n    tryChangeActive: function tryChangeActive(e) {\n        var editor = this.editor;\n        var $elem = this.$elem;\n        if (editor._selectedImg) {\n            this._active = true;\n            $elem.addClass('w-e-active');\n        } else {\n            this._active = false;\n            $elem.removeClass('w-e-active');\n        }\n    }\n};\n\n/*\n    所有菜单的汇总\n*/\n\n// 存储菜单的构造函数\nvar MenuConstructors = {};\n\nMenuConstructors.bold = Bold;\n\nMenuConstructors.head = Head;\n\nMenuConstructors.fontSize = FontSize;\n\nMenuConstructors.fontName = FontName;\n\nMenuConstructors.link = Link;\n\nMenuConstructors.italic = Italic;\n\nMenuConstructors.redo = Redo;\n\nMenuConstructors.strikeThrough = StrikeThrough;\n\nMenuConstructors.underline = Underline;\n\nMenuConstructors.undo = Undo;\n\nMenuConstructors.list = List;\n\nMenuConstructors.justify = Justify;\n\nMenuConstructors.foreColor = ForeColor;\n\nMenuConstructors.backColor = BackColor;\n\nMenuConstructors.quote = Quote;\n\nMenuConstructors.code = Code;\n\nMenuConstructors.emoticon = Emoticon;\n\nMenuConstructors.table = Table;\n\nMenuConstructors.video = Video;\n\nMenuConstructors.image = Image;\n\n/*\n    菜单集合\n*/\n// 构造函数\nfunction Menus(editor) {\n    this.editor = editor;\n    this.menus = {};\n}\n\n// 修改原型\nMenus.prototype = {\n    constructor: Menus,\n\n    // 初始化菜单\n    init: function init() {\n        var _this = this;\n\n        var editor = this.editor;\n        var config = editor.config || {};\n        var configMenus = config.menus || []; // 获取配置中的菜单\n\n        // 根据配置信息，创建菜单\n        configMenus.forEach(function (menuKey) {\n            var MenuConstructor = MenuConstructors[menuKey];\n            if (MenuConstructor && typeof MenuConstructor === 'function') {\n                // 创建单个菜单\n                _this.menus[menuKey] = new MenuConstructor(editor);\n            }\n        });\n\n        // 添加到菜单栏\n        this._addToToolbar();\n\n        // 绑定事件\n        this._bindEvent();\n    },\n\n    // 添加到菜单栏\n    _addToToolbar: function _addToToolbar() {\n        var editor = this.editor;\n        var $toolbarElem = editor.$toolbarElem;\n        var menus = this.menus;\n        var config = editor.config;\n        // config.zIndex 是配置的编辑区域的 z-index，菜单的 z-index 得在其基础上 +1\n        var zIndex = config.zIndex + 1;\n        objForEach(menus, function (key, menu) {\n            var $elem = menu.$elem;\n            if ($elem) {\n                // 设置 z-index\n                $elem.css('z-index', zIndex);\n                $toolbarElem.append($elem);\n            }\n        });\n    },\n\n    // 绑定菜单 click mouseenter 事件\n    _bindEvent: function _bindEvent() {\n        var menus = this.menus;\n        var editor = this.editor;\n        objForEach(menus, function (key, menu) {\n            var type = menu.type;\n            if (!type) {\n                return;\n            }\n            var $elem = menu.$elem;\n            var droplist = menu.droplist;\n            var panel = menu.panel;\n\n            // 点击类型，例如 bold\n            if (type === 'click' && menu.onClick) {\n                $elem.on('click', function (e) {\n                    if (editor.selection.getRange() == null) {\n                        return;\n                    }\n                    menu.onClick(e);\n                });\n            }\n\n            // 下拉框，例如 head\n            if (type === 'droplist' && droplist) {\n                $elem.on('mouseenter', function (e) {\n                    if (editor.selection.getRange() == null) {\n                        return;\n                    }\n                    // 显示\n                    droplist.showTimeoutId = setTimeout(function () {\n                        droplist.show();\n                    }, 200);\n                }).on('mouseleave', function (e) {\n                    // 隐藏\n                    droplist.hideTimeoutId = setTimeout(function () {\n                        droplist.hide();\n                    }, 0);\n                });\n            }\n\n            // 弹框类型，例如 link\n            if (type === 'panel' && menu.onClick) {\n                $elem.on('click', function (e) {\n                    e.stopPropagation();\n                    if (editor.selection.getRange() == null) {\n                        return;\n                    }\n                    // 在自定义事件中显示 panel\n                    menu.onClick(e);\n                });\n            }\n        });\n    },\n\n    // 尝试修改菜单状态\n    changeActive: function changeActive() {\n        var menus = this.menus;\n        objForEach(menus, function (key, menu) {\n            if (menu.tryChangeActive) {\n                setTimeout(function () {\n                    menu.tryChangeActive();\n                }, 100);\n            }\n        });\n    }\n};\n\n/*\n    粘贴信息的处理\n*/\n\n// 获取粘贴的纯文本\nfunction getPasteText(e) {\n    var clipboardData = e.clipboardData || e.originalEvent && e.originalEvent.clipboardData;\n    var pasteText = void 0;\n    if (clipboardData == null) {\n        pasteText = window.clipboardData && window.clipboardData.getData('text');\n    } else {\n        pasteText = clipboardData.getData('text/plain');\n    }\n\n    return replaceHtmlSymbol(pasteText);\n}\n\n// 获取粘贴的html\nfunction getPasteHtml(e, filterStyle, ignoreImg) {\n    var clipboardData = e.clipboardData || e.originalEvent && e.originalEvent.clipboardData;\n    var pasteText = void 0,\n        pasteHtml = void 0;\n    if (clipboardData == null) {\n        pasteText = window.clipboardData && window.clipboardData.getData('text');\n    } else {\n        pasteText = clipboardData.getData('text/plain');\n        pasteHtml = clipboardData.getData('text/html');\n    }\n    if (!pasteHtml && pasteText) {\n        pasteHtml = '<p>' + replaceHtmlSymbol(pasteText) + '</p>';\n    }\n    if (!pasteHtml) {\n        return;\n    }\n\n    // 过滤word中状态过来的无用字符\n    var docSplitHtml = pasteHtml.split('</html>');\n    if (docSplitHtml.length === 2) {\n        pasteHtml = docSplitHtml[0];\n    }\n\n    // 过滤无用标签\n    pasteHtml = pasteHtml.replace(/<(meta|script|link).+?>/igm, '');\n    // 去掉注释\n    pasteHtml = pasteHtml.replace(/<!--.*?-->/mg, '');\n    // 过滤 data-xxx 属性\n    pasteHtml = pasteHtml.replace(/\\s?data-.+?=('|\").+?('|\")/igm, '');\n\n    if (ignoreImg) {\n        // 忽略图片\n        pasteHtml = pasteHtml.replace(/<img.+?>/igm, '');\n    }\n\n    if (filterStyle) {\n        // 过滤样式\n        pasteHtml = pasteHtml.replace(/\\s?(class|style)=('|\").*?('|\")/igm, '');\n    } else {\n        // 保留样式\n        pasteHtml = pasteHtml.replace(/\\s?class=('|\").*?('|\")/igm, '');\n    }\n\n    return pasteHtml;\n}\n\n// 获取粘贴的图片文件\nfunction getPasteImgs(e) {\n    var result = [];\n    var txt = getPasteText(e);\n    if (txt) {\n        // 有文字，就忽略图片\n        return result;\n    }\n\n    var clipboardData = e.clipboardData || e.originalEvent && e.originalEvent.clipboardData || {};\n    var items = clipboardData.items;\n    if (!items) {\n        return result;\n    }\n\n    objForEach(items, function (key, value) {\n        var type = value.type;\n        if (/image/i.test(type)) {\n            result.push(value.getAsFile());\n        }\n    });\n\n    return result;\n}\n\n/*\n    编辑区域\n*/\n\n// 获取一个 elem.childNodes 的 JSON 数据\nfunction getChildrenJSON($elem) {\n    var result = [];\n    var $children = $elem.childNodes() || []; // 注意 childNodes() 可以获取文本节点\n    $children.forEach(function (curElem) {\n        var elemResult = void 0;\n        var nodeType = curElem.nodeType;\n\n        // 文本节点\n        if (nodeType === 3) {\n            elemResult = curElem.textContent;\n            elemResult = replaceHtmlSymbol(elemResult);\n        }\n\n        // 普通 DOM 节点\n        if (nodeType === 1) {\n            elemResult = {};\n\n            // tag\n            elemResult.tag = curElem.nodeName.toLowerCase();\n            // attr\n            var attrData = [];\n            var attrList = curElem.attributes || {};\n            var attrListLength = attrList.length || 0;\n            for (var i = 0; i < attrListLength; i++) {\n                var attr = attrList[i];\n                attrData.push({\n                    name: attr.name,\n                    value: attr.value\n                });\n            }\n            elemResult.attrs = attrData;\n            // children（递归）\n            elemResult.children = getChildrenJSON($(curElem));\n        }\n\n        result.push(elemResult);\n    });\n    return result;\n}\n\n// 构造函数\nfunction Text(editor) {\n    this.editor = editor;\n}\n\n// 修改原型\nText.prototype = {\n    constructor: Text,\n\n    // 初始化\n    init: function init() {\n        // 绑定事件\n        this._bindEvent();\n    },\n\n    // 清空内容\n    clear: function clear() {\n        this.html('<p><br></p>');\n    },\n\n    // 获取 设置 html\n    html: function html(val) {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        var html = void 0;\n        if (val == null) {\n            html = $textElem.html();\n            // 未选中任何内容的时候点击“加粗”或者“斜体”等按钮，就得需要一个空的占位符 &#8203 ，这里替换掉\n            html = html.replace(/\\u200b/gm, '');\n            return html;\n        } else {\n            $textElem.html(val);\n\n            // 初始化选取，将光标定位到内容尾部\n            editor.initSelection();\n        }\n    },\n\n    // 获取 JSON\n    getJSON: function getJSON() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        return getChildrenJSON($textElem);\n    },\n\n    // 获取 设置 text\n    text: function text(val) {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        var text = void 0;\n        if (val == null) {\n            text = $textElem.text();\n            // 未选中任何内容的时候点击“加粗”或者“斜体”等按钮，就得需要一个空的占位符 &#8203 ，这里替换掉\n            text = text.replace(/\\u200b/gm, '');\n            return text;\n        } else {\n            $textElem.text('<p>' + val + '</p>');\n\n            // 初始化选取，将光标定位到内容尾部\n            editor.initSelection();\n        }\n    },\n\n    // 追加内容\n    append: function append(html) {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        $textElem.append($(html));\n\n        // 初始化选取，将光标定位到内容尾部\n        editor.initSelection();\n    },\n\n    // 绑定事件\n    _bindEvent: function _bindEvent() {\n        // 实时保存选取\n        this._saveRangeRealTime();\n\n        // 按回车建时的特殊处理\n        this._enterKeyHandle();\n\n        // 清空时保留 <p><br></p>\n        this._clearHandle();\n\n        // 粘贴事件（粘贴文字，粘贴图片）\n        this._pasteHandle();\n\n        // tab 特殊处理\n        this._tabHandle();\n\n        // img 点击\n        this._imgHandle();\n\n        // 拖拽事件\n        this._dragHandle();\n    },\n\n    // 实时保存选取\n    _saveRangeRealTime: function _saveRangeRealTime() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        // 保存当前的选区\n        function saveRange(e) {\n            // 随时保存选区\n            editor.selection.saveRange();\n            // 更新按钮 ative 状态\n            editor.menus.changeActive();\n        }\n        // 按键后保存\n        $textElem.on('keyup', saveRange);\n        $textElem.on('mousedown', function (e) {\n            // mousedown 状态下，鼠标滑动到编辑区域外面，也需要保存选区\n            $textElem.on('mouseleave', saveRange);\n        });\n        $textElem.on('mouseup', function (e) {\n            saveRange();\n            // 在编辑器区域之内完成点击，取消鼠标滑动到编辑区外面的事件\n            $textElem.off('mouseleave', saveRange);\n        });\n    },\n\n    // 按回车键时的特殊处理\n    _enterKeyHandle: function _enterKeyHandle() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        function insertEmptyP($selectionElem) {\n            var $p = $('<p><br></p>');\n            $p.insertBefore($selectionElem);\n            editor.selection.createRangeByElem($p, true);\n            editor.selection.restoreSelection();\n            $selectionElem.remove();\n        }\n\n        // 将回车之后生成的非 <p> 的顶级标签，改为 <p>\n        function pHandle(e) {\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            var $parentElem = $selectionElem.parent();\n\n            if ($parentElem.html() === '<code><br></code>') {\n                // 回车之前光标所在一个 <p><code>.....</code></p> ，忽然回车生成一个空的 <p><code><br></code></p>\n                // 而且继续回车跳不出去，因此只能特殊处理\n                insertEmptyP($selectionElem);\n                return;\n            }\n\n            if (!$parentElem.equal($textElem)) {\n                // 不是顶级标签\n                return;\n            }\n\n            var nodeName = $selectionElem.getNodeName();\n            if (nodeName === 'P') {\n                // 当前的标签是 P ，不用做处理\n                return;\n            }\n\n            if ($selectionElem.text()) {\n                // 有内容，不做处理\n                return;\n            }\n\n            // 插入 <p> ，并将选取定位到 <p>，删除当前标签\n            insertEmptyP($selectionElem);\n        }\n\n        $textElem.on('keyup', function (e) {\n            if (e.keyCode !== 13) {\n                // 不是回车键\n                return;\n            }\n            // 将回车之后生成的非 <p> 的顶级标签，改为 <p>\n            pHandle(e);\n        });\n\n        // <pre><code></code></pre> 回车时 特殊处理\n        function codeHandle(e) {\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            if (!$selectionElem) {\n                return;\n            }\n            var $parentElem = $selectionElem.parent();\n            var selectionNodeName = $selectionElem.getNodeName();\n            var parentNodeName = $parentElem.getNodeName();\n\n            if (selectionNodeName !== 'CODE' || parentNodeName !== 'PRE') {\n                // 不符合要求 忽略\n                return;\n            }\n\n            if (!editor.cmd.queryCommandSupported('insertHTML')) {\n                // 必须原生支持 insertHTML 命令\n                return;\n            }\n\n            // 处理：光标定位到代码末尾，联系点击两次回车，即跳出代码块\n            if (editor._willBreakCode === true) {\n                // 此时可以跳出代码块\n                // 插入 <p> ，并将选取定位到 <p>\n                var $p = $('<p><br></p>');\n                $p.insertAfter($parentElem);\n                editor.selection.createRangeByElem($p, true);\n                editor.selection.restoreSelection();\n\n                // 修改状态\n                editor._willBreakCode = false;\n\n                e.preventDefault();\n                return;\n            }\n\n            var _startOffset = editor.selection.getRange().startOffset;\n\n            // 处理：回车时，不能插入 <br> 而是插入 \\n ，因为是在 pre 标签里面\n            editor.cmd.do('insertHTML', '\\n');\n            editor.selection.saveRange();\n            if (editor.selection.getRange().startOffset === _startOffset) {\n                // 没起作用，再来一遍\n                editor.cmd.do('insertHTML', '\\n');\n            }\n\n            var codeLength = $selectionElem.html().length;\n            if (editor.selection.getRange().startOffset + 1 === codeLength) {\n                // 说明光标在代码最后的位置，执行了回车操作\n                // 记录下来，以便下次回车时候跳出 code\n                editor._willBreakCode = true;\n            }\n\n            // 阻止默认行为\n            e.preventDefault();\n        }\n\n        $textElem.on('keydown', function (e) {\n            if (e.keyCode !== 13) {\n                // 不是回车键\n                // 取消即将跳转代码块的记录\n                editor._willBreakCode = false;\n                return;\n            }\n            // <pre><code></code></pre> 回车时 特殊处理\n            codeHandle(e);\n        });\n    },\n\n    // 清空时保留 <p><br></p>\n    _clearHandle: function _clearHandle() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        $textElem.on('keydown', function (e) {\n            if (e.keyCode !== 8) {\n                return;\n            }\n            var txtHtml = $textElem.html().toLowerCase().trim();\n            if (txtHtml === '<p><br></p>') {\n                // 最后剩下一个空行，就不再删除了\n                e.preventDefault();\n                return;\n            }\n        });\n\n        $textElem.on('keyup', function (e) {\n            if (e.keyCode !== 8) {\n                return;\n            }\n            var $p = void 0;\n            var txtHtml = $textElem.html().toLowerCase().trim();\n\n            // firefox 时用 txtHtml === '<br>' 判断，其他用 !txtHtml 判断\n            if (!txtHtml || txtHtml === '<br>') {\n                // 内容空了\n                $p = $('<p><br/></p>');\n                $textElem.html(''); // 一定要先清空，否则在 firefox 下有问题\n                $textElem.append($p);\n                editor.selection.createRangeByElem($p, false, true);\n                editor.selection.restoreSelection();\n            }\n        });\n    },\n\n    // 粘贴事件（粘贴文字 粘贴图片）\n    _pasteHandle: function _pasteHandle() {\n        var editor = this.editor;\n        var config = editor.config;\n        var pasteFilterStyle = config.pasteFilterStyle;\n        var pasteTextHandle = config.pasteTextHandle;\n        var ignoreImg = config.pasteIgnoreImg;\n        var $textElem = editor.$textElem;\n\n        // 粘贴图片、文本的事件，每次只能执行一个\n        // 判断该次粘贴事件是否可以执行\n        var pasteTime = 0;\n        function canDo() {\n            var now = Date.now();\n            var flag = false;\n            if (now - pasteTime >= 100) {\n                // 间隔大于 100 ms ，可以执行\n                flag = true;\n            }\n            pasteTime = now;\n            return flag;\n        }\n        function resetTime() {\n            pasteTime = 0;\n        }\n\n        // 粘贴文字\n        $textElem.on('paste', function (e) {\n            if (UA.isIE()) {\n                return;\n            } else {\n                // 阻止默认行为，使用 execCommand 的粘贴命令\n                e.preventDefault();\n            }\n\n            // 粘贴图片和文本，只能同时使用一个\n            if (!canDo()) {\n                return;\n            }\n\n            // 获取粘贴的文字\n            var pasteHtml = getPasteHtml(e, pasteFilterStyle, ignoreImg);\n            var pasteText = getPasteText(e);\n            pasteText = pasteText.replace(/\\n/gm, '<br>');\n\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            if (!$selectionElem) {\n                return;\n            }\n            var nodeName = $selectionElem.getNodeName();\n\n            // code 中只能粘贴纯文本\n            if (nodeName === 'CODE' || nodeName === 'PRE') {\n                if (pasteTextHandle && isFunction(pasteTextHandle)) {\n                    // 用户自定义过滤处理粘贴内容\n                    pasteText = '' + (pasteTextHandle(pasteText) || '');\n                }\n                editor.cmd.do('insertHTML', '<p>' + pasteText + '</p>');\n                return;\n            }\n\n            // 先放开注释，有问题再追查 ————\n            // // 表格中忽略，可能会出现异常问题\n            // if (nodeName === 'TD' || nodeName === 'TH') {\n            //     return\n            // }\n\n            if (!pasteHtml) {\n                // 没有内容，可继续执行下面的图片粘贴\n                resetTime();\n                return;\n            }\n            try {\n                // firefox 中，获取的 pasteHtml 可能是没有 <ul> 包裹的 <li>\n                // 因此执行 insertHTML 会报错\n                if (pasteTextHandle && isFunction(pasteTextHandle)) {\n                    // 用户自定义过滤处理粘贴内容\n                    pasteHtml = '' + (pasteTextHandle(pasteHtml) || '');\n                }\n                editor.cmd.do('insertHTML', pasteHtml);\n            } catch (ex) {\n                // 此时使用 pasteText 来兼容一下\n                if (pasteTextHandle && isFunction(pasteTextHandle)) {\n                    // 用户自定义过滤处理粘贴内容\n                    pasteText = '' + (pasteTextHandle(pasteText) || '');\n                }\n                editor.cmd.do('insertHTML', '<p>' + pasteText + '</p>');\n            }\n        });\n\n        // 粘贴图片\n        $textElem.on('paste', function (e) {\n            if (UA.isIE()) {\n                return;\n            } else {\n                e.preventDefault();\n            }\n\n            // 粘贴图片和文本，只能同时使用一个\n            if (!canDo()) {\n                return;\n            }\n\n            // 获取粘贴的图片\n            var pasteFiles = getPasteImgs(e);\n            if (!pasteFiles || !pasteFiles.length) {\n                return;\n            }\n\n            // 获取当前的元素\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            if (!$selectionElem) {\n                return;\n            }\n            var nodeName = $selectionElem.getNodeName();\n\n            // code 中粘贴忽略\n            if (nodeName === 'CODE' || nodeName === 'PRE') {\n                return;\n            }\n\n            // 上传图片\n            var uploadImg = editor.uploadImg;\n            uploadImg.uploadImg(pasteFiles);\n        });\n    },\n\n    // tab 特殊处理\n    _tabHandle: function _tabHandle() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        $textElem.on('keydown', function (e) {\n            if (e.keyCode !== 9) {\n                return;\n            }\n            if (!editor.cmd.queryCommandSupported('insertHTML')) {\n                // 必须原生支持 insertHTML 命令\n                return;\n            }\n            var $selectionElem = editor.selection.getSelectionContainerElem();\n            if (!$selectionElem) {\n                return;\n            }\n            var $parentElem = $selectionElem.parent();\n            var selectionNodeName = $selectionElem.getNodeName();\n            var parentNodeName = $parentElem.getNodeName();\n\n            if (selectionNodeName === 'CODE' && parentNodeName === 'PRE') {\n                // <pre><code> 里面\n                editor.cmd.do('insertHTML', '    ');\n            } else {\n                // 普通文字\n                editor.cmd.do('insertHTML', '&nbsp;&nbsp;&nbsp;&nbsp;');\n            }\n\n            e.preventDefault();\n        });\n    },\n\n    // img 点击\n    _imgHandle: function _imgHandle() {\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n\n        // 为图片增加 selected 样式\n        $textElem.on('click', 'img', function (e) {\n            var img = this;\n            var $img = $(img);\n\n            if ($img.attr('data-w-e') === '1') {\n                // 是表情图片，忽略\n                return;\n            }\n\n            // 记录当前点击过的图片\n            editor._selectedImg = $img;\n\n            // 修改选区并 restore ，防止用户此时点击退格键，会删除其他内容\n            editor.selection.createRangeByElem($img);\n            editor.selection.restoreSelection();\n        });\n\n        // 去掉图片的 selected 样式\n        $textElem.on('click  keyup', function (e) {\n            if (e.target.matches('img')) {\n                // 点击的是图片，忽略\n                return;\n            }\n            // 删除记录\n            editor._selectedImg = null;\n        });\n    },\n\n    // 拖拽事件\n    _dragHandle: function _dragHandle() {\n        var editor = this.editor;\n\n        // 禁用 document 拖拽事件\n        var $document = $(document);\n        $document.on('dragleave drop dragenter dragover', function (e) {\n            e.preventDefault();\n        });\n\n        // 添加编辑区域拖拽事件\n        var $textElem = editor.$textElem;\n        $textElem.on('drop', function (e) {\n            e.preventDefault();\n            var files = e.dataTransfer && e.dataTransfer.files;\n            if (!files || !files.length) {\n                return;\n            }\n\n            // 上传图片\n            var uploadImg = editor.uploadImg;\n            uploadImg.uploadImg(files);\n        });\n    }\n};\n\n/*\n    命令，封装 document.execCommand\n*/\n\n// 构造函数\nfunction Command(editor) {\n    this.editor = editor;\n}\n\n// 修改原型\nCommand.prototype = {\n    constructor: Command,\n\n    // 执行命令\n    do: function _do(name, value) {\n        var editor = this.editor;\n\n        // 使用 styleWithCSS\n        if (!editor._useStyleWithCSS) {\n            document.execCommand('styleWithCSS', null, true);\n            editor._useStyleWithCSS = true;\n        }\n\n        // 如果无选区，忽略\n        if (!editor.selection.getRange()) {\n            return;\n        }\n\n        // 恢复选取\n        editor.selection.restoreSelection();\n\n        // 执行\n        var _name = '_' + name;\n        if (this[_name]) {\n            // 有自定义事件\n            this[_name](value);\n        } else {\n            // 默认 command\n            this._execCommand(name, value);\n        }\n\n        // 修改菜单状态\n        editor.menus.changeActive();\n\n        // 最后，恢复选取保证光标在原来的位置闪烁\n        editor.selection.saveRange();\n        editor.selection.restoreSelection();\n\n        // 触发 onchange\n        editor.change && editor.change();\n    },\n\n    // 自定义 insertHTML 事件\n    _insertHTML: function _insertHTML(html) {\n        var editor = this.editor;\n        var range = editor.selection.getRange();\n\n        if (this.queryCommandSupported('insertHTML')) {\n            // W3C\n            this._execCommand('insertHTML', html);\n        } else if (range.insertNode) {\n            // IE\n            range.deleteContents();\n            range.insertNode($(html)[0]);\n        } else if (range.pasteHTML) {\n            // IE <= 10\n            range.pasteHTML(html);\n        }\n    },\n\n    // 插入 elem\n    _insertElem: function _insertElem($elem) {\n        var editor = this.editor;\n        var range = editor.selection.getRange();\n\n        if (range.insertNode) {\n            range.deleteContents();\n            range.insertNode($elem[0]);\n        }\n    },\n\n    // 封装 execCommand\n    _execCommand: function _execCommand(name, value) {\n        document.execCommand(name, false, value);\n    },\n\n    // 封装 document.queryCommandValue\n    queryCommandValue: function queryCommandValue(name) {\n        return document.queryCommandValue(name);\n    },\n\n    // 封装 document.queryCommandState\n    queryCommandState: function queryCommandState(name) {\n        return document.queryCommandState(name);\n    },\n\n    // 封装 document.queryCommandSupported\n    queryCommandSupported: function queryCommandSupported(name) {\n        return document.queryCommandSupported(name);\n    }\n};\n\n/*\n    selection range API\n*/\n\n// 构造函数\nfunction API(editor) {\n    this.editor = editor;\n    this._currentRange = null;\n}\n\n// 修改原型\nAPI.prototype = {\n    constructor: API,\n\n    // 获取 range 对象\n    getRange: function getRange() {\n        return this._currentRange;\n    },\n\n    // 保存选区\n    saveRange: function saveRange(_range) {\n        if (_range) {\n            // 保存已有选区\n            this._currentRange = _range;\n            return;\n        }\n\n        // 获取当前的选区\n        var selection = window.getSelection();\n        if (selection.rangeCount === 0) {\n            return;\n        }\n        var range = selection.getRangeAt(0);\n\n        // 判断选区内容是否在编辑内容之内\n        var $containerElem = this.getSelectionContainerElem(range);\n        if (!$containerElem) {\n            return;\n        }\n\n        // 判断选区内容是否在不可编辑区域之内\n        if ($containerElem.attr('contenteditable') === 'false' || $containerElem.parentUntil('[contenteditable=false]')) {\n            return;\n        }\n\n        var editor = this.editor;\n        var $textElem = editor.$textElem;\n        if ($textElem.isContain($containerElem)) {\n            // 是编辑内容之内的\n            this._currentRange = range;\n        }\n    },\n\n    // 折叠选区\n    collapseRange: function collapseRange(toStart) {\n        if (toStart == null) {\n            // 默认为 false\n            toStart = false;\n        }\n        var range = this._currentRange;\n        if (range) {\n            range.collapse(toStart);\n        }\n    },\n\n    // 选中区域的文字\n    getSelectionText: function getSelectionText() {\n        var range = this._currentRange;\n        if (range) {\n            return this._currentRange.toString();\n        } else {\n            return '';\n        }\n    },\n\n    // 选区的 $Elem\n    getSelectionContainerElem: function getSelectionContainerElem(range) {\n        range = range || this._currentRange;\n        var elem = void 0;\n        if (range) {\n            elem = range.commonAncestorContainer;\n            return $(elem.nodeType === 1 ? elem : elem.parentNode);\n        }\n    },\n    getSelectionStartElem: function getSelectionStartElem(range) {\n        range = range || this._currentRange;\n        var elem = void 0;\n        if (range) {\n            elem = range.startContainer;\n            return $(elem.nodeType === 1 ? elem : elem.parentNode);\n        }\n    },\n    getSelectionEndElem: function getSelectionEndElem(range) {\n        range = range || this._currentRange;\n        var elem = void 0;\n        if (range) {\n            elem = range.endContainer;\n            return $(elem.nodeType === 1 ? elem : elem.parentNode);\n        }\n    },\n\n    // 选区是否为空\n    isSelectionEmpty: function isSelectionEmpty() {\n        var range = this._currentRange;\n        if (range && range.startContainer) {\n            if (range.startContainer === range.endContainer) {\n                if (range.startOffset === range.endOffset) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    },\n\n    // 恢复选区\n    restoreSelection: function restoreSelection() {\n        var selection = window.getSelection();\n        selection.removeAllRanges();\n        selection.addRange(this._currentRange);\n    },\n\n    // 创建一个空白（即 &#8203 字符）选区\n    createEmptyRange: function createEmptyRange() {\n        var editor = this.editor;\n        var range = this.getRange();\n        var $elem = void 0;\n\n        if (!range) {\n            // 当前无 range\n            return;\n        }\n        if (!this.isSelectionEmpty()) {\n            // 当前选区必须没有内容才可以\n            return;\n        }\n\n        try {\n            // 目前只支持 webkit 内核\n            if (UA.isWebkit()) {\n                // 插入 &#8203\n                editor.cmd.do('insertHTML', '&#8203;');\n                // 修改 offset 位置\n                range.setEnd(range.endContainer, range.endOffset + 1);\n                // 存储\n                this.saveRange(range);\n            } else {\n                $elem = $('<strong>&#8203;</strong>');\n                editor.cmd.do('insertElem', $elem);\n                this.createRangeByElem($elem, true);\n            }\n        } catch (ex) {\n            // 部分情况下会报错，兼容一下\n        }\n    },\n\n    // 根据 $Elem 设置选区\n    createRangeByElem: function createRangeByElem($elem, toStart, isContent) {\n        // $elem - 经过封装的 elem\n        // toStart - true 开始位置，false 结束位置\n        // isContent - 是否选中Elem的内容\n        if (!$elem.length) {\n            return;\n        }\n\n        var elem = $elem[0];\n        var range = document.createRange();\n\n        if (isContent) {\n            range.selectNodeContents(elem);\n        } else {\n            range.selectNode(elem);\n        }\n\n        if (typeof toStart === 'boolean') {\n            range.collapse(toStart);\n        }\n\n        // 存储 range\n        this.saveRange(range);\n    }\n};\n\n/*\n    上传进度条\n*/\n\nfunction Progress(editor) {\n    this.editor = editor;\n    this._time = 0;\n    this._isShow = false;\n    this._isRender = false;\n    this._timeoutId = 0;\n    this.$textContainer = editor.$textContainerElem;\n    this.$bar = $('<div class=\"w-e-progress\"></div>');\n}\n\nProgress.prototype = {\n    constructor: Progress,\n\n    show: function show(progress) {\n        var _this = this;\n\n        // 状态处理\n        if (this._isShow) {\n            return;\n        }\n        this._isShow = true;\n\n        // 渲染\n        var $bar = this.$bar;\n        if (!this._isRender) {\n            var $textContainer = this.$textContainer;\n            $textContainer.append($bar);\n        } else {\n            this._isRender = true;\n        }\n\n        // 改变进度（节流，100ms 渲染一次）\n        if (Date.now() - this._time > 100) {\n            if (progress <= 1) {\n                $bar.css('width', progress * 100 + '%');\n                this._time = Date.now();\n            }\n        }\n\n        // 隐藏\n        var timeoutId = this._timeoutId;\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(function () {\n            _this._hide();\n        }, 500);\n    },\n\n    _hide: function _hide() {\n        var $bar = this.$bar;\n        $bar.remove();\n\n        // 修改状态\n        this._time = 0;\n        this._isShow = false;\n        this._isRender = false;\n    }\n};\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n};\n\n/*\n    上传图片\n*/\n\n// 构造函数\nfunction UploadImg(editor) {\n    this.editor = editor;\n}\n\n// 原型\nUploadImg.prototype = {\n    constructor: UploadImg,\n\n    // 根据 debug 弹出不同的信息\n    _alert: function _alert(alertInfo, debugInfo) {\n        var editor = this.editor;\n        var debug = editor.config.debug;\n        var customAlert = editor.config.customAlert;\n\n        if (debug) {\n            throw new Error('wangEditor: ' + (debugInfo || alertInfo));\n        } else {\n            if (customAlert && typeof customAlert === 'function') {\n                customAlert(alertInfo);\n            } else {\n                alert(alertInfo);\n            }\n        }\n    },\n\n    // 根据链接插入图片\n    insertLinkImg: function insertLinkImg(link) {\n        var _this2 = this;\n\n        if (!link) {\n            return;\n        }\n        var editor = this.editor;\n        var config = editor.config;\n\n        // 校验格式\n        var linkImgCheck = config.linkImgCheck;\n        var checkResult = void 0;\n        if (linkImgCheck && typeof linkImgCheck === 'function') {\n            checkResult = linkImgCheck(link);\n            if (typeof checkResult === 'string') {\n                // 校验失败，提示信息\n                alert(checkResult);\n                return;\n            }\n        }\n\n        editor.cmd.do('insertHTML', '<img src=\"' + link + '\" style=\"max-width:100%;\"/>');\n\n        // 验证图片 url 是否有效，无效的话给出提示\n        var img = document.createElement('img');\n        img.onload = function () {\n            var callback = config.linkImgCallback;\n            if (callback && typeof callback === 'function') {\n                callback(link);\n            }\n\n            img = null;\n        };\n        img.onerror = function () {\n            img = null;\n            // 无法成功下载图片\n            _this2._alert('插入图片错误', 'wangEditor: \\u63D2\\u5165\\u56FE\\u7247\\u51FA\\u9519\\uFF0C\\u56FE\\u7247\\u94FE\\u63A5\\u662F \"' + link + '\"\\uFF0C\\u4E0B\\u8F7D\\u8BE5\\u94FE\\u63A5\\u5931\\u8D25');\n            return;\n        };\n        img.onabort = function () {\n            img = null;\n        };\n        img.src = link;\n    },\n\n    // 上传图片\n    uploadImg: function uploadImg(files) {\n        var _this3 = this;\n\n        if (!files || !files.length) {\n            return;\n        }\n\n        // ------------------------------ 获取配置信息 ------------------------------\n        var editor = this.editor;\n        var config = editor.config;\n        var uploadImgServer = config.uploadImgServer;\n        var uploadImgShowBase64 = config.uploadImgShowBase64;\n\n        var maxSize = config.uploadImgMaxSize;\n        var maxSizeM = maxSize / 1024 / 1024;\n        var maxLength = config.uploadImgMaxLength || 10000;\n        var uploadFileName = config.uploadFileName || '';\n        var uploadImgParams = config.uploadImgParams || {};\n        var uploadImgParamsWithUrl = config.uploadImgParamsWithUrl;\n        var uploadImgHeaders = config.uploadImgHeaders || {};\n        var hooks = config.uploadImgHooks || {};\n        var timeout = config.uploadImgTimeout || 3000;\n        var withCredentials = config.withCredentials;\n        if (withCredentials == null) {\n            withCredentials = false;\n        }\n        var customUploadImg = config.customUploadImg;\n\n        if (!customUploadImg) {\n            // 没有 customUploadImg 的情况下，需要如下两个配置才能继续进行图片上传\n            if (!uploadImgServer && !uploadImgShowBase64) {\n                return;\n            }\n        }\n\n        // ------------------------------ 验证文件信息 ------------------------------\n        var resultFiles = [];\n        var errInfo = [];\n        arrForEach(files, function (file) {\n            var name = file.name;\n            var size = file.size;\n\n            // chrome 低版本 name === undefined\n            if (!name || !size) {\n                return;\n            }\n\n            if (/\\.(jpg|jpeg|png|bmp|gif|webp)$/i.test(name) === false) {\n                // 后缀名不合法，不是图片\n                errInfo.push('\\u3010' + name + '\\u3011\\u4E0D\\u662F\\u56FE\\u7247');\n                return;\n            }\n            if (maxSize < size) {\n                // 上传图片过大\n                errInfo.push('\\u3010' + name + '\\u3011\\u5927\\u4E8E ' + maxSizeM + 'M');\n                return;\n            }\n\n            // 验证通过的加入结果列表\n            resultFiles.push(file);\n        });\n        // 抛出验证信息\n        if (errInfo.length) {\n            this._alert('图片验证未通过: \\n' + errInfo.join('\\n'));\n            return;\n        }\n        if (resultFiles.length > maxLength) {\n            this._alert('一次最多上传' + maxLength + '张图片');\n            return;\n        }\n\n        // ------------------------------ 自定义上传 ------------------------------\n        if (customUploadImg && typeof customUploadImg === 'function') {\n            customUploadImg(resultFiles, this.insertLinkImg.bind(this));\n\n            // 阻止以下代码执行\n            return;\n        }\n\n        // 添加图片数据\n        var formdata = new FormData();\n        arrForEach(resultFiles, function (file) {\n            var name = uploadFileName || file.name;\n            formdata.append(name, file);\n        });\n\n        // ------------------------------ 上传图片 ------------------------------\n        if (uploadImgServer && typeof uploadImgServer === 'string') {\n            // 添加参数\n            var uploadImgServerArr = uploadImgServer.split('#');\n            uploadImgServer = uploadImgServerArr[0];\n            var uploadImgServerHash = uploadImgServerArr[1] || '';\n            objForEach(uploadImgParams, function (key, val) {\n                // 因使用者反应，自定义参数不能默认 encode ，由 v3.1.1 版本开始注释掉\n                // val = encodeURIComponent(val)\n\n                // 第一，将参数拼接到 url 中\n                if (uploadImgParamsWithUrl) {\n                    if (uploadImgServer.indexOf('?') > 0) {\n                        uploadImgServer += '&';\n                    } else {\n                        uploadImgServer += '?';\n                    }\n                    uploadImgServer = uploadImgServer + key + '=' + val;\n                }\n\n                // 第二，将参数添加到 formdata 中\n                formdata.append(key, val);\n            });\n            if (uploadImgServerHash) {\n                uploadImgServer += '#' + uploadImgServerHash;\n            }\n\n            // 定义 xhr\n            var xhr = new XMLHttpRequest();\n            xhr.open('POST', uploadImgServer);\n\n            // 设置超时\n            xhr.timeout = timeout;\n            xhr.ontimeout = function () {\n                // hook - timeout\n                if (hooks.timeout && typeof hooks.timeout === 'function') {\n                    hooks.timeout(xhr, editor);\n                }\n\n                _this3._alert('上传图片超时');\n            };\n\n            // 监控 progress\n            if (xhr.upload) {\n                xhr.upload.onprogress = function (e) {\n                    var percent = void 0;\n                    // 进度条\n                    var progressBar = new Progress(editor);\n                    if (e.lengthComputable) {\n                        percent = e.loaded / e.total;\n                        progressBar.show(percent);\n                    }\n                };\n            }\n\n            // 返回数据\n            xhr.onreadystatechange = function () {\n                var result = void 0;\n                if (xhr.readyState === 4) {\n                    if (xhr.status < 200 || xhr.status >= 300) {\n                        // hook - error\n                        if (hooks.error && typeof hooks.error === 'function') {\n                            hooks.error(xhr, editor);\n                        }\n\n                        // xhr 返回状态错误\n                        _this3._alert('上传图片发生错误', '\\u4E0A\\u4F20\\u56FE\\u7247\\u53D1\\u751F\\u9519\\u8BEF\\uFF0C\\u670D\\u52A1\\u5668\\u8FD4\\u56DE\\u72B6\\u6001\\u662F ' + xhr.status);\n                        return;\n                    }\n\n                    result = xhr.responseText;\n                    if ((typeof result === 'undefined' ? 'undefined' : _typeof(result)) !== 'object') {\n                        try {\n                            result = JSON.parse(result);\n                        } catch (ex) {\n                            // hook - fail\n                            if (hooks.fail && typeof hooks.fail === 'function') {\n                                hooks.fail(xhr, editor, result);\n                            }\n\n                            _this3._alert('上传图片失败', '上传图片返回结果错误，返回结果是: ' + result);\n                            return;\n                        }\n                    }\n                    if (!hooks.customInsert && result.errno != '0') {\n                        // hook - fail\n                        if (hooks.fail && typeof hooks.fail === 'function') {\n                            hooks.fail(xhr, editor, result);\n                        }\n\n                        // 数据错误\n                        _this3._alert('上传图片失败', '上传图片返回结果错误，返回结果 errno=' + result.errno);\n                    } else {\n                        if (hooks.customInsert && typeof hooks.customInsert === 'function') {\n                            // 使用者自定义插入方法\n                            hooks.customInsert(_this3.insertLinkImg.bind(_this3), result, editor);\n                        } else {\n                            // 将图片插入编辑器\n                            var data = result.data || [];\n                            data.forEach(function (link) {\n                                _this3.insertLinkImg(link);\n                            });\n                        }\n\n                        // hook - success\n                        if (hooks.success && typeof hooks.success === 'function') {\n                            hooks.success(xhr, editor, result);\n                        }\n                    }\n                }\n            };\n\n            // hook - before\n            if (hooks.before && typeof hooks.before === 'function') {\n                var beforeResult = hooks.before(xhr, editor, resultFiles);\n                if (beforeResult && (typeof beforeResult === 'undefined' ? 'undefined' : _typeof(beforeResult)) === 'object') {\n                    if (beforeResult.prevent) {\n                        // 如果返回的结果是 {prevent: true, msg: 'xxxx'} 则表示用户放弃上传\n                        this._alert(beforeResult.msg);\n                        return;\n                    }\n                }\n            }\n\n            // 自定义 headers\n            objForEach(uploadImgHeaders, function (key, val) {\n                xhr.setRequestHeader(key, val);\n            });\n\n            // 跨域传 cookie\n            xhr.withCredentials = withCredentials;\n\n            // 发送请求\n            xhr.send(formdata);\n\n            // 注意，要 return 。不去操作接下来的 base64 显示方式\n            return;\n        }\n\n        // ------------------------------ 显示 base64 格式 ------------------------------\n        if (uploadImgShowBase64) {\n            arrForEach(files, function (file) {\n                var _this = _this3;\n                var reader = new FileReader();\n                reader.readAsDataURL(file);\n                reader.onload = function () {\n                    _this.insertLinkImg(this.result);\n                };\n            });\n        }\n    }\n};\n\n/*\n    编辑器构造函数\n*/\n\n// id，累加\nvar editorId = 1;\n\n// 构造函数\nfunction Editor(toolbarSelector, textSelector) {\n    if (toolbarSelector == null) {\n        // 没有传入任何参数，报错\n        throw new Error('错误：初始化编辑器时候未传入任何参数，请查阅文档');\n    }\n    // id，用以区分单个页面不同的编辑器对象\n    this.id = 'wangEditor-' + editorId++;\n\n    this.toolbarSelector = toolbarSelector;\n    this.textSelector = textSelector;\n\n    // 自定义配置\n    this.customConfig = {};\n}\n\n// 修改原型\nEditor.prototype = {\n    constructor: Editor,\n\n    // 初始化配置\n    _initConfig: function _initConfig() {\n        // _config 是默认配置，this.customConfig 是用户自定义配置，将它们 merge 之后再赋值\n        var target = {};\n        this.config = Object.assign(target, config, this.customConfig);\n\n        // 将语言配置，生成正则表达式\n        var langConfig = this.config.lang || {};\n        var langArgs = [];\n        objForEach(langConfig, function (key, val) {\n            // key 即需要生成正则表达式的规则，如“插入链接”\n            // val 即需要被替换成的语言，如“insert link”\n            langArgs.push({\n                reg: new RegExp(key, 'img'),\n                val: val\n\n            });\n        });\n        this.config.langArgs = langArgs;\n    },\n\n    // 初始化 DOM\n    _initDom: function _initDom() {\n        var _this = this;\n\n        var toolbarSelector = this.toolbarSelector;\n        var $toolbarSelector = $(toolbarSelector);\n        var textSelector = this.textSelector;\n\n        var config$$1 = this.config;\n        var zIndex = config$$1.zIndex;\n\n        // 定义变量\n        var $toolbarElem = void 0,\n            $textContainerElem = void 0,\n            $textElem = void 0,\n            $children = void 0;\n\n        if (textSelector == null) {\n            // 只传入一个参数，即是容器的选择器或元素，toolbar 和 text 的元素自行创建\n            $toolbarElem = $('<div></div>');\n            $textContainerElem = $('<div></div>');\n\n            // 将编辑器区域原有的内容，暂存起来\n            $children = $toolbarSelector.children();\n\n            // 添加到 DOM 结构中\n            $toolbarSelector.append($toolbarElem).append($textContainerElem);\n\n            // 自行创建的，需要配置默认的样式\n            $toolbarElem.css('background-color', '#f1f1f1').css('border', '1px solid #ccc');\n            $textContainerElem.css('border', '1px solid #ccc').css('border-top', 'none').css('height', '300px');\n        } else {\n            // toolbar 和 text 的选择器都有值，记录属性\n            $toolbarElem = $toolbarSelector;\n            $textContainerElem = $(textSelector);\n            // 将编辑器区域原有的内容，暂存起来\n            $children = $textContainerElem.children();\n        }\n\n        // 编辑区域\n        $textElem = $('<div></div>');\n        $textElem.attr('contenteditable', 'true').css('width', '100%').css('height', '100%');\n\n        // 初始化编辑区域内容\n        if ($children && $children.length) {\n            $textElem.append($children);\n        } else {\n            $textElem.append($('<p><br></p>'));\n        }\n\n        // 编辑区域加入DOM\n        $textContainerElem.append($textElem);\n\n        // 设置通用的 class\n        $toolbarElem.addClass('w-e-toolbar');\n        $textContainerElem.addClass('w-e-text-container');\n        $textContainerElem.css('z-index', zIndex);\n        $textElem.addClass('w-e-text');\n\n        // 添加 ID\n        var toolbarElemId = getRandom('toolbar-elem');\n        $toolbarElem.attr('id', toolbarElemId);\n        var textElemId = getRandom('text-elem');\n        $textElem.attr('id', textElemId);\n\n        // 记录属性\n        this.$toolbarElem = $toolbarElem;\n        this.$textContainerElem = $textContainerElem;\n        this.$textElem = $textElem;\n        this.toolbarElemId = toolbarElemId;\n        this.textElemId = textElemId;\n\n        // 记录输入法的开始和结束\n        var compositionEnd = true;\n        $textContainerElem.on('compositionstart', function () {\n            // 输入法开始输入\n            compositionEnd = false;\n        });\n        $textContainerElem.on('compositionend', function () {\n            // 输入法结束输入\n            compositionEnd = true;\n        });\n\n        // 绑定 onchange\n        $textContainerElem.on('click keyup', function () {\n            // 输入法结束才出发 onchange\n            compositionEnd && _this.change && _this.change();\n        });\n        $toolbarElem.on('click', function () {\n            this.change && this.change();\n        });\n\n        //绑定 onfocus 与 onblur 事件\n        if (config$$1.onfocus || config$$1.onblur) {\n            // 当前编辑器是否是焦点状态\n            this.isFocus = false;\n\n            $(document).on('click', function (e) {\n                //判断当前点击元素是否在编辑器内\n                var isChild = $textElem.isContain($(e.target));\n\n                //判断当前点击元素是否为工具栏\n                var isToolbar = $toolbarElem.isContain($(e.target));\n                var isMenu = $toolbarElem[0] == e.target ? true : false;\n\n                if (!isChild) {\n                    //若为选择工具栏中的功能，则不视为成blur操作\n                    if (isToolbar && !isMenu) {\n                        return;\n                    }\n\n                    if (_this.isFocus) {\n                        _this.onblur && _this.onblur();\n                    }\n                    _this.isFocus = false;\n                } else {\n                    if (!_this.isFocus) {\n                        _this.onfocus && _this.onfocus();\n                    }\n                    _this.isFocus = true;\n                }\n            });\n        }\n    },\n\n    // 封装 command\n    _initCommand: function _initCommand() {\n        this.cmd = new Command(this);\n    },\n\n    // 封装 selection range API\n    _initSelectionAPI: function _initSelectionAPI() {\n        this.selection = new API(this);\n    },\n\n    // 添加图片上传\n    _initUploadImg: function _initUploadImg() {\n        this.uploadImg = new UploadImg(this);\n    },\n\n    // 初始化菜单\n    _initMenus: function _initMenus() {\n        this.menus = new Menus(this);\n        this.menus.init();\n    },\n\n    // 添加 text 区域\n    _initText: function _initText() {\n        this.txt = new Text(this);\n        this.txt.init();\n    },\n\n    // 初始化选区，将光标定位到内容尾部\n    initSelection: function initSelection(newLine) {\n        var $textElem = this.$textElem;\n        var $children = $textElem.children();\n        if (!$children.length) {\n            // 如果编辑器区域无内容，添加一个空行，重新设置选区\n            $textElem.append($('<p><br></p>'));\n            this.initSelection();\n            return;\n        }\n\n        var $last = $children.last();\n\n        if (newLine) {\n            // 新增一个空行\n            var html = $last.html().toLowerCase();\n            var nodeName = $last.getNodeName();\n            if (html !== '<br>' && html !== '<br\\/>' || nodeName !== 'P') {\n                // 最后一个元素不是 <p><br></p>，添加一个空行，重新设置选区\n                $textElem.append($('<p><br></p>'));\n                this.initSelection();\n                return;\n            }\n        }\n\n        this.selection.createRangeByElem($last, false, true);\n        this.selection.restoreSelection();\n    },\n\n    // 绑定事件\n    _bindEvent: function _bindEvent() {\n        // -------- 绑定 onchange 事件 --------\n        var onChangeTimeoutId = 0;\n        var beforeChangeHtml = this.txt.html();\n        var config$$1 = this.config;\n\n        // onchange 触发延迟时间\n        var onchangeTimeout = config$$1.onchangeTimeout;\n        onchangeTimeout = parseInt(onchangeTimeout, 10);\n        if (!onchangeTimeout || onchangeTimeout <= 0) {\n            onchangeTimeout = 200;\n        }\n\n        var onchange = config$$1.onchange;\n        if (onchange && typeof onchange === 'function') {\n            // 触发 change 的有三个场景：\n            // 1. $textContainerElem.on('click keyup')\n            // 2. $toolbarElem.on('click')\n            // 3. editor.cmd.do()\n            this.change = function () {\n                // 判断是否有变化\n                var currentHtml = this.txt.html();\n\n                if (currentHtml.length === beforeChangeHtml.length) {\n                    // 需要比较每一个字符\n                    if (currentHtml === beforeChangeHtml) {\n                        return;\n                    }\n                }\n\n                // 执行，使用节流\n                if (onChangeTimeoutId) {\n                    clearTimeout(onChangeTimeoutId);\n                }\n                onChangeTimeoutId = setTimeout(function () {\n                    // 触发配置的 onchange 函数\n                    onchange(currentHtml);\n                    beforeChangeHtml = currentHtml;\n                }, onchangeTimeout);\n            };\n        }\n\n        // -------- 绑定 onblur 事件 --------\n        var onblur = config$$1.onblur;\n        if (onblur && typeof onblur === 'function') {\n            this.onblur = function () {\n                var currentHtml = this.txt.html();\n                onblur(currentHtml);\n            };\n        }\n\n        // -------- 绑定 onfocus 事件 --------\n        var onfocus = config$$1.onfocus;\n        if (onfocus && typeof onfocus === 'function') {\n            this.onfocus = function () {\n                onfocus();\n            };\n        }\n    },\n\n    // 创建编辑器\n    create: function create() {\n        // 初始化配置信息\n        this._initConfig();\n\n        // 初始化 DOM\n        this._initDom();\n\n        // 封装 command API\n        this._initCommand();\n\n        // 封装 selection range API\n        this._initSelectionAPI();\n\n        // 添加 text\n        this._initText();\n\n        // 初始化菜单\n        this._initMenus();\n\n        // 添加 图片上传\n        this._initUploadImg();\n\n        // 初始化选区，将光标定位到内容尾部\n        this.initSelection(true);\n\n        // 绑定事件\n        this._bindEvent();\n    },\n\n    // 解绑所有事件（暂时不对外开放）\n    _offAllEvent: function _offAllEvent() {\n        $.offAll();\n    }\n};\n\n// 检验是否浏览器环境\ntry {\n    document;\n} catch (ex) {\n    throw new Error('请在浏览器环境下运行');\n}\n\n// polyfill\npolyfill();\n\n// 这里的 `inlinecss` 将被替换成 css 代码的内容，详情可去 ./gulpfile.js 中搜索 `inlinecss` 关键字\nvar inlinecss = '.w-e-toolbar,.w-e-text-container,.w-e-menu-panel {  padding: 0;  margin: 0;  box-sizing: border-box;}.w-e-toolbar *,.w-e-text-container *,.w-e-menu-panel * {  padding: 0;  margin: 0;  box-sizing: border-box;}.w-e-clear-fix:after {  content: \"\";  display: table;  clear: both;}.w-e-toolbar .w-e-droplist {  position: absolute;  left: 0;  top: 0;  background-color: #fff;  border: 1px solid #f1f1f1;  border-right-color: #ccc;  border-bottom-color: #ccc;}.w-e-toolbar .w-e-droplist .w-e-dp-title {  text-align: center;  color: #999;  line-height: 2;  border-bottom: 1px solid #f1f1f1;  font-size: 13px;}.w-e-toolbar .w-e-droplist ul.w-e-list {  list-style: none;  line-height: 1;}.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item {  color: #333;  padding: 5px 0;}.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item:hover {  background-color: #f1f1f1;}.w-e-toolbar .w-e-droplist ul.w-e-block {  list-style: none;  text-align: left;  padding: 5px;}.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item {  display: inline-block;  *display: inline;  *zoom: 1;  padding: 3px 5px;}.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item:hover {  background-color: #f1f1f1;}@font-face {  font-family: \\'w-e-icon\\';  src: url(data:application/x-font-woff;charset=utf-8;base64,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) format(\\'truetype\\');  font-weight: normal;  font-style: normal;}[class^=\"w-e-icon-\"],[class*=\" w-e-icon-\"] {  /* use !important to prevent issues with browser extensions that change fonts */  font-family: \\'w-e-icon\\' !important;  speak: none;  font-style: normal;  font-weight: normal;  font-variant: normal;  text-transform: none;  line-height: 1;  /* Better Font Rendering =========== */  -webkit-font-smoothing: antialiased;  -moz-osx-font-smoothing: grayscale;}.w-e-icon-close:before {  content: \"\\\\f00d\";}.w-e-icon-upload2:before {  content: \"\\\\e9c6\";}.w-e-icon-trash-o:before {  content: \"\\\\f014\";}.w-e-icon-header:before {  content: \"\\\\f1dc\";}.w-e-icon-pencil2:before {  content: \"\\\\e906\";}.w-e-icon-paint-brush:before {  content: \"\\\\f1fc\";}.w-e-icon-image:before {  content: \"\\\\e90d\";}.w-e-icon-play:before {  content: \"\\\\e912\";}.w-e-icon-location:before {  content: \"\\\\e947\";}.w-e-icon-undo:before {  content: \"\\\\e965\";}.w-e-icon-redo:before {  content: \"\\\\e966\";}.w-e-icon-quotes-left:before {  content: \"\\\\e977\";}.w-e-icon-list-numbered:before {  content: \"\\\\e9b9\";}.w-e-icon-list2:before {  content: \"\\\\e9bb\";}.w-e-icon-link:before {  content: \"\\\\e9cb\";}.w-e-icon-happy:before {  content: \"\\\\e9df\";}.w-e-icon-bold:before {  content: \"\\\\ea62\";}.w-e-icon-underline:before {  content: \"\\\\ea63\";}.w-e-icon-italic:before {  content: \"\\\\ea64\";}.w-e-icon-strikethrough:before {  content: \"\\\\ea65\";}.w-e-icon-table2:before {  content: \"\\\\ea71\";}.w-e-icon-paragraph-left:before {  content: \"\\\\ea77\";}.w-e-icon-paragraph-center:before {  content: \"\\\\ea78\";}.w-e-icon-paragraph-right:before {  content: \"\\\\ea79\";}.w-e-icon-terminal:before {  content: \"\\\\f120\";}.w-e-icon-page-break:before {  content: \"\\\\ea68\";}.w-e-icon-cancel-circle:before {  content: \"\\\\ea0d\";}.w-e-icon-font:before {  content: \"\\\\ea5c\";}.w-e-icon-text-heigh:before {  content: \"\\\\ea5f\";}.w-e-toolbar {  display: -webkit-box;  display: -ms-flexbox;  display: flex;  padding: 0 5px;  /* flex-wrap: wrap; */  /* 单个菜单 */}.w-e-toolbar .w-e-menu {  position: relative;  text-align: center;  padding: 5px 10px;  cursor: pointer;}.w-e-toolbar .w-e-menu i {  color: #999;}.w-e-toolbar .w-e-menu:hover i {  color: #333;}.w-e-toolbar .w-e-active i {  color: #1e88e5;}.w-e-toolbar .w-e-active:hover i {  color: #1e88e5;}.w-e-text-container .w-e-panel-container {  position: absolute;  top: 0;  left: 50%;  border: 1px solid #ccc;  border-top: 0;  box-shadow: 1px 1px 2px #ccc;  color: #333;  background-color: #fff;  /* 为 emotion panel 定制的样式 */  /* 上传图片的 panel 定制样式 */}.w-e-text-container .w-e-panel-container .w-e-panel-close {  position: absolute;  right: 0;  top: 0;  padding: 5px;  margin: 2px 5px 0 0;  cursor: pointer;  color: #999;}.w-e-text-container .w-e-panel-container .w-e-panel-close:hover {  color: #333;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title {  list-style: none;  display: -webkit-box;  display: -ms-flexbox;  display: flex;  font-size: 14px;  margin: 2px 10px 0 10px;  border-bottom: 1px solid #f1f1f1;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title .w-e-item {  padding: 3px 5px;  color: #999;  cursor: pointer;  margin: 0 3px;  position: relative;  top: 1px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title .w-e-active {  color: #333;  border-bottom: 1px solid #333;  cursor: default;  font-weight: 700;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content {  padding: 10px 15px 10px 15px;  font-size: 16px;  /* 输入框的样式 */  /* 按钮的样式 */}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input:focus,.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea:focus,.w-e-text-container .w-e-panel-container .w-e-panel-tab-content button:focus {  outline: none;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea {  width: 100%;  border: 1px solid #ccc;  padding: 5px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea:focus {  border-color: #1e88e5;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text] {  border: none;  border-bottom: 1px solid #ccc;  font-size: 14px;  height: 20px;  color: #333;  text-align: left;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text].small {  width: 30px;  text-align: center;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text].block {  display: block;  width: 100%;  margin: 10px 0;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text]:focus {  border-bottom: 2px solid #1e88e5;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button {  font-size: 14px;  color: #1e88e5;  border: none;  padding: 5px 10px;  background-color: #fff;  cursor: pointer;  border-radius: 3px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.left {  float: left;  margin-right: 10px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.right {  float: right;  margin-left: 10px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.gray {  color: #999;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.red {  color: #c24f4a;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button:hover {  background-color: #f1f1f1;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container:after {  content: \"\";  display: table;  clear: both;}.w-e-text-container .w-e-panel-container .w-e-emoticon-container .w-e-item {  cursor: pointer;  font-size: 18px;  padding: 0 3px;  display: inline-block;  *display: inline;  *zoom: 1;}.w-e-text-container .w-e-panel-container .w-e-up-img-container {  text-align: center;}.w-e-text-container .w-e-panel-container .w-e-up-img-container .w-e-up-btn {  display: inline-block;  *display: inline;  *zoom: 1;  color: #999;  cursor: pointer;  font-size: 60px;  line-height: 1;}.w-e-text-container .w-e-panel-container .w-e-up-img-container .w-e-up-btn:hover {  color: #333;}.w-e-text-container {  position: relative;}.w-e-text-container .w-e-progress {  position: absolute;  background-color: #1e88e5;  bottom: 0;  left: 0;  height: 1px;}.w-e-text {  padding: 0 10px;  overflow-y: scroll;}.w-e-text p,.w-e-text h1,.w-e-text h2,.w-e-text h3,.w-e-text h4,.w-e-text h5,.w-e-text table,.w-e-text pre {  margin: 10px 0;  line-height: 1.5;}.w-e-text ul,.w-e-text ol {  margin: 10px 0 10px 20px;}.w-e-text blockquote {  display: block;  border-left: 8px solid #d0e5f2;  padding: 5px 10px;  margin: 10px 0;  line-height: 1.4;  font-size: 100%;  background-color: #f1f1f1;}.w-e-text code {  display: inline-block;  *display: inline;  *zoom: 1;  background-color: #f1f1f1;  border-radius: 3px;  padding: 3px 5px;  margin: 0 3px;}.w-e-text pre code {  display: block;}.w-e-text table {  border-top: 1px solid #ccc;  border-left: 1px solid #ccc;}.w-e-text table td,.w-e-text table th {  border-bottom: 1px solid #ccc;  border-right: 1px solid #ccc;  padding: 3px 5px;}.w-e-text table th {  border-bottom: 2px solid #ccc;  text-align: center;}.w-e-text:focus {  outline: none;}.w-e-text img {  cursor: pointer;}.w-e-text img:hover {  box-shadow: 0 0 5px #333;}';\n\n// 将 css 代码添加到 <style> 中\nvar style = document.createElement('style');\nstyle.type = 'text/css';\nstyle.innerHTML = inlinecss;\ndocument.getElementsByTagName('HEAD').item(0).appendChild(style);\n\n// 返回\nvar index = window.wangEditor || Editor;\n\nreturn index;\n\n})));\n"]}