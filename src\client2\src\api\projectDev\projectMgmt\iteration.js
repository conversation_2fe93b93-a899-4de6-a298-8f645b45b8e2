import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/Iteration/'

export function getIterationList(data) {
  data = data || {};
  return request({
    url: busServiceAreaName + 'GetListByCondition',
    method: 'post',
    data
  })
}

export function getIterationPageList(data) {
  data = data || {};
  return request({
    url: busServiceAreaName + 'GetListPage',
    method: 'post',
    data
  })
}

export function addIteration(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function getDetail(params) {
  return request({
    url: busServiceAreaName + 'GetDetails',
    method: 'get',
    params
  })
}

export function edit(data) {
  return request({
    url: busServiceAreaName + 'Edit',
    method: 'post',
    data
  })
}

export function changeProcess(data) {
  return request({
    url: busServiceAreaName + 'EditProgressAsync',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: busServiceAreaName + 'Delete',
    method: 'post',
    data
  })
}


