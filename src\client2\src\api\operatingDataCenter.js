import request from "@/utils/request";
import { serviceArea } from "./serviceArea";

let serviceAreaName = serviceArea.business + "/OperatingDataCenter/";
let serviceKr = serviceArea.business + "/KingdeeRequisition/";

//工作计划
export function getWorkPlanChart(params) {
  return request({
    url: serviceAreaName + "WorkPlanChart",
    method: "get",
    params,
  });
}

//研发
export function getResearchChart(params) {
  return request({
    url: serviceAreaName + "ResearchChart",
    method: "get",
    params,
  });
}

//售后
export function getMaintenanceChart(params) {
  return request({
    url: serviceAreaName + "MaintenanceChart",
    method: "get",
    params,
  });
}

//人事
export function getPersonnelChart(params) {
  return request({
    url: serviceAreaName + "PersonnelChart",
    method: "get",
    params,
  });
}

//日常运营
export function getDailyOperationChart(params) {
  return request({
    url: serviceAreaName + "DailyOperationChart",
    method: "get",
    params,
  });
}

//生产
export function getImplementChart(params) {
  return request({
    url: serviceAreaName + "ImplementChart",
    method: "get",
    params,
  });
}

//客户（销售）
export function getCustomers(params) {
  return request({
    url: serviceAreaName + "Customers",
    method: "get",
    params,
  });
}

//客户（销售）详情
export function getCustomersDetails(data) {
  return request({
    url: serviceAreaName + "CustomersDetails",
    method: "post",
    data,
  });
}

export function getDailyOperationDetailsChart(params) {
  return request({
    url: serviceAreaName + "DailyOperationDetailsChart",
    method: "get",
    params,
  });
}

//售后详情
export function getMaintenanceDetailsChart(data) {
  return request({
    url: serviceAreaName + "MaintenanceDetailsChart",
    method: "post",
    data,
  });
}

//研发详情
export function getResearchDetailsChart(data) {
  return request({
    url: serviceAreaName + "ResearchDetailsChart",
    method: "post",
    data,
  });
}

//工作计划详情
export function getWorkPlanDetailsChart(data) {
  return request({
    url: serviceAreaName + "WorkPlanDetailsChart",
    method: "post",
    data,
  });
}

//生产详情
export function getImplementDetailsChart(data) {
  return request({
    url: serviceAreaName + "ImplementDetailsChart",
    method: "post",
    data,
  });
}

//人事详情
export function getPersonnelDetailsChart(data) {
  return request({
    url: serviceAreaName + "PersonnelDetailsChart",
    method: "post",
    data,
  });
}

//采购
export function getPurchaseChart(params) {
  return request({
    url: serviceAreaName + "PurchaseChart",
    method: "get",
    params,
  });
}

//供应链
export function getSupplyChain(params) {
  return request({
    url: serviceAreaName + "SupplyChain",
    method: "get",
    params,
  });
}

//未分享
export function getUnsharedPeople() {
  return request({
    url: serviceAreaName + "GetUnsharedPeople",
    method: "get",
  });
}

//未提交
export function getUnsubmitDaily() {
  return request({
    url: serviceAreaName + "GetUnsubmitDaily",
    method: "get",
  });
}

//销售图表
export function getVisitRecordManagementChartData(data) {
  return request({
    url: serviceAreaName + "GetVisitRecordManagementChartData",
    method: "post",
    data,
  });
}

//获取物料
export function getMaterial(params) {
  return request({
    url: serviceAreaName + "GetMaterial",
    method: "get",
    params,
  });
}

//获取单位
export function getUnit(params) {
  return request({
    url: serviceAreaName + "GetUnit",
    method: "get",
    params,
  });
}

//获取仓库
export function getStock(params) {
  return request({
    url: serviceAreaName + "GetStock",
    method: "get",
    params,
  });
}

export function getMaterialDetails(params) {
  return request({
    url: serviceAreaName + "GetMaterialDetails",
    method: "get",
    params,
  });
}

//批量获取
export function getMaterialListDetails(data) {
  return request({
    url: serviceAreaName + "GetMaterialListDetails",
    method: "post",
    data,
  });
}

//批量获取
export function getListStock(data) {
  return request({
    url: serviceAreaName + "GetListStock",
    method: "post",
    data,
  });
}

export function getMaterialInventoryByFStockId(params) {
  return request({
    url: serviceAreaName + "GetMaterialInventoryByFStockId",
    method: "get",
    params,
  });
}

export function getMaterialListPage(data) {
  return request({
    url: serviceAreaName + "GetMaterialListPage",
    method: "post",
    data,
  });
}
// 获取物料库存
export function getMaterialInventoryList(data) {
  return request({
    url: serviceAreaName + "GetMaterialInventoryList",
    method: "post",
    data,
  });
}

// 获取采购申请单
export function getPurchaseListPage(data) {
  return request({
    url: serviceKr + "GetListPage",
    method: "post",
    data,
  });
}
// 获取采购申请单详情
export function getPurchaseDetails(data) {
  return request({
    url: serviceKr + "GetDetails",
    method: "post",
    data,
  });
}
// 编辑采购申请单详情
export function editPurchase(data) {
  return request({
    url: serviceKr + "Edit",
    method: "post",
    data,
  });
}
// 采购申请单搜索申请人
export function getEmpinfoKingdee(params) {
  return request({
    url: serviceAreaName + "GetEmpinfoKingdee",
    method: "get",
    params,
  });
}

export function getPurchaseOrderKingdee(params) {
  return request({
    url: serviceAreaName + "GetPurchaseOrderKingdee",
    method: "get",
    params,
  });
}
// 搜索供应商
export function getSupplierKingdee(params) {
  return request({
    url: serviceAreaName + "GetSupplierKingdee",
    method: "get",
    params,
  });
}
