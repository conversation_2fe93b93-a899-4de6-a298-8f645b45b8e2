import request from '@/utils/request'

import { serviceArea } from '../serviceArea'

let busServiceAreaName = serviceArea.business + '/QualityProblem/'

let busServiceAreaName_pfr = serviceArea.business + '/PurchaseFollowUpRecord/'
let busServiceAreaName_qfr = serviceArea.business + '/QualityFollowUpRecord/'
let busServiceAreaName_wfr = serviceArea.business + '/WarehouseFollowUpRecord/'

export function getList(data) {
  return request({
    url: busServiceAreaName + 'GetListPage',
    method: 'post',
    data
  })
}

export function detail(params) {
  return request({
    url: busServiceAreaName + 'GetDetails',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: busServiceAreaName + 'Edit',
    method: 'post',
    data
  })
}


export function del(data) {
  return request({
    url: busServiceAreaName + 'Delete',
    method: 'post',
    data
  })
}



export function addPFRRecord(data) {
  return request({
    url: busServiceAreaName_pfr + 'Add',
    method: 'post',
    data
  })
}
export function addQFRRecord(data) {
  return request({
    url: busServiceAreaName_qfr + 'Add',
    method: 'post',
    data
  })
}
export function addWFRRecord(data) {
  return request({
    url: busServiceAreaName_wfr + 'Add',
    method: 'post',
    data
  })
}


