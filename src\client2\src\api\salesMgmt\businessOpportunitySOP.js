import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/BusinessOpportunitySOP/'

/********************************************* sop *********************************************/
//新增sop
export function add(data) {
    return request({
        url: serviceAreaName + 'EditAllList',
        method: 'post',
        data
    })
}

//删除sop
export function deleteSOP(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}


//分页查询sop
export function getStageListSOP(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
