import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/SalesAfterVist/'
export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data){
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function detail(params){
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}


export function editVistReminderSetting(data){
    return request({
        url: serviceAreaName + 'EditVistReminderSetting',
        method: 'post',
        data
    })
}


export function getVistReminderSetting(params){
    return request({
        url: serviceAreaName + 'GetVistReminderSetting',
        method: 'get',
        params
    })
}


export function loadLastTemplate(params){
    return request({
        url: serviceAreaName + 'LoadLastTemplate',
        method: 'get',
        params
    })
}

export function getAfterVistClock(data) {
    return request({
        url: serviceAreaName + 'GetAfterVistClock',
        method: 'post',
        data
    })
}

export function getAfterVistClockList(data) {
    return request({
        url: serviceAreaName + 'GetAfterVistClockList',
        method: 'post',
        data
    })
}

export function getDataDisk(data) {
    return request({
        url: serviceAreaName + 'DataDisk',
        method: 'post',
        data
    })
}

