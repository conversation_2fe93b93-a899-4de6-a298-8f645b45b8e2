<template>
    <div class="wrapper">
        <div class="opt" v-show="showBackBtn">
            <i class="el-icon-back" @click="handleClick"> 返回</i>
        </div>
        <div class="title-wrapper">
            <slot name="def">
                <ul v-show="title">
                    <li class="main-title" :class="{'bold': textBold}">{{ title }}</li>
                    <el-divider v-if="subTitle.length > 0" direction="vertical"></el-divider>
                    <template v-for="(t, idx) in subTitle">
                        <li :key="idx">{{ t }}</li>
                        <el-divider v-if="idx < subTitle.length - 1" direction="vertical" :key="idx"></el-divider>
                    </template>
                </ul>
            </slot>
        </div>
        <!-- 新增右侧内容区域 (按钮) -->
        <div class="oRight">
            <slot name="oRight"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'page-title',
    components: {
    },
    props: {
        title: {
            type: String
        },
        subTitle: {
            type: Array,
            default: () => {
                return []
            }
        },
        showBackBtn: {
            type: Boolean,
            default: false
        },
        textBold: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {}
    },
    methods: {
        handleClick() {
            this.$emit('goBack')
        },
    }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.wrapper {
    display: flex;
    align-items: center;
    height: 40px;
    background: #fff;
    padding: 0 10px;
    // border-bottom: 1px solid #DCDFE6;
    border-bottom: 1px solid $border-color-light;
    .title-wrapper {
        flex: 1;
        ul {
            padding: 0;
            margin: 0;
            // padding-left: 10px;
            li {
                list-style-type: none;
                display: inline-block;
            }
            .main-title{
                // font-size: 16px;
                // font-weight: 600;
                &.bold{
                    font-weight: 600;
                    font-size: 15px;
                }
            }
            
        } 
    }
    .opt{
        height: 20px;
        line-height: 20px;
        padding-right: 16px;
        margin-right: 16px;
        border-right: 1px solid #DCDFE6;
        font-size: 14px;
        cursor: pointer;
    }
    .opt:hover{
        i{
            color:rgb(64, 158, 255);
        }
    }

}
</style>