
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/EmployeePointsRule/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}


export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function editEmployee(data) {
    return request({
        url: serviceAreaName + 'EditEmployee',
        method: 'post',
        data
    })
}

export function getSalesPoint(data) {
    return request({
        url: serviceAreaName + 'SalesPoint',
        method: 'post',
        data
    })
}


export function getWeekSalesPoint(data) {
    return request({
        url: serviceAreaName + 'GetWeekSalesPoint',
        method: 'post',
        data
    })
}

export function saveWeekSalesPoint(data) {
    return request({
        url: serviceAreaName + 'SaveWeekSalesPoint',
        method: 'post',
        data
    })
}

export function getWeekSalesPointDetails(data) {
    return request({
        url: serviceAreaName + 'GetWeekSalesPointDetails',
        method: 'post',
        data
    })
}



export function getDayCounter(params) {
    return request({
        url: serviceAreaName + 'GetDayCounter',
        method: 'get',
        params
    })
}


export function getMonthSalesPoint(data) {
    return request({
        url: serviceAreaName + 'GetMonthSalesPoint',
        method: 'post',
        data
    })
}

export function getMonthSalesPointDetails(data) {
    return request({
        url: serviceAreaName + 'GetMonthSalesPointDetails',
        method: 'post',
        data
    })
}

export function getDetailsByEmployeeId(params) {
    return request({
        url: serviceAreaName + 'GetDetailsByEmployeeId',
        method: 'get',
        params
    })
}

