<template>
  <div>
    <treeselect
      :noResultsText="noResultsTextOfSelTree"
      :noOptionsText="noOptionsTextOfSelTree"
      :normalizer="normalizer"
      :default-expand-level="3"
      :options="orgs"
      :multiple="false"
      :show-count="true"
      @select="onSelect"
      @input="input"
    ></treeselect>
    <el-transfer
      v-model="selectUsers"
      :data="orgUsers"
      @change="handleChange"
      :titles="['系统用户', '已选用户']"
    ></el-transfer>
  </div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import * as login from "@/api/login";
import * as orgs from "@/api/organization";
import * as emps from "@/api/emp";
import { getUserInfo } from "@/utils/auth";

export default {
  name: "select-users",
  components: {
    Treeselect
  },
  props: ["users"],
  data() {
    // todo:兼容layui的样式、图标
    return {
      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        };
      },
      selectUsers: this.users,
      orgUsers: [],
      orgs: []
    };
  },
  watch: {
    users(val) {
      // 因为组件只挂载一次，后期只能通过watch来改变selectusers的值
      this.selectUsers = val;
    }
  },
  mounted() {
    var _this = this;
    orgs.getValidOrgs().then(response => {
      var orgs = response.map(function(item, index, input) {
        return {
          Id: item.OrganizationId,
          label: item.Name,
          ParentId: item.ParentId
        };
      });
      var tree = listToTreeSelect(orgs);

      _this.orgs = tree;
      _this.getOrgUsers("");
    });
  },
  methods: {
    getOrgUsers(orgId) {
      var _this = this;
      emps.getList({ orgId: orgId }).then(response => {
        _this.orgUsers = response.Items.map(function(item, index, input) {
          return { key: item.EmployeeId, label: item.Name };
        });
      });
    },
    onSelect: function(node, instanceId) {
      this.getOrgUsers(node.Id);
    },
    input: function(node, instanceId) {
      if (node === undefined) {
        // 清空选择
        this.getOrgUsers("");
      }
    },
    handleChange(value, direction, movedKeys) {
      this.$emit("users-change", this.selectUsers);
    }
  }
};
</script>

<style scoped>
.el-transfer {
  margin-top: 10px;
}
</style>
