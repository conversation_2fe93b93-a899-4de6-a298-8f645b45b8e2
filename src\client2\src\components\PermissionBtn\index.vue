<template>
	<div class="filter-items">
    <template v-for="(btn, idx) of _topBtns">
      <template v-if="btn.DomId==='customDomId'">
        <slot :name="btn.DomId" :data="btn"></slot>
      </template>
      <slot v-else :name="btn.DomId" :data="btn">
        <el-button 
          type="primary" 
          :icon="icons[btn.Icon]" 
          :size="size" 
          v-bind:key="btn.Id + idx" 
          class="filter-item" 
          @click="handleClick(btn)"
          >
            {{btn.Name}}
        </el-button>
      </slot>
    </template>
	</div>
</template>

<script>
// import { mapGetters } from 'vuex'
import waves from '@/directive/waves' // 水波纹指令
export default {
  name: 'permission-btn',
  data() {
    // todo:兼容layui的样式、图标
    return {
      types: {
        'layui-btn-danger': 'danger',
        'layui-btn-normal': 'primary'
      },
      icons: {
        '&#xe615;': 'el-icon-refresh',
        '&#xe640;': 'el-icon-remove',
        '&#xe642;': 'el-icon-edit',
        '&#xe60a;': 'el-icon-document',
        '&#xe654;': 'el-icon-circle-plus'
      },
      // btnList: []
    }
  },
  mounted() {
    // let btns = this.$slots
    // Object.keys(btns).forEach(b => {
    //   let refTemp = btns[b][0].elm
    //   refTemp.addEventListener('click', this.handleClick(refTemp.data), false)
    //   this.btnList.push(refTemp)
    // })

  },
  methods: {
    handleClick(btn) {
      this.$emit('btn-event', btn.DomId)
    }
  },
  directives: {
    waves
  },
  computed: {
    _topBtns() {
      if(this.filterBtn) {
        return this.filterBtn(this.topBtns)
      }else{
        return this.topBtns
      }
    },
    // buttons: function() {
    //   var route = this.$route
    //   var elements = route.meta.elements.filter(b => b.ButtonShowType == 1 || b.ButtonShowType == 3)
    //   return elements.sort((a, b) => {
    //     return a.Sort - b.Sort //升序
    //   })
    // }
  },
  props: {
    // moduleName: {
    //   type: String,
    //   required: true
    // },
    size: {
      type: String,
      default: ''
    },
    filterBtn: Function
  },
  // beforeDestroy() {
  //   if (this.btnList && this.btnList.length > 0) {
  //       this.btnList.forEach(b => {
  //         b.removeEventListener('click', this.handleClick, false)
  //       })
  //   }
  // },
}
</script>

<style scoped>
.btn-bottom {
    margin-bottom: 0;
}
.filter-items {
    display: inline-block;
    /* margin-left: 10px; */
}
.filter-items button {
  margin-left: 4px;
}
.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
</style>
