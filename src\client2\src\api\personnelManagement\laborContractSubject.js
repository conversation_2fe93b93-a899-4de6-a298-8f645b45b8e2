import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/LaborContractSubject/'


export function addOrUpdateOrDelete(data) {
    return request({
        url: serviceAreaName + 'AddOrUpdateOrDelete',
        method: 'post',
        data
    })
}

export function getAllLaborContractSubject(params) {
    return request({
        url: serviceAreaName + 'GetAllLaborContractSubject',
        method: 'get',
        params
    })
}
