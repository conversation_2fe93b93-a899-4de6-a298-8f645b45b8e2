
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/SystemMessage/'


export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function markAsRead(params) {
    return request({
        url: serviceAreaName + 'MarkAsRead',
        method: 'get',
        params
    })
}


export function getSubordinateModuleList(params) {
    return request({
        url: serviceAreaName + 'GetSubordinateModuleList',
        method: 'get',
        params
    })
}