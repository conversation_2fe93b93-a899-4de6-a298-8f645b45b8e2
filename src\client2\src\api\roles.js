import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.user
export function getList(params) {
  return request({
    url: serviceAreaName + '/Roles/Load',
    method: 'get',
    params
  })
}

export function loadForUser(userId) {
  return request({
    url: serviceAreaName + '/Roles/LoadForUser',
    method: 'get',
    params: { userId: userId }
  })
}

export function add(data) {
  return request({
    url: serviceAreaName + '/Roles/Add',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: serviceAreaName + '/Roles/Update',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: serviceAreaName + '/Roles/Delete',
    method: 'post',
    data
  })
}


export function getUsersByRoleId(params) {
  return request({
    url: serviceAreaName + '/Roles/GetRoleUser',
    method: 'get',
    params
  })
}

export function accessRole(data) {
  return request({
    url: serviceAreaName + '/Roles/EditRoleUser',
    method: 'post',
    data
  })
}
