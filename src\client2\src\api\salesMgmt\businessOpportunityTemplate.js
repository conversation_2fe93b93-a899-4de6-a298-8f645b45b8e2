import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/BusinessOpportunityTemplate/'

/********************************************* 模板 *********************************************/
//新增模板
export function addTemplate(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

//删除模板
export function deleteTemplate(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//编辑模板
export function editTemplate(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

//查看模板详情
export function getTemplateDetails(params) {
    return request({
        url: serviceAreaName + 'Details',
        method: 'get',
        params
    })
}

//分页查询模板
export function getTemplateListPage(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

//根据条件查询模板
export function getTemplateListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetTemplateListByCondition',
        method: 'post',
        data
    })
}


//设置模板启用状态
export function setTemplateStatus(params) {
    return request({
        url: serviceAreaName + 'SetTemplateStatus',
        method: 'get',
        params
    })
}




