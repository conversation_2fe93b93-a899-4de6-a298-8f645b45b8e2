<template>
  <div class="vditor-wrapper" :class="readonly ? 'readonly' : ''">
    <div :id="id"></div>
  </div>
</template>

<script>
import Vditor from "vditor";
import "vditor/dist/index.css";

export default {
  name: "AppVditor",
  model: {
    prop: "selected",
    event: "change",
  },
  props: {
    selected: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      required: false,
      default() {
        return "markdown-editor-" + +new Date() + ((Math.random() * 1000).toFixed(0) + "");
      },
    },
    isHideTools: {
      type: Boolean,
      required: false,
      default: false,
    },
    isPin: {
      type: Boolean,
      required: false,
      default: true,
    },
    height: {
      type: [Number, String],
      required: false,
      default: 160,
    },
    width: {
      type: String,
      required: false,
      default: "auto",
    },
    mode: {
      type: String,
      required: false,
      default: "ir", //wysiwyg ir sv
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      vditor: "",
    };
  },
  mounted() {
    //如果是“详情”，子需要展示markdown内容，不需要初始化md编辑器。
    if (this.readonly) {
      // let reviewDom = document.getElementById(`${this.id}`);
      // Vditor.preview(reviewDom, this.selected, {
      //   speech: {
      //     enable: false,
      //   },
      //   anchor: 1,
      //   after() {},
      // });
      // return false;
    }

    //非详情，需要初始化编辑器
    this.vditor = new Vditor(`${this.id}`, {
      height: this.height,
      //   minHeight: 160,//自适应，回车自动拉伸高度（给了最小高度，回车就没发自动拉伸高度）
      width: this.width,
      toolbarConfig: {
        pin: this.isPin,
        hide: this.isHideTools,
      },
      //支持拖动大小
      resize: {
        enable: true,
      },
      cache: {
        enable: false,
      },
      toolbar: [
        "emoji",
        "headings",
        "bold",
        "italic",
        "strike",
        "link",
        "|",
        "list",
        "ordered-list",
        "check",
        "outdent",
        "indent",
        "|",
        "quote",
        "line",
        "code",
        "inline-code",
        "insert-before",
        "insert-after",
        "|",
        //"upload",
        // "record",

        // {
        //   //自定义上传
        //   hotkey: "",
        //   name: "upload",
        //   // tipPosition: "s",
        //   tip: "上传图片",
        //   className: "right",
        // },

        "table",
        "|",
        "undo",
        "redo",
        "|",
        // "fullscreen",
        "edit-mode",
        {
          name: "more",
          toolbar: [
            //"both",
            "code-theme",
            "content-theme",
            "export",
            "outline",
            "preview",
            //"devtools",
            // "info",
            //"help",
          ],
        },
      ],
      after: () => {
        let val = this.selected;
        this.vditor.setValue(val);

        if (this.readonly) {
          // setTimeout(() => {
          //   let reviewDom = document.getElementById(`preview-${this.id}`);
          //   Vditor.preview(reviewDom, val, {
          //     speech: {
          //       enable: false,
          //     },
          //     anchor: 1,
          //     after() {},
          //   });
          // }, 10);
        }
      },
      input: val => {
        this.$emit("change", val);
      },
      mode: this.mode,
      preview: {
        mode: "both",
        actions: [],
      },
      upload: {
        accept: "image/*", // 规定上传的图片格式
        url: "/api/uploadFile", // 请求的接口
        multiple: false,
        fieldName: "file",
        max: 10 * 1024 * 1024, //上传图片的大小（）
        // extraData: { 'access_token': this.token }, // 为 FormData 添加额外的参数
        linkToImgUrl: "/api/admin/uploadFile",
        validate: (files) => {
          // const isLessthan10M = files[0].size / 1024 / 1024 < 10;
          // if (!isLessthan10M) {
          //   this.$message.error('上传图片大小不能超过 10MB!')
          // }

          // this.$message({
          //   message: `上传图片大小不能超过 10MB!`,
          //   type: 'error'
          // })


        },
        // 自定义上传
        handler: (files) => {
          this.$message({
            message: `暂不支持图片上传`,
            type: 'error'
          })

        },
        // 上传图片回显处理
        format(files, responseText) {
          var self = this;
          var data = JSON.parse(responseText);
          // 上传图片请求状态
          if (data.status) {
            let filName = data.msg;
            let lastTipNum = filName.substr(
              filName.lastIndexOf("/", filName.lastIndexOf("/") - 1) + 1
            );
            let index = lastTipNum.lastIndexOf("\/");
            let succ = {};
            succ[filName] = "/api" + data.data;
            //图片回显
            return JSON.stringify({
              data,
              data,
              data: {
                errFiles: [],
                succMap: succ,
              },
            });
          } else {
            Message({
              message: "图片上传失败",
              type: "error",
            });
          }
        },
        error(msg) {
          console.log(msg + "上传失败");
        },
      },
    });
    this.unwatch = this.$watch("selected", val => {
      if (this.vditor && this.getValue() !== val) {
        this.setValue(val);
      }
    });
  },
  methods: {
    getValue() {
      return this.vditor.getValue();
    },
    getHTML() {
      return this.vditor.getHTML();
    },
    setValue(value) {
      return this.vditor.setValue(value);
    },
    disabled() {
      return this.vditor.disabled();
    },
  },
  beforeDestory() {
    if (this.unwatch) {
      this.unwatch();
    }
    if (this.vditor) {
      this.vditor.destory();
    }
  },
};
</script>

<style lang="scss" scoped>
// .vditor-wrapper:not(.readonly) {
//   /deep/.vditor-reset {
//     padding: 10px !important;
//   }
// }

.vditor-wrapper {
  /deep/.vditor-toolbar {
    padding-left: 5px!important;
  }

  /deep/a {
    color: $color-primary;
    text-decoration: underline;
  }

  /deep/ul {
    li {
      list-style-type: disc;
    }
  }

  /deep/ol {
    li {
      list-style-type: decimal;
    }
  }
}
</style>
