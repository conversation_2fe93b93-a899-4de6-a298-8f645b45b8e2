import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + "/ConfigTableHead/";


export function getCurrent(params) {
  return request({
    url: serviceAreaName + "GetCurrent",
    method: 'get',
    params
  })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}
export function appliesEveryone(data) {
    return request({
        url: serviceAreaName + 'AppliesEveryone',
        method: 'post',
        data
    })
}

