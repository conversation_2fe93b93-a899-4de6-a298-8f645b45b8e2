import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/News/GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/News/Add',
        method: 'post',
        data
    })
}

export function update(data) {
    return request({
        url: serviceAreaName + '/News/Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + '/News/Delete',
        method: 'post',
        data
    })
}

export function detail(params){
    return request({
        url: serviceAreaName + '/News/GetDetails',
        method: 'get',
        params
    })
}

export function getNewsCategories() {
    return request({
        url: serviceAreaName + '/News/GetNewsClass',
        method: 'get'
    })
}

//首页获取行业新闻
export function getIndustryNews(params) {
    return request({
        url: serviceAreaName + '/News/GetIndustryNewsPage',
        method: 'get',
        params
    })
}

//首页获取公司新闻
export function getCmpNew(params) {
    return request({
        url: serviceAreaName + '/News/GetCompanyNewsPage',
        method: 'get',
        params
    })
}

//首页轮播图
export function getAdvs() {
    return request({
        url: serviceAreaName + '/News/GetHomeAdvert',
        method: 'get'
    })
}

