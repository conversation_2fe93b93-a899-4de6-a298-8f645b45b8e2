import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/ImplementationManage/'

/********************************************* 模板 *********************************************/
//新增模板
export function addImplementationTemplate(data) {
    return request({
        url: serviceAreaName + 'AddImplementationTemplate',
        method: 'post',
        data
    })
}

//删除模板
export function deleteImplementationTemplate(data) {
    return request({
        url: serviceAreaName + 'DeleteImplementationTemplate',
        method: 'post',
        data
    })
}

//编辑模板
export function editImplementationTemplate(data) {
    return request({
        url: serviceAreaName + 'EditImplementationTemplate',
        method: 'post',
        data
    })
}

//查看模板详情
export function getImplementationTemplateDetails(params) {
    return request({
        url: serviceAreaName + 'GetImplementationTemplateDetails',
        method: 'get',
        params
    })
}

//分页查询模板
export function getImplementationTemplateListPage(data) {
    return request({
        url: serviceAreaName + 'GetImplementationTemplateListPage',
        method: 'post',
        data
    })
}

//根据条件查询模板
export function getImplementationTemplateListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetImplementationTemplateListByCondition',
        method: 'post',
        data
    })
}

/********************************************* 工序 *********************************************/
//新增工序
export function addImplementationProcedureTemplate(data) {
    return request({
        url: serviceAreaName + 'AddImplementationProcedureTemplate',
        method: 'post',
        data
    })
}

//删除工序
export function deleteImplementationProcedureTemplate(data) {
    return request({
        url: serviceAreaName + 'DeleteImplementationProcedureTemplate',
        method: 'post',
        data
    })
}

//编辑工序
export function editImplementationProcedureTemplate(data) {
    return request({
        url: serviceAreaName + 'EditImplementationProcedureTemplate',
        method: 'post',
        data
    })
}

//实施工序模板集合操作（新增、编辑）
export function editImplementationProcedureTemplateList(data) {
    return request({
        url: serviceAreaName + 'EditImplementationProcedureTemplateList',
        method: 'post',
        data
    })
}

//查看工序详情
export function getImplementationProcedureTemplateDetails(params) {
    return request({
        url: serviceAreaName + 'GetImplementationProcedureTemplateDetails',
        method: 'get',
        params
    })
}

//分页查询工序
export function getImplementationProcedureTemplateListPage(data) {
    return request({
        url: serviceAreaName + 'GetImplementationProcedureTemplateListPage',
        method: 'post',
        data
    })
}

//根据条件查询工序
export function getImplementationProcedureTemplateListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetImplementationProcedureTemplateListByCondition',
        method: 'post',
        data
    })
}




/********************************************* 事项 *********************************************/
//新增事项
export function addImplementationItemTemplate(data) {
    return request({
        url: serviceAreaName + 'AddImplementationItemTemplate',
        method: 'post',
        data
    })
}

//删除事项
export function deleteImplementationItemTemplate(data) {
    return request({
        url: serviceAreaName + 'DeleteImplementationItemTemplate',
        method: 'post',
        data
    })
}

//编辑事项
export function editImplementationItemTemplate(data) {
    return request({
        url: serviceAreaName + 'EditImplementationItemTemplate',
        method: 'post',
        data
    })
}

//实施事项模板集合操作（新增、编辑）
export function editImplementationItemTemplateList(data) {
    return request({
        url: serviceAreaName + 'EditImplementationItemTemplateList',
        method: 'post',
        data
    })
}

//查看事项详情
export function getImplementationItemTemplateDetails(params) {
    return request({
        url: serviceAreaName + 'GetImplementationItemTemplateDetails',
        method: 'get',
        params
    })
}

//分页查询事项
export function getImplementationItemTemplateListPage(data) {
    return request({
        url: serviceAreaName + 'GetImplementationItemTemplateListPage',
        method: 'post',
        data
    })
}

//根据条件查询事项
export function getImplementationItemTemplateListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetImplementationItemTemplateListByCondition',
        method: 'post',
        data
    })
}


/********************************************* 自定义 *********************************************/

//设置模板启用状态
export function setImplementationTemplateStatus(params) {
    return request({
        url: serviceAreaName + 'SetImplementationTemplateStatus',
        method: 'get',
        params
    })
}