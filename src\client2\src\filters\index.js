import dayjs from 'dayjs'
import { Decimal } from "decimal.js"
import { getTextByCode } from '@/utils'


export function statusFilter(isFinish) {
    const statusMap = {
        true: 'color-success',
        false: 'color-danger',
        
        // 0: 'color-info',
        // 1: 'color-success',
        // 2: 'color-danger',
        // 3: 'color-danger',
        // 4: 'color-danger'
    }
    return statusMap[isFinish]
}

export function dateFilter(val, fat,rt) {
    if(!val){
        //在没有值传入时，自定义返回值
        if(rt){
            return rt
        }
        return '无'
    }
    return dayjs(val).format(fat)
}

/**
 * 枚举获取label或对象
 * @param {string|number} val - 枚举值
 * @param {Array} Enum - 枚举数组
 * @param {String} empty - 空值时显示
 * @returns {string}
 */
export function enumFilter(val, Enum,empty='') {
  const findItem = Enum.find(t => t.value === val);
  return findItem ? findItem.label : empty;
}

export function weekFilter(val = 0) {
    let weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return weeks[val]
}



//val 分钟数
export function minuteFilter(val) {
    let symbol = val > 0 ? '' : '-'
    val = Math.abs(val)
    if(val) {
        if(symbol) {
            return `${symbol} ${parseInt(val / 60)} 时 ${parseInt(val % 60)} 分`
        }
        return `${parseInt(val / 60)} 时 ${parseInt(val % 60)} 分`
    }
    return '-'
}

export function emptyFilter(val) {
    // debugger
    
    if(val || val === 0) {
        return val
    }
    return '无'
}

export function fixedFilter(num, decimalPlaces = 0) {
    if(num || num == 0) {
        return Decimal(num).toFixed(decimalPlaces, Decimal.ROUND_DOWN)
    }
    return num
}


export function areaCodeFilter(val) {
    if(val) {
        return getTextByCode(val)
    }
    return val
}



// export function highlight(words, query) {
//     if(!query) {
//         return words
//     }

//     let iQuery = new RegExp(query, "ig");
//     return words.toString().replace(iQuery, function(matchedTxt,a,b){
//         return ('<span class=\'highlight\'>' + matchedTxt + '</span>');
//     });
// }
