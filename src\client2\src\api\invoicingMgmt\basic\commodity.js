
import request from '@/utils/request'
import { serviceArea } from '../../serviceArea'

let serviceAreaName = serviceArea.business + '/Commodity/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

//批量设置
export function bulkEdit(data) {
    return request({
        url: serviceAreaName + 'BulkEdit',
        method: 'post',
        data
    })
}


export function getListInventory(data) {
    return request({
        url: serviceAreaName + 'GetListInventory',
        method: 'post',
        data
    })
}
