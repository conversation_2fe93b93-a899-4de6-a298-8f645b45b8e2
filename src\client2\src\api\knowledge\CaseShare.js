import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/CaseShare/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}
export function addApprovalSetting(data) {
    return request({
        url: serviceAreaName + 'AddApprovalSetting',
        method: 'post',
        data
    })
}
export function getApprovalSetting(params) {
    return request({
        url: serviceAreaName + 'GetApprovalSetting',
        method: 'get',
        params
    })
}
export function approval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}
export function BonusPoints(data) {
    return request({
        url: serviceAreaName + 'BonusPoints',
        method: 'post',
        data
    })
}

export function TransitionCourse(params) {
    return request({
        url: serviceAreaName + 'TransitionCourse',
        method: 'get',
        params
    })
}