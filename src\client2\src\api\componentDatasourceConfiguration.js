import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/ComponentDatasourceConfiguration/GetComponentDatasourceConfigurationList',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/ComponentDatasourceConfiguration/Edit',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/ComponentDatasourceConfiguration/Add',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + '/ComponentDatasourceConfiguration/Delete',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + '/ComponentDatasourceConfiguration/GetComponentDatasourceConfigurationDetail',
        method: 'get',
        params
    })
}