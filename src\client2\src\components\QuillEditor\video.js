import * as common from "@/api/common";
import SparkMD5 from "spark-md5";
import * as Quill from "quill"; //引入编辑器
let count = 0; //上传片数记录
let params = null;
let isClose = false; //控制是否取消上传
let config = {
  headers: { "Content-Type": "multipart/form-data" },
};
config.timeout = 300000;
export const closeUpload = () => {
  isClose = true;
};
export const uploadByPieces = ({ file, pieceSize = 2, progress, success, error }) => {
  console.log(0, file);
  isClose = false;
  // if (!file || !file.length) return
  // 上传过程中用到的变量
  let p = 0; //进度
  let fileMD5 = ""; // 总文件列表
  let totalSize = file.size;
  const chunkSize = pieceSize * 1024; // 5MB一片
  const chunkCount = Math.ceil(file.size / chunkSize); // 总片数
  const computeMD5 = () => {
    let flag = 0;
    let chunkSize1 = 1024 * 1024 * 0.5; //不需要根据分片设置切片的大小（主要考虑计算耗时——分片多，计算少，分片大，计算可能会卡死（IE））
    let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
    let fileReader = new FileReader();
    let spark = new SparkMD5.ArrayBuffer();
    let currentChunk1 = 0;
    let chunks1 = Math.ceil(file.size / chunkSize1);

    // file.name += ' (文件分析中...)'
    // file.pause();
    fileReader.onload = e => {
      spark.append(e.target.result); // Append array buffer
      currentChunk1++;

      if (currentChunk1 < chunks1) {
        loadNext();
      } else {
        fileMD5 = spark.end(); // Compute hash
        common
          .testFile({ identifier: fileMD5, fileName: file.name, totalChunks: chunkCount })
          .then(res => {
            if (res.SkipUpload) {
              // console.log("文件已被上传")
              merge();
            } else {
              // console.log("文件未被上传，将分片上传")
              readChunkMD5();
            }
          })
          .catch(e => {
            error && error(e);
          });
      }
    };

    fileReader.onerror = function() {
      console.log(
        "FileReader onerror was triggered, maybe the browser aborted due to high memory usage."
      );
    };

    function loadNext() {
      var start = currentChunk1 * chunkSize1,
        end = start + chunkSize1 >= file.size ? file.size : start + chunkSize1;
      fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    }
    loadNext();
  };

  const getChunkInfo = (file, currentChunk, chunkSize) => {
    let start = currentChunk * chunkSize;
    let end = Math.min(file.size, start + chunkSize);
    let chunk = file.slice(start, end);
    return { start, end, chunk };
  };
  // 针对每个文件进行chunk处理
  const readChunkMD5 = () => {
    count = 0;
    const { chunk } = getChunkInfo(file, count, chunkSize);
    params = { chunk, currentChunk: count, chunkCount };
    // 针对单个文件进行chunk上传
    uploadChunk(params);
  };
  const merge = () => {
    common
      .mergeChunks(
        { fileName: file.name, identifier: fileMD5, type: "video/mp4", totalSize: totalSize },
        config
      )
      .then(res => {
        // console.log(3,res)
        success && success(res);
      })
      .catch(err => {
        error && error(err);
      });
  };
  const uploadChunk = chunkInfo => {
    p = Math.floor((100 / chunkInfo.chunkCount) * (chunkInfo.currentChunk + 1) * 100) / 100;
    progress && progress(p);
    // progressFun()
    let fetchForm = new FormData();
    fetchForm.append("chunkNumber", chunkInfo.currentChunk);
    fetchForm.append("chunkSize", chunkSize);
    fetchForm.append("totalChunks", chunkInfo.chunkCount);
    fetchForm.append("file", chunkInfo.chunk);
    fetchForm.append("identifier", fileMD5);
    fetchForm.append("filename", file.name);
    fetchForm.append("relativePath", file.name);
    fetchForm.append("type", "video/mp4");
    fetchForm.append("currentChunkSize", chunkSize);

    fetchForm.append("totalSize", totalSize);

    common
      .postUploadChunk(fetchForm, config)
      .then(res => {
        //   console.log(2,res)
        if (res.Result) {
          if (chunkInfo.currentChunk < chunkInfo.chunkCount - 1) {
            //   console.log("分片上传成功")
            if (!isClose) {
              count++;
              const { chunk } = getChunkInfo(file, count, chunkSize);
              params = { chunk, currentChunk: count, chunkCount };
              uploadChunk(params);
            }
          } else {
            // 当总数大于等于分片个数的时候
            if (chunkInfo.currentChunk >= chunkInfo.chunkCount - 1) {
              // console.log("文件开始------合并成功")

              merge();
            }
          }
        } else {
          console.log(res.data.msg);
        }
      })
      .catch(e => {
        error && error(e);
        return;
      });
  };
  computeMD5();
};

var BlockEmbed = Quill.import("blots/block/embed");

export class Video extends BlockEmbed {
  static create(value) {
    var node = super.create(value);
    node.setAttribute("src", value.src);
    node.setAttribute("width", "100%");
    node.setAttribute("controls", "controls");
    node.setAttribute("poster", value.poster);
    node.setAttribute("muted", "");
    node.setAttribute("x-webkit-airplay", true);
    node.setAttribute("playsinline", true);
    node.setAttribute("x5-video-player-type", "h5");
    // node.setAttribute("webkit-playsinline",true);
    // node.setAttribute("x5-playsinline",true);
    // node.setAttribute("x5-video-player-fullscreen",false)
    // node.setAttribute("autoplay","autoplay");

    // node.setAttribute('src', value.src);
    // node.setAttribute('poster', value.poster);
    // node.setAttribute('width', '100%');
    // node.setAttribute("controls","controls");
    // node.setAttribute("playsinline",true);
    // node.setAttribute("webkit-playsinline",true);
    // node.setAttribute("x5-playsinline",true);
    // node.setAttribute("preload","preload")
    // node.setAttribute("x-webkit-airplay","allow");

    return node;
  }

  static value(node) {
    return {
      src: node.getAttribute("src"),
      poster: node.getAttribute("poster"),
    };
  }
}
Video.blotName = "video";
Video.tagName = "Video";
Video.className = "ql-video";
