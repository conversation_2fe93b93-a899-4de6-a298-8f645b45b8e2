<template>
  <el-cascader
    :title="addressStr"
    :value="selected"
    :options="regionData"
    v-bind="$attrs"
    v-on="$listeners"
    clearable
    filterable
    @change="handleChange"
  ></el-cascader>
</template>

<script>
// import { regionData, CodeToText, TextToCode } from "element-china-area-data";
import { regionData, CodeToText } from "element-china-area-data";
export default {
  name: "app-area-cascader",
  model: {
    prop: "selected",
    event: "change",
  },
  props: {
    selected: {
      type: Array,
      default: [],
    },
  },
  computed: {
    addressStr() {
      if (this.selected && this.selected.length > 0) {
        return this.selected.map(s => CodeToText[s]).join('/')
      }
      return "";
    },
  },
  data() {
    return {
      regionData,
    };
  },
  methods: {
    handleChange(val) {
      this.$emit("change", val || []);
    },
  },
};
</script>
