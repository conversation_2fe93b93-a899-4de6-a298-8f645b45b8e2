import request from '@/utils/request'
import { serviceArea } from '@/api/serviceArea'

let serviceAreaName = serviceArea.business + '/SalesDataAnalysis/'

/** 获取转订单情况 */
export function getBusinessOpportunityToOrder(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityToOrder',
        method: 'get',
        params
    })
}


/** 获取各地区产值情况 */
export function getRegionalOutputTargetDataAnalysis(params) {
    return request({
        url: serviceAreaName + 'GetRegionalOutputTargetDataAnalysis',
        method: 'get',
        params
    })
}

/** 获取客户画像 */
export function getCustomerPortrait(params) {
    return request({
        url: serviceAreaName + 'GetCustomerPortrait',
        method: 'get',
        params
    })
}


/** 统计 客户数量/订单数量/订单金额 */
export function statisticsQuantity(params) {
    return request({
        url: serviceAreaName + 'StatisticsQuantity',
        method: 'get',
        params
    })
}


/** 获取客户数据分析 */
export function getCustomerDataAnalysis(params) {
    return request({
        url: serviceAreaName + 'GetCustomerDataAnalysis',
        method: 'get',
        params
    })
}


/** 获取商机数据分析 */
export function getBusinessOpportunityDataAnalysis(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityDataAnalysis',
        method: 'get',
        params
    })
}


/**获取投标数据分析 */
export function getBiddingDataAnalysis(params) {
    return request({
        url: serviceAreaName + 'GetBiddingDataAnalysis',
        method: 'get',
        params
    })
}


/**获取订单数据分析 */
export function getOrderDataAnalysis(params) {
    return request({
        url: serviceAreaName + 'GetOrderDataAnalysis',
        method: 'get',
        params
    })
}

/**产值数据分析 */
export function GetNewOrderDataAnalysis(params) {
    return request({
        url: serviceAreaName + 'GetNewOrderDataAnalysis',
        method: 'get',
        params
    })
}

/**订单（项目）产值明细 */
export function GetNewOrderDataAnalysisDetail(params) {
    return request({
        url: serviceAreaName + 'GetNewOrderDataAnalysisDetail',
        method: 'get',
        params
    })
}


export function getOrderRegionalDataAnalysis(params) {
    return request({
        url: serviceAreaName + 'GetOrderRegionalDataAnalysis',
        method: 'get',
        params
    })
}


