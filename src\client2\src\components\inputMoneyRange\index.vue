<template>
<div class="vl-numberRangeMain">
    <el-popover :disabled="disabled" v-model="vlNumberRangeMainPopoverVisible"
    placement="bottom-start" popper-class="vlNumberRangeMainPopover" @after-leave="popoverHide"
    width="300" ref="vlNumberRangeMainPopover"
    trigger="focus">
        <div class="vl-numberRangeMainInput popper">
            <div class="vl-numberRangeMainInput_number">
                <el-input type="number" placeholder="开始值" maxlength="8" :disabled="disabled" v-model="leftValue1" v-popover:popover
                @input="inputChange('leftValue1')" @wheel.native.prevent="stopScrollFun($event)"></el-input>
            </div>
            <div class="vl-numberRangeMainInput_separator">~</div>
            <div class="vl-numberRangeMainInput_number">
                <el-input type="number" placeholder="结束值" maxlength="8" :disabled="disabled" v-model="rightValue1" v-popover:popover
                @input="inputChange('rightValue1')" @wheel.native.prevent="stopScrollFun($event)"></el-input>
            </div>
        </div>
        <div class="vl-numberRangeMainInputBtnBox">
            <el-button type="text" @click="submitInput(false)">清空</el-button>
            <el-button @click="submitInput(true)" :disabled="!(leftValue1&&rightValue1)">确定</el-button>
        </div>
        <div class="vl-numberRangeMainInput" slot="reference">
            <div>
                <el-input :disabled="disabled" v-model="leftValue"></el-input>
            </div>
            <div class="vl-numberRangeMainInput_separator">~</div>
            <div>
                <el-input :disabled="disabled" v-model="rightValue"></el-input>
            </div>
        </div>
    </el-popover>
</div>
</template>
<script>
export default {
    name: 'numberRangeMain',
    props: {
        // 数组值
        value: {
            type: Array,
            default: () => {
                return []
            }
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            default: false
        },
        // // 控制按钮位置
        // controlsPosition: {
        //     type: String,
        //     default: "right"
        // },
        // // 是否使用控制按钮
        // controls: {
        //     type: Boolean,
        //     default: false
        // },
        // // 数值精度
        // precision: {
        //     type: Number,
        //     default: 0
        // },
        // 最小值
        min: {
            type: Number,
            default: 0
        },
        // 最大值
        max: {
            type: Number,
            default: 99999999
        },
    },
    data() {
        return {
            vlNumberRangeMainPopoverVisible: false,
            leftValue: null,
            rightValue: null,
            leftValue1: null,
            rightValue1: null,
            isBtnDisdb: false,
        }
    },
    computed: {
        maxLeft() {
            if (this.rightValue&&this.rightValue>0){
                return this.rightValue
            }
            return this.max
        },
        minRight() {
            if (this.leftValue&&this.leftValue>0){
                return this.leftValue
            }
            return this.min
        },
    },
    watch: {
        value (val){
            // console.log(val)
            if (val.length===0) {
                this.leftValue = null
                this.rightValue = null
                this.leftValue1 = null
                this.rightValue1 = null
            } else {
                this.leftValue = val[0] > val [1] ? val[1] : val[0]
                this.rightValue =  val[0] > val [1] ? val[0] : val[1]
                this.leftValue1 =  val[0] > val [1] ? val[1] : val[0]
                this.rightValue1 = val[0] > val [1] ? val[0] : val[1]
            }
        },
        vlNumberRangeMainPopoverVisible: {
            handler(val) {
                if (!val&&this.isBtnDisdb) {
                    this.isBtnDisdb = false
                }
            },
            immediate: true,
        },
    },
    methods: {
        submitInput(type) {
            let lVal = Math.floor(this.leftValue1 * 100) / 100,
                rVal = Math.floor(this.rightValue1 * 100) / 100;
            // console.log('leftValue1', lVal)
            // console.log('rightValue1', rVal)
            // console.log('leftValue1>rightValue1', lVal>rVal)
            if (type) {
                if (lVal>rVal) {
                    lVal = lVal > this.max ? this.max : lVal
                    rVal = rVal < this.min ? this.min : rVal
                    this.$emit('input', [rVal, lVal])
                } else {
                    rVal = rVal > this.max ? this.max : rVal
                    lVal = lVal < this.min ? this.min : lVal
                    this.$emit('input', [lVal, rVal])
                }
            } else {
                this.leftValue = null
                this.rightValue = null
                this.leftValue1 = null
                this.rightValue1 = null
                this.$emit('input', [])
            }
            this.isBtnDisdb = true
            this.vlNumberRangeMainPopoverVisible = false
        },
        inputChange(name){
            // console.log(name, this[name])
            // this[name] = (this[name] + '').replace('-', '')
            // if (this.vlNumberRangeMainPopoverVisible){
            //     this.submitInput(true)
            //     console.log(this.vlNumberRangeMainPopoverVisible)
            // }
        },
        popoverHide() {
            if (this.isBtnDisdb) return false;
            let val = JSON.parse(JSON.stringify(this.value))
            if (val.length===0) {
                this.leftValue = null
                this.rightValue = null
                this.leftValue1 = null
                this.rightValue1 = null
            }else{
                this.leftValue = val[0] > val [1] ? val[1] : val[0]
                this.rightValue =  val[0] > val [1] ? val[0] : val[1]
                this.leftValue1 =  val[0] > val [1] ? val[1] : val[0]
                this.rightValue1 = val[0] > val [1] ? val[0] : val[1]
                // this.$emit('input', val)
            }
            this.$forceUpdate()
            this.isBtnDisdb = false
            // console.log(this.vlNumberRangeMainPopoverVisible, val)
        },
        stopScrollFun(evt) {
            evt = evt || window.event;
            if(evt.preventDefault) {
                // Firefox
                evt.preventDefault();
                evt.stopPropagation();
            } else {
                // IE
                evt.cancelBubble=true;
                evt.returnValue = false;
            }
            return false;
        }
    }
}
</script>

<style>
.vlNumberRangeMainPopover{padding: 0;}
.vlNumberRangeMainPopover input::-webkit-outer-spin-button,
.vlNumberRangeMainPopover input::-webkit-inner-spin-button{
    -webkit-appearance:textfield;
}    
.vlNumberRangeMainPopover input[type="number"]{
    -moz-appearance:textfield;
}
.vlNumberRangeMainPopover input::-webkit-outer-spin-button,
.vlNumberRangeMainPopover input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}
.vlNumberRangeMainPopover input[type="number"]{
    -moz-appearance: textfield;
}
</style>
<style scoped>
.vl-numberRangeMainInput >>>.el-input-number{
    width: 100% !important;
}
.vl-numberRangeMainInput >>>.el-input-number .el-input__inner{
    text-align: left;
}
.vl-numberRangeMainInput{
    display: flex;
}
.vl-numberRangeMainInput_separator{
    width: 20px;
    line-height: 28px;
    text-align: center;
}
.vl-numberRangeMainInput_number{
    flex: 1;
}
.vl-numberRangeMainInputBtnBox{
    border-top: 1px solid #e4e4e4;
    padding: 4px;
    text-align: right;
    background-color: #FFF;
    position: relative;
    font-size: 0;
}
.vl-numberRangeMainInput.popper{
    padding: 10px;
}

</style>