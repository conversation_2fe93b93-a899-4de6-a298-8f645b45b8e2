
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/Maintenance/'


export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}
export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

//处理报修单
export function dealWithAsync(data) {
    return request({
        url: serviceAreaName + 'DealWithAsync',
        method: 'post',
        data
    })
}

//获得设备报修分页记录
export function getEquipmentRepairRecordPageAsync(data) {
    return request({
        url: serviceAreaName + 'GetEquipmentRepairRecordPageAsync',
        method: 'post',
        data
    })
}

export function assign(data) {
    return request({
        url: serviceAreaName + 'Assign',
        method: 'post',
        data
    })
}

export function createApproval(data) {
    return request({
        url: serviceAreaName + 'ApprovalAsync',
        method: 'post',
        data
    })
}

export function getListMiniPage(data) {
    return request({
        url: serviceAreaName + 'GetListMiniPage',
        method: 'post',
        data
    })
}

export function getImplementerList(params) {
    return request({
        url: serviceAreaName + 'GetHandlerEmployeeListAsync',
        method: 'get',
        params
    })
}

export function getReportSheetTrendChart(params) {
    return request({
        url: serviceAreaName + 'GetReportSheetTrendChartAsync',
        method: 'get',
        params
    })
}


export function getPercentageCompleteChart(params) {
    return request({
        url: serviceAreaName + 'GetPercentageCompleteChartAsync',
        method: 'get',
        params
    })
}


export function getSupplierCount(data) {
    return request({
        url: serviceAreaName + 'GetSupplierCount',
        method: 'post',
        data
    })
}


export function getFaultPhenomenonAnalysisChart(data) {
    return request({
        url: serviceAreaName + 'GetFaultPhenomenonAnalysisChart',
        method: 'post',
        data
    })
}
export function getFaultChart(data) {
    return request({
        url: serviceAreaName + 'GetFaultPhenomenonAnalysisStructPartChart',
        method: 'post',
        data
    })
}

export function getFaultChartCount(data) {
    return request({
        url: serviceAreaName + 'GetFaultPhenomenonAnalysisStructPartChartCount',
        method: 'post',
        data
    })
}



export function getAccessoriesLossAnalysis(data) {
    return request({
        url: serviceAreaName + 'GetAccessoriesLossAnalysis',
        method: 'post',
        data
    })
}

export function getAccessoriesLossAnalysisCount(data) {
    return request({
        url: serviceAreaName + 'GetAccessoriesLossAnalysisCount',
        method: 'post',
        data
    })
}



export function getStatisticalStatesNumber(params) {
    return request({
        url: serviceAreaName + 'GetStatisticalStatesNumber',
        method: 'get',
        params
    })
}



export function saveRegionalManagements(data) {
    return request({
        url: serviceAreaName + 'SaveRegionalManagements',
        method: 'post',
        data
    })
}


export function getSaveRegionalManagements(params) {
    return request({
        url: serviceAreaName + 'GetSaveRegionalManagements',
        method: 'get',
        params
    })
}

//售后结算
export function getAfterSalesSettlement(data) {
    return request({
        url: serviceAreaName + 'GetAfterSalesSettlement',
        method: 'post',
        data
    })
}

export function getAfterSalesSettlementNew(data) {
    return request({
        url: serviceAreaName + 'GetAfterSalesSettlementNew',
        method: 'post',
        data
    })
}

export function getAfterSalesSettlementCountMoney(data) {
    return request({
        url: serviceAreaName + 'GetAfterSalesSettlementCountMoney',
        method: 'post',
        data
    })
}

export function getFaultPhenomenonAnalysis(data) {
    return request({
        url: serviceAreaName + 'GetFaultPhenomenonAnalysis',
        method: 'post',
        data
    })
}

export function batchUpdateAfterContract(data) {
    return request({
        url: serviceAreaName + 'BatchUpdateAfterContract',
        method: 'post',
        data
    })
}

export function cancelContract(data) {
    return request({
        url: serviceAreaName + 'CancelContract',
        method: 'post',
        data
    })
}

export function faultTypeStatistical(data) {
    return request({
        url: serviceAreaName + 'FaultTypeStatistical',
        method: 'post',
        data
    })
}

export function AssignList(data) {
    return request({
        url: serviceAreaName + 'AssignList',
        method: 'post',
        data
    })
}

export function getClock(data) {
    return request({
        url: serviceAreaName + 'GetClock',
        method: 'post',
        data
    })
}

