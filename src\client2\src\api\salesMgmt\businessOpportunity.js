
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/BusinessOpportunity/'


/******************************************** 商机 ********************************************/
/**新增商机 */
export function addBusinessOpportunity(data) {
    return request({
        url: serviceAreaName + 'AddBusinessOpportunity',
        method: 'post',
        data
    })
}

/**删除商机 */
export function deleteBusinessOpportunity(data) {
    return request({
        url: serviceAreaName + 'DeleteBusinessOpportunity',
        method: 'post',
        data
    })
}

export function editBusinessOpportunityStatus(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityStatus',
        method: 'post',
        data
    })
}

/**编辑商机 */
export function editBusinessOpportunity(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunity',
        method: 'post',
        data
    })
}

/**获取商机分页列表 */
export function getBusinessOpportunityListPage(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityListPage',
        method: 'post',
        data
    })
}

/**获取商机详情 */
export function getBusinessOpportunityDetails(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityDetails',
        method: 'get',
        params
    })
}

/**获取商机完整列表 */
export function getBusinessOpportunityListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 商机阶段 ********************************************/
/**商机阶段集合操作（新增、编辑） */
export function editBusinessOpportunityPhaseList(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityPhaseList',
        method: 'post',
        data
    })
}

/**编辑商机阶段 */
export function editBusinessOpportunityPhase(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityPhase',
        method: 'post',
        data
    })
}

/**获取商机阶段分页列表 */
export function getBusinessOpportunityPhaseListPage(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityPhaseListPage',
        method: 'post',
        data
    })
}

/**获取商机阶段详情 */
export function getBusinessOpportunityPhaseDetails(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityPhaseDetails',
        method: 'get',
        params
    })
}

/**获取商机阶段完整列表 */
export function getBusinessOpportunityPhaseListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityPhaseListByCondition',
        method: 'post',
        data
    })
}
/******************************************** 商机需求分析 ********************************************/
/**新增商机需求分析 */
export function addBusinessOpportunityDemand(data) {
    return request({
        url: serviceAreaName + 'AddBusinessOpportunityDemand',
        method: 'post',
        data
    })
}

/**删除商机需求分析 */
export function deleteBusinessOpportunityDemand(data) {
    return request({
        url: serviceAreaName + 'DeleteBusinessOpportunityDemand',
        method: 'post',
        data
    })
}

/**编辑商机需求分析 */
export function editBusinessOpportunityDemand(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityDemand',
        method: 'post',
        data
    })
}

/**获取商机需求分析分页列表 */
export function getBusinessOpportunityDemandListPage(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityDemandListPage',
        method: 'post',
        data
    })
}

/**获取商机需求分析详情 */
export function getBusinessOpportunityDemandDetails(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityDemandDetails',
        method: 'get',
        params
    })
}

/**获取商机需求分析完整列表 */
export function getBusinessOpportunityDemandListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityDemandListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 商机立项评估 ********************************************/
/**新增商机立项评估 */
export function addBusinessOpportunityProject(data) {
    return request({
        url: serviceAreaName + 'AddBusinessOpportunityProject',
        method: 'post',
        data
    })
}

/**删除商机立项评估 */
export function deleteBusinessOpportunityProject(data) {
    return request({
        url: serviceAreaName + 'DeleteBusinessOpportunityProject',
        method: 'post',
        data
    })
}

/**编辑商机立项评估 */
export function editBusinessOpportunityProject(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityProject',
        method: 'post',
        data
    })
}

/**获取商机立项评估分页列表 */
export function getBusinessOpportunityProjectListPage(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityProjectListPage',
        method: 'post',
        data
    })
}

/**获取商机立项评估详情 */
export function getBusinessOpportunityProjectDetails(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityProjectDetails',
        method: 'get',
        params
    })
}

/**获取商机立项评估完整列表 */
export function getBusinessOpportunityProjectListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityProjectListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 商机方案制定 ********************************************/
/**新增商机方案制定 */
export function addBusinessOpportunityScheme(data) {
    return request({
        url: serviceAreaName + 'AddBusinessOpportunityScheme',
        method: 'post',
        data
    })
}

/**删除商机方案制定 */
export function deleteBusinessOpportunityScheme(data) {
    return request({
        url: serviceAreaName + 'DeleteBusinessOpportunityScheme',
        method: 'post',
        data
    })
}

/**编辑商机方案制定 */
export function editBusinessOpportunityScheme(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityScheme',
        method: 'post',
        data
    })
}

/**获取商机方案制定分页列表 */
export function getBusinessOpportunitySchemeListPage(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunitySchemeListPage',
        method: 'post',
        data
    })
}

/**获取商机方案制定详情 */
export function getBusinessOpportunitySchemeDetails(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunitySchemeDetails',
        method: 'get',
        params
    })
}

/**获取商机方案制定完整列表 */
export function getBusinessOpportunitySchemeListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunitySchemeListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 商机关联关系 ********************************************/

/**编辑关联客户的关联度 */
export function changeCustomerStatus(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityRelation',
        method: 'post',
        data
    })
}

/**新增商机关联关系 */
export function addBusinessOpportunityRelation(data) {
    return request({
        url: serviceAreaName + 'AddBusinessOpportunityRelation',
        method: 'post',
        data
    })
}

/**删除商机关联关系 */
export function deleteBusinessOpportunityRelation(data) {
    return request({
        url: serviceAreaName + 'DeleteBusinessOpportunityRelation',
        method: 'post',
        data
    })
}

/**编辑商机关联关系 */
export function editBusinessOpportunityRelation(data) {
    return request({
        url: serviceAreaName + 'EditBusinessOpportunityRelation',
        method: 'post',
        data
    })
}

/**获取商机关联关系分页列表 */
export function getBusinessOpportunityRelationListPage(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityRelationListPage',
        method: 'post',
        data
    })
}

/**获取商机关联关系详情 */
export function getBusinessOpportunityRelationDetails(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityRelationDetails',
        method: 'get',
        params
    })
}

/**获取商机关联关系完整列表 */
export function getBusinessOpportunityRelationListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityRelationListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 自定义 ********************************************/
/**获取机会点产值信息 */
export function getProjectStatisticsInfo(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityPhaseStatisticsData',
        method: 'post',
        data
    })
}

/**获取商机统计 */
export function getBusinessOpportunityStatistics(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityStatistics',
        method: 'get',
        params
    })
}
/**获取商机阶段统计 */
export function getBusinessOpportunityPhaseStatistics(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityPhaseStatistics',
        method: 'get',
        params
    })
}

/**获取商机负责人列表 */
export function getBusinessOpportunityEmployeeList(params) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityEmployeeList',
        method: 'get',
        params
    })
}

export function saveFollowUpNotes(data) {
    return request({
        url: serviceAreaName + 'SaveFollowUpNotes',
        method: 'post',
        data
    })
}


export function termination(data) {
    return request({
        url: serviceAreaName + 'Termination',
        method: 'post',
        data
    })
}


/**新增附件 */
export function upAttachment(data) {
    return request({
        url: serviceAreaName + 'UpAttachment',
        method: 'post',
        data
    })
}

export function getDataStatistics(data) {
    return request({
        url: serviceAreaName + 'DataStatistics',
        method: 'post',
        data
    })
}

export function getPortrait(data) {
    return request({
        url: serviceAreaName + 'GetPortrait',
        method: 'post',
        data
    })
}