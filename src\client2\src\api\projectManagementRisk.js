import request from "@/utils/request";
import { serviceArea } from "./serviceArea";

let serviceAreaName = serviceArea.business;

//风险草稿保存
export function addOrUpdateProjectManagementRiskForSave(data) {
  return request({
    url:
      serviceAreaName +
      "/ProjectManagmentRisk/AddOrUpdateProjectManagementRiskForSave",
    method: "post",
    data
  });
}

//风险提交
export function addOrUpdateProjectManagementRiskForSubmit(data) {
  return request({
    url:
      serviceAreaName +
      "/ProjectManagmentRisk/AddOrUpdateProjectManagementRiskForSubmit",
    method: "post",
    data
  });
}

// 责任人 提审
export function projectManagementRiskOwnerSubmitForApproval(data) {
  return request({
    url:
      serviceAreaName +
      "/ProjectManagmentRisk/ProjectManagementRiskOwnerSubmitForApproval",
    method: "post",
    data
  });
}

// 责任人 驳回
export function projectManagementRiskOwnerSubmitForReject(data) {
  return request({
    url:
      serviceAreaName +
      "/ProjectManagmentRisk/ProjectManagementRiskOwnerSubmitForReject",
    method: "post",
    data
  });
}

// 责任人 转审
export function projectManagementRiskOwnerSubmitForTransfer(data) {
  return request({
    url:
      serviceAreaName +
      "/ProjectManagmentRisk/ProjectManagementRiskOwnerSubmitForTransfer",
    method: "post",
    data
  });
}

// 审批人 同意
export function projectManagementRiskApproverSubmitForApproved(data) {
  return request({
    url:
      serviceAreaName +
      "/ProjectManagmentRisk/ProjectManagementRiskApproverSubmitForApproved",
    method: "post",
    data
  });
}

// 审批人 驳回
export function projectManagementRiskApproverSubmitForReject(data) {
  return request({
    url:
      serviceAreaName +
      "/ProjectManagmentRisk/ProjectManagementRiskApproverSubmitForReject",
    method: "post",
    data
  });
}

// 审批人 转审
export function projectManagementRiskApproverSubmitForTransfer(data) {
  return request({
    url:
      serviceAreaName +
      "/ProjectManagmentRisk/ProjectManagementRiskApproverSubmitForTransfer",
    method: "post",
    data
  });
}

// 删除
export function deleteProjectManagementRisk(data) {
  return request({
    url: serviceAreaName + "/ProjectManagmentRisk/DeleteProjectManagementRisk",
    method: "post",
    data
  });
}

// 获取风险明细
export function getProjectManagementRisk(params) {
  return request({
    url: serviceAreaName + "/ProjectManagmentRisk/GetProjectManagementRisk",
    method: "get",
    params
  });
}

// 搜索风险
export function searchProjectManagementRisk(data) {
  return request({
    url: serviceAreaName + "/ProjectManagmentRisk/SearchProjectManagementRisk",
    method: "post",
    data
  });
}
//审批历史记录
export function getProjectManagmentRiskFlowOperationHistories(data) {
  return request({
    url: serviceAreaName + "/ProjectManagmentRisk/GetProjectManagmentRiskFlowOperationHistories",
    method: "post",
    data
  });
}