import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

export function getList(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/GetDynamicFormList',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + '/DynamicForm/GetDynamicFormDetails',
        method: 'get',
        params
    })
}


//操作表单数据
export function getDatas(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/GetDynamicFormData',
        method: 'post',
        data
    })    
}

export function addData(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/AddDynamicFormData',
        method: 'post',
        data
    })
}

export function delData(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/DeleteDynamicFormData',
        method: 'post',
        data
    })
}

export function editData(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/EditDynamicFormData',
        method: 'post',
        data
    })    
}

