
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/train/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function setIsShow(data) {
    return request({
        url: serviceAreaName + 'SetIsShow',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}
export function BatchEdit(data) {
    return request({
        url: serviceAreaName + 'BatchEdit',
        method: 'post',
        data
    })
}

// 获取可见时段
export function GetVisibleInfo(params) {
    return request({
        url: serviceAreaName + 'GetVisibleInfo',
        method: 'get',
        params
    })
}

export function AddTrainLearningLesson(data) {
    return request({
        url: serviceAreaName + 'AddTrainLearningLesson',
        method: 'post',
        data
    })
}
export function GetTrainLearningLessonDetail(params) {
    return request({
        url: serviceAreaName + 'GetTrainLearningLessonDetail',
        method: 'get',
        params
    })
}

export function GetTrainLearningLessonDetailsTree(params) {
    return request({
        url: serviceAreaName + 'GetTrainLearningLessonDetailsTree',
        method: 'get',
        params
    })
}

export function GetComment(params) {
    return request({
        url: serviceAreaName + 'GetComment',
        method: 'get',
        params
    })
}
// 新增评论
export function AddComment(data) {
    return request({
        url: serviceAreaName + 'AddComment',
        method: 'post',
        data
    })
}
export function revocation(params) {
    return request({
        url: serviceAreaName + 'Recall',
        method: 'get',
        params
    })
}
export function SetGiveLike(params) {
    return request({
        url: serviceAreaName + 'SetGiveLike',
        method: 'get',
        params
    })
}


export function getDepartmentEmployeeTree(params) {
    return request({
        url: serviceAreaName + 'GetDepartmentEmployeeTree',
        method: 'get',
        params
    })
}

export function getTrainLearningLessonByEmployeeId(params) {
    return request({
        url: serviceAreaName + 'GetTrainLearningLessonByEmployeeId',
        method: 'get',
        params
    })
}


export function getSharedPeopleStatistics(params) {
    return request({
        url: serviceAreaName + 'GetSharedPeopleStatistics',
        method: 'get',
        params
    })
}

export function getTrainLearningLessonDetailApp(params) {
    return request({
        url: serviceAreaName + 'GetTrainLearningLessonDetailApp',
        method: 'get',
        params
    })
}


