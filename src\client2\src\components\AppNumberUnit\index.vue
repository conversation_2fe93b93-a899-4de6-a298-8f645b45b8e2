<template>
<!-- 带单位的金额输入文本框 -->
<div class="vl-numberUnit">
    <el-input-number class="vl-numberUnit-number" :disabled="disabled" :placeholder="placeholder"
    :max="max" :precision="precision" :min="min" :controls="controls"
    :value="value" @change="valueInput"></el-input-number>
    <div class="vl-numberUnit-unit">{{unitText}}</div>
</div>
</template>
<script>
export default {
    name: 'app-number-unit',
    props: {
        // 数组值
        value: {
            default: ''
        },
        // 是否禁用
        disabled: {
            type: <PERSON>olean,
            default: false
        },
        // placeholder
        placeholder: {
            type: String,
            default: ""
        },
        unitText: {
            type: String,
            require: true,
            default: "万"
        },
        // 控制按钮位置
        controlsPosition: {
            type: String,
            default: "right"
        },
        // 是否使用控制按钮
        controls: {
            type: Boolean,
            default: false
        },
        // 数值精度
        precision: {
            type: Number,
            default: 2
        },
        // 最小值
        min: {
            type: Number,
            default: 0
        },
        // 最大值
        max: {
            type: Number,
            default: 999999999
        },
    },
    computed: {
    },
    created(){
        if(this.value){
            if(this.value>=this.min){
                this.$emit('input', this.value||0)
            }else{
                this.$emit('input', this.min||0)
            }
        }else{
            this.$emit('input', this.min||0)
        }
    },
    methods: {
        valueInput(val) {
            console.log(val)
            this.$emit('input', val||0)
        },
    }
}
</script>
<style scoped>
.vl-numberUnit >>>.el-input-number .el-input__inner{
    text-align: left;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
</style>
<style lang="scss" scoped>
.vl-numberUnit {
    width: 100%;
    overflow: hidden;
    display: inline-table;
    &-number {
        width: auto;
        overflow: hidden;
        vertical-align: middle;
        display: table-cell;
    }
    &-unit{
        vertical-align: middle;
        display: table-cell;
        background-color: #fafbfc;
        position: relative;
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        border-color: #edeff3;
        border-left: 0;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        padding: 0 20px;
        width: 1px;
        white-space: nowrap;
        color: #9b9b9b;
        line-height: 26px;
        overflow: hidden;
    }
}
</style>