import request from '@/utils/request'
// import { get } from 'http';
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        // url: '/RepairOrder/GetRepairOrdersByPage',
        url: serviceAreaName + '/RepairOrder/GetRepairFlowPage',
        method: 'post',
        data
    })
}

//第一次创建维修单
export function create(data) {
    return request({
        url: serviceAreaName + '/Flow/CreateRepairOrderFlowInstance',
        method: 'post',
        data
    })
}

//被驳回或自己撤回后再次提交
export function resubmit(data) {
    return request({
        url: serviceAreaName + '/Flow/ResubmitRepairOrderFlow',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/RepairOrder/EditRepairOrder',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + '/RepairOrder/DeleteById?id=' + data.id,
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + '/RepairOrder/GetRepairOrderFlowDetail',
        // url: '/RepairOrder/GetRepairOrderById',
        method: 'get',
        params
    })
}



//获取所有集团组织下所有 设备-属性-属性值
export function getEquList() {
    return request({
        url: serviceAreaName + '/Equipment/GetEquipmentsGroup',
        method: 'get',
    })
}

//获取车辆信息
export function getVehicles() {
    return request({
        url: serviceAreaName + '/RepairOrder/GetVehicles',
        method: 'get'
    })
}


export function exportFile(data) {
    return request({
        url: serviceAreaName + '/RepairOrder/Export',
        method: 'post',
        data
    })
}

