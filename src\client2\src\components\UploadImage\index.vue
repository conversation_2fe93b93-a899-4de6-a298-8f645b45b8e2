<template>
  <div class="image_upload_container" :class="{ disabled_upload: disabled }">
    <slot name="tip" class="tip" v-if="!disabled"></slot>
    <el-upload
      :class="{ hide: fileList.length >= limit || disabled }"
      action="/api/Resources/FileUpload/UploadFile"
      list-type="picture-card"
      :headers="{
        Token: getToken(),
      }"
      :file-list="fileList"
      :multiple="multiple"
      :limit="limit"
      accept=".png,.jpg,.jpeg,.PNG,.JPG,.JPEG"
      :disabled="disabled"
      :on-success="success"
      :on-error="error"
      :on-exceed="exceed"
    >
      <div slot="file" slot-scope="{ file }" class="file_list" v-if="file.status === 'success'">
        <el-image :src="file.url" fit="cover" class="image" :preview-src-list="previewSrcList" />
        <div class="hover_container" v-if="!disabled">
          <slot name="imageHover" :file="file"></slot>
        </div>
      </div>
      <i class="el-icon-plus" />
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  props: {
    // 文件数组
    list: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: Number,
      default: 1,
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
  created() {
    this.fileList = this.$_.cloneDeep(this.list);
  },
  computed: {
    previewSrcList() {
      return this.fileList.map(t => t.url);
    },
  },
  methods: {
    getToken,
    // 文件列表移除文件时的钩子
    remove(file) {
      this.fileList = this.fileList.filter(t=> t.id !== file.id)
      this.$emit("change", this.fileList);
    },
    // 文件上传成功时的钩子
    success(res, file, fileList) {
      if (res?.HttpStatusCode === 200) {
        const fileListRes = fileList.map(item => {
          if (!item.id) {
            item.id = item?.response?.TMessageData?.Id || "";
          }
          return item;
        });
        this.fileList = fileListRes;
        this.$emit("change", this.fileList);
      } else {
        // 过滤上传失败的图片
        this.fileList = this.fileList.filter(item => item.status !== "success");
        this.$message.error("图片上传失败");
      }
    },
    // 文件上传失败时的钩子
    error() {
      this.$message.error("图片上传失败");
    },
    // 文件超出个数限制时的钩子
    exceed(){
      this.$message.error(`最多上传${this.limit}张`)
    }
  },
};
</script>

<style lang="scss" scoped>
.hide {
  /deep/.el-upload--picture-card {
    display: none;
  }
}
.tip {
  color: $text-label-color;
}
.file_list {
  width: inherit;
  height: inherit;
  position: relative;
  &:hover .hover_container {
    display: block;
  }
  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .hover_container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
  }
}
/deep/.el-upload-list--picture-card .el-progress {
  width: inherit;
  height: inherit;
  .el-progress-circle {
    width: inherit !important;
    height: inherit !important;
  }
}
.disabled_upload {
  /deep/.el-upload--picture-card:hover,
  /deep/.el-upload:focus {
    border: 1px dashed #c0ccda;
  }
  /deep/.el-upload--picture-card {
    cursor: not-allowed;
  }
}
</style>
