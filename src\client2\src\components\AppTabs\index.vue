<template>
    <div :class="styleThemes == 'card' ? 'card-tab-wrapper' : 'tab-wrapper'">
        <div :class="styleThemes == 'card' ? 'card-tab-item-wrapper' : 'tab-item-wrapper'">
            <div :class="[{'is-active': idx == currentIdx, 'is-active-only-one' : items.length == 1}, styleThemes == 'card' ? 'card-tab-item' : 'tab-item']" v-for="(item, idx) in items" :key="idx" @click="choiceTab(idx)">{{ item }}</div>
            <div class="tab-opt-wrapper" v-if="showOpt">
                <div class="tab-opt" @click="handleShowMore">更多 <img src="../../assets/images/more.png" /></div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'app-tabs',
    props: {
        //tabs主题
        styleThemes: {
            type: String,
            default: 'default', // default、card
        },
        //选择当前tabs的项集合
        items: {
            type: Array,
            default: () => {
                return []
            }
        },
        //当前选中的tabs项索引
        active: {
            type: Number,
            default: 0
        },
        //是否显示更多按钮
        showOpt: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            currentIdx: this.active,
            
        }
    },
    methods: {
        choiceTab(idx) {
            this.currentIdx = idx
            this.$emit('update:active', this.currentIdx)
        },
        handleShowMore() {
            this.$emit('handleShowMore')
        }
    }
}
</script>

<style scoped>

.tab-wrapper, .card-tab-wrapper, .tab-item, .tab-opt, .card-tab-item{
    height: 54px;
    line-height: 54px;
}

.tab-item-wrapper, .card-tab-item-wrapper{
    position: relative;
}

.tab-wrapper, .card-tab-wrapper{
    box-sizing: border-box;
    border-bottom: 1px solid #e4e7ed;
}

.card-tab-wrapper{
    background: #fafafa;
}

.tab-item, .card-tab-item{
    display: inline-block;
    
    cursor: pointer;
    font-size: 14px;
    transition: background-color .3s;
}

.tab-item{
    margin: 0 15px;
    color: #9c9faf;
}

.card-tab-item{
    box-sizing: border-box;
    padding: 0 15px;
    font-size: 20px;
    color: #9c9faf;
    border-left: 1px solid transparent;
    border-right: 1px solid transparent;
}

.is-active{
    font-weight: 600;
}

.tab-item.is-active{
    color: #409eff;
    font-size: 16px;
    border-bottom: 2px solid #409eff;
}

.card-tab-item.is-active{
    background: #fff;
    border-left: 1px solid #e4e7ed;
    border-right: 1px solid #e4e7ed;
    color: #000034;
}

/* 如果只有一个页签 */
.card-tab-item.is-active-only-one{
    background: transparent;
    border-left: 1px solid transparent;
    border-right: 1px solid transparent;
}

.card-tab-item:first-child.is-active{
    border-left: 1px solid transparent;
}

.tab-opt-wrapper{
    cursor: pointer;
    color: #409eff;
    
}

.tab-opt{
    position: absolute;
    display: inline-block;
    top: 0;
    right: 0;
    padding: 0 15px;
}

.tab-opt img{
    display: inline-block;
    vertical-align: middle;
    margin-top: -3px;
}



</style>
