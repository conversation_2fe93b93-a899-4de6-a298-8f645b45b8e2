import request from "@/utils/request";
import { serviceArea } from "../serviceArea";

let serviceAreaName = serviceArea.business + "/CorporateClientDepartment/";

export function getListPage(data) {
  return request({
    url: serviceAreaName + "GetListPage",
    method: "post",
    data,
  });
}

export function getDynamicState(data) {
  return request({
    url: serviceAreaName + "GetDynamicState",
    method: "post",
    data,
  });
}

export function getCustomerProCardList(data) {
  return request({
    url: serviceAreaName + "GetCustomerProCardList",
    method: "post",
    data,
  });
}
export function dissolveApi(data) {
  return request({
    url: serviceAreaName + "Dissolve",
    method: "post",
    data,
  });
}
export function splitApi(data) {
  return request({
    url: serviceAreaName + "Split",
    method: "post",
    data,
  });
}
export function mergeApi(data) {
  return request({
    url: serviceAreaName + "Merge",
    method: "post",
    data,
  });
}




