import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business+'/PurchaseOrder/';

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function AddReceiving(data){
    return request({
        url: serviceAreaName + 'AddReceiving',
        method: 'post',
        data
    })
}

export function GetListReceiving(params) {
    return request({
        url: serviceAreaName + 'GetListReceiving',
        method: 'get',
        params
    })
}

export function GetReceiving(params) {
    return request({
        url: serviceAreaName + 'GetReceiving',
        method: 'get',
        params
    })
}

export function RevocationReceiving(data){
    return request({
        url: serviceAreaName + 'RevocationReceiving',
        method: 'post',
        data
    })
}