import request from '@/utils/request'

import { serviceArea } from './serviceArea'

let busServiceAreaName = serviceArea.business
let userServiceAreaName = serviceArea.user


export function getList(data) {
    return request({
        url: busServiceAreaName + '/Employee/GetEmployeesByPage',
        method: 'post',
        data
    })
}
//人员弹窗公共组件取人员信息接口
export function getCommonEmpList(data) {
    return request({
        url: busServiceAreaName + '/Employee/GetCommonEmployeesByCondition',
        method: 'post',
        data
    })
}

export function getProjectEmployeeList(data) {
    return request({
        url: busServiceAreaName + '/Employee/GetEmployeesInProjectByPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: busServiceAreaName + '/Employee/GetEmployeeById',
        method: 'get',
        params
    })
}

export function edit(data) {
    return request({
        url: busServiceAreaName + '/Employee/EditEmployee',
        method: 'post',
        data
    })
}


export function del(data) {
    return request({
        url: busServiceAreaName + '/Employee/DeleteEmployees',
        method: 'post',
        data
    })
}

// export function add(data){
//     return request({
//         url: userServiceAreaName + '/Account/AddEmployee',
//         method: 'post',
//         data
//     })
// }

export function addEmp(data) {
    return request({
        url: busServiceAreaName + '/Employee/AddEmployee',
        method: 'post',
        data
    })
}

export function userToEmp(data) {
    return request({
        url: busServiceAreaName + 'Employee/UserToEmployee',
        method: 'post',
        data
    })
}

export function resetPwd(data) {
    return request({
        url: userServiceAreaName + '/Account/ResetPasswordByAdmin',
        method: 'post',
        data
    })
}

export function updatePwd(data) {
    return request({
        url: userServiceAreaName + '/Account/ResetPasswordByPersonal',
        method: 'post',
        data
    })
}

export function register(data) {
    return request({
        url: userServiceAreaName + '/Account/Register',
        method: 'post',
        data
    })
}

export function exportFile(data) {
    return request({
        url: busServiceAreaName + '/Employee/ExportList',
        method: 'post',
        data
    })
}

//根据当前用户获取具有查看权限的用户
export function getAuthUsers(data) {
    return request({
        url: busServiceAreaName + '/Employee/GetTaskManagementEmployess',
        method: 'post',
        data
    })
}