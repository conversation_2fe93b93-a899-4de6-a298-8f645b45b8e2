<template>
  <el-link type="text" class="_cus_btn" v-bind="$attrs" v-on="$listeners" :type="cusType">
    {{ btnText }}
    <slot></slot>
  </el-link>
</template>

<script>
/**
 * 表格行内按钮
 */
export default {
  name: "app-table-row-button",
  props: {
    text: {
      type: String,
      default: "",
    },
    type: {
      type: Number,
      default: 1, //1：编辑；2：详情；3：删除  4: 启用 (文字 是 绿色)；5：警告（橙色）
    },
  },
  computed: {
    cusType() {
      if (this.type == 1) {
        return "primary";
      } else if (this.type == 2) {
        return "primary";
      } else if (this.type == 3) {
        return "danger";
      } else if (this.type == 4) {
        return "success";
      } else if (this.type == 5) {
        return "warning";
      } else {
        return "";
      }
    },
    btnText() {
      if (this.text) {
        return this.text;
      }

      if (this.type == 1) {
        return "编辑";
      } else if (this.type == 2) {
        return "详情";
      } else if (this.type == 3) {
        return "删除";
      } else {
        return "";
      }
    },
  },
  data() {
    return {};
  },
};
</script>

<style scoped>
.danger {
  color: #f56c6c;
}
.green {
  color: #67c23a;
}

._cus_btn {
  padding: 0 !important;
  margin: 0 2px;

  line-height: normal;
  /* 设置 display: inline; 会导致表格列中多个按钮放不下时，隐藏起来，参考
    http://localhost:8080/#/personnelManagement/systemDepartment
  */
  /* display: inline; */
  
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}
</style>
