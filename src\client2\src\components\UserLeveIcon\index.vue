<template>
    <div class="userLeveWarp">
        <i class="userLeveWarp--icon" :class="icon" :style="`color:${leveEnum.find(s=>s.value===number).color}`">
            <i class="userLeveWarp--icon_number" :style="`color:#fff`">{{leveEnum.find(s=>s.value===number).label}}</i>
        </i>
    </div>
</template>

<script>
import {leveEnum} from "./enum";
export default {
    name: 'user-leve-icon',
    props: {
        number: {
            type: Number,
            default: 1
        },
        icon: {
            type: String,
            default: 'el-icon-star-on'
        },
    },
    data(){
        return {
            leveEnum
        }
    },
    methods: {
    },
}
</script>

<style lang='scss' scoped>
.userLeveWarp{
    overflow: hidden;
    &--icon{
        float: left;
        position: relative;
        font-size: 46px;
        &_number{
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -25%);
            font-size: 12px;
            color: #ffffff;
        }
    }
}
</style>