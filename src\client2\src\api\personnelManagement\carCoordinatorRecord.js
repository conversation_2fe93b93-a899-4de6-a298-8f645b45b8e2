
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let carCoordinatorRecordServiceName = serviceArea.business + '/CarCoordinatorRecord/'


export function getList(data) {
    return request({
        url: carCoordinatorRecordServiceName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: carCoordinatorRecordServiceName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: carCoordinatorRecordServiceName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: carCoordinatorRecordServiceName + 'Delete',
        method: 'post',
        data
    })
}

export function getLast(params) {
    return request({
        url: carCoordinatorRecordServiceName + 'GetLastUsers',
        method: 'get',
        params
    })
}