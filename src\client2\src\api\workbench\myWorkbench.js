import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/WorkbenchBlock/'


export function addSchedule(data) {
    return request({
        url: serviceAreaName + 'AddSchedule',
        method: 'post',
        data
    })
}

export function editSchedule(data) {
    return request({
        url: serviceAreaName + 'EditSchedule',
        method: 'post',
        data
    })
}

export function chagneStatus(data) {
    return request({
        url: serviceAreaName + 'EditScheduleStatus',
        method: 'post',
        data
    })
}


export function scheduleDetail(params) {
    return request({
        url: serviceAreaName + 'GetScheduleDetails',
        method: 'get',
        params
    })
}

//获取日历（月）统计信息
export function getMonthScheduleStatistics(params) {
    return request({
        url: serviceAreaName + 'GetMonthScheduleStatistics',
        method: 'get',
        params
    })
}

//获取事项列表（根据月份）
export function getScheduleListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetScheduleListByCondition',
        method: 'post',
        data
    })
}

export function delSchedules(data) {
    return request({
        url: serviceAreaName + 'DeleteSchedule',
        method: 'post',
        data
    })
}


export function getWorkbenchBlockListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetWorkbenchBlockListByCondition',
        method: 'post',
        data
    })
}

export function editWorkbenchBlockList(data) {
    return request({
        url: serviceAreaName + 'EditWorkbenchBlockList',
        method: 'post',
        data
    })
}

export function getMyWorkbenchListPage(data) {
    return request({
        url: serviceAreaName + 'GetMyWorkbenchListPage',
        method: 'post',
        data
    })
}

export function getMyWorkbenchListCount(data) {
    return request({
        url: serviceAreaName + 'GetMyWorkbenchListCount',
        method: 'post',
        data
    })
}

