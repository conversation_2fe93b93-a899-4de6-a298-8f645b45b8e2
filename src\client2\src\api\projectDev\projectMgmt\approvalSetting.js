import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let serviceAreaName = serviceArea.business
let busServiceAreaName = serviceAreaName + '/Approval/'


//项目审批设置
export function SetApprovalPrestore(data) {
    return request({
        url: busServiceAreaName + 'SetApprovalPrestore',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: busServiceAreaName + 'GetApprovalPrestore',
        method: 'get',
        params
    })
}

export function getApprovalList(data) {
    return request({
        url: busServiceAreaName + 'ListPageAsync',
        method: 'post',
        data
    })
}

export function getStatistics(params) {
    return request({
        url: busServiceAreaName + 'GetTotalAsync',
        method: 'get',
        params
    })
}

// 变更联络申请  获取列表
export function GetListChangeContactPage(data) {
    return request({
        url: busServiceAreaName + 'GetListChangeContactPage',
        method: 'post',
        data
    })
}

export function putRead(data) {
    return request({
        url: serviceAreaName + '/Common/Read',
        method: 'post',
        data
    })
}


export function getApprovalHistory(params) {
    return request({
        url: busServiceAreaName + 'GetApprovalHistory',
        method: 'get',
        params
    })
}
