<template>
    <!-- <div class="cus-button">
        <div class="inner-wrapper">
            <slot>
                <i v-if="loading" class="el-icon-loading" style="margin-right: 4px;"></i>
                <svg-icon v-if="iconType" style="font-size: 14px; margin-right: 4px;" :icon-class="iconClass"></svg-icon>
                <span>{{ text }}</span>    
            </slot>

            <el-dropdown trigger="hover" v-if="cmdList && cmdList.length > 0" @command="handleCommand($event)">
                <span class="el-dropdown-link" @click.stop>
                    <i class="el-icon-arrow-down" style="padding-left: 5px; height: 40px; display: flex; align-items: center;"></i>
                </span>
                <el-dropdown-menu slot="dropdown" v-if="!disabled">
                    <el-dropdown-item v-for="(cmd, idx) in cmdList" :key="idx" :command="cmd.value">{{ cmd.label }}</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
    </div> -->
    

    <div class="cus-button" :class="_iconType ? 'custom-button-wrapper' : ''">
        <el-button v-if="!cmdList || cmdList.length == 0" v-bind="$attrs"  :type="type" :disabled="disabled" @click="handleClick" :size="size">
            <div :class="_iconType ? 'btn-item-wrapper' : ''">
                <i v-if="loading" class="el-icon-loading"></i>
                <svg-icon v-if="_iconType" :icon-class="_iconType" className="icon-mini" :style="{marginRight: !text ? '0' : '5px'}"></svg-icon>
                <span>{{ text }}</span>
            </div>
        </el-button>
        <el-dropdown v-else trigger="hover" :type="type" :disabled="disabled" @click="handleClick" @command="handleCommand($event)" :size="size">
            <el-button type="primary">
                <i v-if="loading" class="el-icon-loading"></i>
                <span>{{ text }}</span>
            </el-button>
            <el-dropdown-menu slot="dropdown" v-if="!disabled">
                <el-dropdown-item v-for="(cmd, idx) in cmdList" :key="idx" :command="cmd.value">{{ cmd.label }}</el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>

<script>
export default {
    name: 'app-cus-button',
    props: {
        type: {
            type: String,
            default: '' //primary、danger、warning
        },
        text: {
            type: String,
            default: '确认'
        },
        // svg 图片名称
        iconType: {
            type: String,
            default: '' // baocun bianji 
        },
        // svg 图片名称（扩展）
        iconName: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        },
        loading: {
            type: Boolean,
            default: false
        },
        size: {
            type: String,
            default: 'small'
        },
        cmdList: {
            type: Array,
            default: () => {
                return []
            }
        },
    },
    computed: {
        _iconType() {
            //因为之前提供的很多值都是无效值了（现在使用 svg 图片了），所以需要过滤掉无效值（无效值返回空）
            if(this.iconType == 'baocun' || this.iconType == 'bianji' || this.iconType == 'tuichubianji') {
                return this.iconType
            }else if(this.iconName) {
                return this.iconName
            }
            return ''
        },
        // theme() {
        //     if(!this.type) {
        //         return ''
        //     }else if(this.type == 'primary') {
        //         return 'btn-pri'
        //     }else if(this.type == 'danger') {
        //         return 'btn-danger'
        //     }else if(this.type == 'warning') {
        //         return 'btn-warning'
        //     }
        // },
        // iconClass() {
        //     if(this.iconType == 'close') {
        //         return 'close2-icon'
        //     }else if(this.iconType == 'follow') {
        //         return 'follow-icon'
        //     }else if(this.iconType == 'save') {
        //         return 'save-icon'
        //     }else if(this.iconType == 'del') {
        //         return 'del-icon'
        //     }else if(this.iconType == 'edit') {
        //         return 'edit-icon'
        //     }else if(this.iconType == 'plus') {
        //         return 'plus-icon'
        //     }else if(this.iconType == 'finish') {
        //         return 'finish-icon'
        //     }else if(this.iconType == 'withdraw') {
        //         return 'withdraw-icon'
        //     }else if(this.iconType == 'restart') {
        //         return 'restart-icon'
        //     }
        // },
    },
    data() {
        return {

        }
    },
    methods: {
        handleClick() {
            if(!this.disabled) {
                this.$emit('click')
            }
        },
        handleCommand(cmd) {
            this.$emit('dropdownClick', cmd)
        },
    },
}
</script>

<style lang="scss" scoped>

</style>