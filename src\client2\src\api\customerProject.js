import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

//新增
export function addCustomerProject(data) {
    return request({
        url: serviceAreaName + '/CustomerProject/Add',
        method: 'post',
        data
    })
}

//编辑
export function editCustomerProject(data) {
    return request({
        url: serviceAreaName + '/CustomerProject/Edit',
        method: 'post',
        data
    })
}

//删除
export function deleteCustomerProject(data) {
    return request({
        url: serviceAreaName + '/CustomerProject/Delete',
        method: 'post',
        data
    })
}

//分页列表
export function getCustomerProjectListPage(data) {
    return request({
        url: serviceAreaName + '/CustomerProject/GetListPage',
        method: 'post',
        data
    })
}

//完整列表
export function getCustomerProjectList(params) {
    return request({
        url: serviceAreaName + '/CustomerProject/GetList',
        method: 'get',
        params
    })
}

//导出
export function exportCustomerProject(data) {
    return request({
        url: serviceAreaName + '/CustomerProject/Export',
        method: 'post',
        data
    })
}

//详情
export function getCustomerProjectDetail(params) {
    return request({
        url: serviceAreaName + '/CustomerProject/GetCustomerProjectDetail',
        method: 'get',
        params
    })
}