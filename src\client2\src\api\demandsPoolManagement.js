import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

// 获取所有需求列表
export function getAllDemandsPoolList() {
    return request({
        url: serviceAreaName + '/DemandsPoolManagement/getAllDemandsPoolList',
        method: 'post'
    })
}

// 分页查询
export function getListPage(data) {
    return request({
        url: serviceAreaName + '/DemandsPoolManagement/getListPage',
        method: 'post',
        data
    })
}

// 根据需求ID获取详情
export function getDemandsPoolDetailById(params) {
    return request({
        url: serviceAreaName + '/DemandsPoolManagement/getDemandsPoolDetailById',
        method: 'get',
        params
    })
}

// 操作数据集合
// 添加/修改
export function addOrUpdateDemandsPool(data) {
    return request({
        url: serviceAreaName + '/DemandsPoolManagement/addOrUpdateDemandsPool',
        method: 'post',
        data
    })
}

// 删除需求
export function deleteDemandsPool(data) {
    return request({
        url: serviceAreaName + '/DemandsPoolManagement/deleteDemandsPool',
        method: 'post',
        data
    })
}