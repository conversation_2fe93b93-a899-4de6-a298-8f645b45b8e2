import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/PersonnelFile/GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + '/PersonnelFile/GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/PersonnelFile/Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/PersonnelFile/Edit',
        method: 'post',
        data
    })
}


export function del(data){
    return request({
        url: serviceAreaName + '/PersonnelFile/Delete',
        method: 'post',
        data
    })
}


export function exportFile(data){
    return request({
        url: serviceAreaName + '/PersonnelFile/ExportList',
        method: 'post',
        data
    })
}