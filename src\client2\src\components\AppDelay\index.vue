<template>
    <span @mouseout="handleMouseout" @mouseover="handleMouseover">
        <slot></slot>
    </span>
</template>

<script>
export default {
    name: 'app-delay',
    props: {
        delay: {
            type: Number,
            default: 500
        }
    },
    data() {
        return {
            timer: null
        }
    },
    methods: {
        handleMouseout() {
            this.clearTimer()
        },
        handleMouseover(node) {
            this.clearTimer()
            this.timer = setTimeout(() => {
                this.$emit('trigger')
            }, this.delay);
        },        
        clearTimer() {
            if(this.timer){
                clearTimeout(this.timer)
            }
        },
    },
    deactivated() {
        this.clearTimer()
    },
    destroyed() {
        this.clearTimer()
    },
}
</script>