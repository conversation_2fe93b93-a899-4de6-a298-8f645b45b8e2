/**
 *   参考：
 *       https://www.cnblogs.com/xiahj/p/vue-simple-uploader.html
 *       https://github.com/simple-uploader/Uploader/blob/develop/README_zh-CN.md
 *       https://github.com/simple-uploader/vue-uploader
 *       对于大文件，增量计算md5（不然ie11下会卡死）
 *       https://github.com/satazor/js-spark-md5
 *       https://www.jianshu.com/p/6cc9db0f2c7d
 */

<template>
<div>
    <div class="app-upload-file">
        <ul class="file-list clearfix" v-show="files.length > 0">
            <li
                v-for="(item, idx) in files"
                :key="idx"
                @mouseover="() => delIdx = idx"
                @mouseout="() => delIdx = -1"
            >
                <!-- <a :href="preview ? item[fileProp.filePath] : 'javascript:void(0);'" target="_blank">
                    <img
                        class="file-item"
                        alt="点击放大"
                        :src="item[fileProp.filePath]"
                    >
                </a> -->
                <a>
                    <img
                        v-show="fileType == 1"
                        @click="handlePictureCardPreview(item[fileProp.filePath])"
                        class="file-item file-item-img"
                        alt="点击放大"
                        :src="item[fileProp.filePath]"
                    >
                    <video
                        style='width: 180px; height: 180px; background: #ccc;'
                        controls="controls"
                        v-show="fileType == 3"
                        :src="item[fileProp.filePath]"
                        >
                    </video>
                    <div>
                        <div v-show="fileType == 4" class="file-item file-item-other">
                            <i class="el-icon-document file-logo"></i>
                        </div>
                        <div class="file-info-warpper clearfix">
                            <div class="file-name" :style="{ width: fileType == 4 ? '120px' : '180px' }" :title="item.FileName">
                                <span>{{ item[fileProp.fileName] }}</span>
                            </div>
                            <div class="file-button">
                                <el-button type='text' @click="handleDownload(item[fileProp.filePath], item[fileProp.fileName],item)">下载</el-button>
                            </div>
                        </div>
                    </div>
                </a>
                <i
                    v-if="!readonly"
                    v-show="delIdx == idx"
                    class="file-btn-del el-icon-remove"
                    @click.stop="removeFile(idx)"
                ></i>
            </li>
        </ul>
    </div>
    <div
        v-loading='fileAnalysisLoading'
        element-loading-text="文件分析中..."
        >
        <uploader
            ref="uploader"
            :options="options"
            :file-status-text="statusText"
            :autoStart="false"
            @file-added="onFileAdded"
            @files-added="onFilesAdded"
            @file-complete="fileComplete"
            @file-progress="onFileProgress"
            @file-error="onFileError"
            @file-removed="onFileRemoved"
            class="uploader"
        >
            <!-- <uploader-unsupport></uploader-unsupport> -->
            <!-- <uploader-drop v-show="showUploadBtn">
                <p>Drop files here to upload or</p>
                <uploader-btn :attrs="attrs">select images</uploader-btn>
                <uploader-btn :directory="false">select folder</uploader-btn>
                将文件拖拽至此区域或者 <uploader-btn>选择文件</uploader-btn>
            </uploader-drop> -->
            <uploader-unsupport>
                <template>您的浏览器不支持大文件上传，请改用其他（Chrome、QQ、360）浏览器或较高（IE10+）版本的IE浏览器，以获取更好的体验！</template>
            </uploader-unsupport>
            <uploader-list>
                <ul class="uploader-file-list" slot-scope="props">
                    <!-- <el-button type="primary">
                        <uploader-btn id="global-uploader-btn" :attrs="attrs" ref="uploadBtn" v-show="props.fileList.length < max">
                            选择文件<i class="el-icon-upload el-icon--right"></i>
                        </uploader-btn>
                    </el-button> -->

                    <el-button-group>
                        <uploader-btn v-show="showUploadBtn" id="global-uploader-btn" :attrs="attrs" ref="uploadBtn" class="el-button el-button--primary el-button--mini">
                            选择文件<i class="el-icon-upload el-icon--right"></i>
                        </uploader-btn>
                        <el-button v-show="!readonly" type="text" @click="() => showUploadProgress = !showUploadProgress">
                            <span v-show="!showUploadProgress">显示</span><span v-show="showUploadProgress">隐藏</span>上传进度
                        </el-button>
                    </el-button-group>
                    <div v-show="showUploadProgress && !readonly">
                        <li v-for="file in props.fileList" :key="file.id">
                            <uploader-file :class="'file_' + file.id" ref="files" :file="file" :list="true"></uploader-file>
                        </li>
                        <div class="no-file" v-if="!props.fileList.length"><i class="iconfont icon-empty-file"></i> 暂无待上传文件</div>
                    </div>
                </ul>
            </uploader-list>
            <!-- <uploader-list></uploader-list> -->
        </uploader>
    </div>
</div>
</template>

<script>
import * as common from '@/api/common'
import { getToken } from '@/utils/auth'
import SparkMD5 from 'spark-md5'
import { downloadFile, sizeUnitConversion } from '@/utils/index'
import { serviceArea } from '../../api/serviceArea'

export default {
    name: 'app-upload-big-file',
    props: {
        value: Array, // [{Id: '', Path: ''}]
        readonly: {
            type: Boolean,
            default: false
        },
        //上传文件最大数量
        max: {
            type: Number,
            default: 3
        },
        //已存在多少个文件
        // existsTotal: {
        //     type: Number,
        //     default: 0
        // },
        //上传文件格式限制（如果 fileType == 4，不校验格式——表示接受所有格式）
        accept: {
            type: String,
            default: 'jpg,jpeg,png',
        },
        fileType: {
            type: Number,
            default: 1, //1:图片;2:excel;3:视频;4:其他附件
        },
        //上传文件大小限制（默认不超过3mb）
        fileSize: {
            type: Number,
            default: 1024 * 1024 * 10,
        },
        //总大小限制（1024 * 1024 * 1024 = 1024mb）
        limitTotalSize: {
            type: Number,
            default: -1 //表示不限制
        },
    },
    computed: {
        showUploadBtn() {
            // if(!this.options.singleFile){ //如果是单文件，组件会覆盖，不需要判断
                if(this.max > 0){
                    //不考虑已经上传完成的（已经上传完成的会加到调用页面）
                    let isCompletes = this.uploader.fileList.filter(i => this.completedFiles.findIndex(o => o == i.uniqueIdentifier) > -1)
                    if(this.max < this.files.length + isCompletes.length){
                        return false
                    }
                }
                return true
            // }
        },
    },
    data () {
        return {
            uploadingFileCurrent: null,
            completedFiles: [],//已经上传完成的文件编号
            completedFilesDels: [],//已经上传完成，但是被删除的文件编号列表
            // existasTotalTmp: 0,
            delIdx: -1,
            files: [],
            uploading: false,
            fileAnalysisLoading: false, //大文件分析loading（大文件计算md5值）
            fileProp: {
              fileKey: 'Id',
              filePath: 'Path',
              fileName: 'FileName',
            },
            options: {
                categoryMap: {
                    image: ['gif', 'jpg', 'jpeg', 'png', 'bmp', 'webp'],
                    video: ['mp4', 'm3u8', 'rmvb', 'avi', 'swf', '3gp', 'mkv', 'flv'],
                    audio: ['mp3', 'wav', 'wma', 'ogg', 'aac', 'flac'],
                    document: ['doc', 'txt', 'docx', 'pages', 'epub', 'pdf', 'numbers', 'csv', 'xls', 'xlsx', 'keynote', 'ppt', 'pptx']
                },
                singleFile: false,
                fileParameterName: 'file',
                target: `/api${serviceArea.resource}/FileUpload/UploadChunk?t=` + Date.now(),
                testChunks: true,
                headers: {
                    'Token': getToken(),
                },
                query: function(file, chunk, isTestMode) { //为上传切片接口添加额外的参数
                    return {
                        // id: file.id,
                        type: file.fileType
                    }
                },
                // allowDuplicateUploads: true,//如果说一个文件以及上传过了是否还允许再次上传
                simultaneousUploads: 10,//并发上传数量
                chunkSize: 100 * 1024, // 100kb
                // 服务器分片校验函数，秒传及断点续传基础
                checkChunkUploadedByResponse: function (chunk, message) {
                    let objMessage = JSON.parse(message);
                    if (objMessage.SkipUpload) {
                        return true;
                    }
                    return (objMessage.Uploadeds || []).indexOf(chunk.offset + 1) >= 0
                },
                //切片上传成功后回调
                processResponse: (response, cb) => {
                    cb(null, response) //默认的，继续上传


// fileName: file.name, xxxx
// identifier: firstArg.uniqueIdentifier, xxxxx
// totalSize: file.size, xxxxxx
// type: file.type,

// chunkNumber: 53
// chunkSize: 102400
// currentChunkSize: 102400
// totalSize: 37764281
// identifier: 50d99695b2afd9870027105eaac1b339
// filename: b.mp4
// relativePath: b.mp4
// totalChunks: 368
// type: video/mp4
                    // this.statusText.success = '合并中'

                    // if(response.NeedMerge){ //上传完成，需要合并了
                    //     this.mergeChunks().then(err => {
                    //         //合并成功（业务代码）
                    //         cb(null, response) //默认的，继续上传
                    //     }).catch(err => {
                    //         cb(err, response) //合并失败
                    //     })
                    // }else{
                    //     cb(null, response) //默认的，继续上传
                    // }
                },
                //生成文件唯一标示的函数
                // generateUniqueIdentifier: function(file) {
                //     return file.name + 'xxxx'
                // }
            },
            attrs: {
                accept: []
            },
            statusText: {
                success: '',//上传成功
                error: '出错了',
                uploading: '上传中',
                paused: '暂停中',
                waiting: '等待中'
            },
            showUploadProgress: true,
            //uploader 控件中包含的文件，用于监听每个文件的状态，供外部根据状态做业务判断
            totalSize: 0, //上传的附件总大小（kb）
        }
    },
    computed: {
        showUploadBtn () {
            return (this.files.length < this.max) && !this.readonly
        },
        uploader() {
            return this.$refs.uploader.uploader;
        },
    },
    mounted () {

        this.attrs.accept = this.accept.split(',')
        this.initFiles(this.value)
    },
    watch: {
        value: {
            handler (newval) {
                this.initFiles(newval)
            },
            deep: true
        },
    },
    methods: {
        initFiles (data) {
            let self = this;
            this.files = data || []
            // if (_.isString(data)) {
            //     this.files = data.split(',').map(v => ({
            //         [self.fileProp.fileKey]: v.Id,
            //         [self.fileProp.filePath]: v.Path
            //     }))
            // } else {
            // }

            //计算已存在文件总体积
            if(this.files && this.files.length > 0){
                this.totalSize = (this.files.map(f => f.FileSize) || [0]).reduce(function(prev,cur,index,array){
                    return prev + cur
                }) //byte
            }
        },
        removeFile (index) {
            if(this.limitTotalSize > 0) {
                this.totalSize -= this.files[index].FileSize
                if(this.totalSize <= 0){
                    this.totalSize = 0
                }
            }

            let delFileId = this.files[index].FileMd5value
            if(this.completedFilesDels.indexOf(delFileId) == -1){
                this.completedFilesDels.push(delFileId)
            }

            this.files.splice(index, 1)
            this.existasTotalTmp = this.files.length
            this.filesChange()
        },
        filesChange () {
            this.$emit("change", this.files)
        },
        conver: sizeUnitConversion,
        
        // conver(limit) {
        //     var size = "";
        //     if (limit < 0.1 * 1024) { //如果小于0.1KB转化成B
        //         size = limit.toFixed(2) + "B";
        //     } else if (limit < 0.1 * 1024 * 1024) {//如果小于0.1MB转化成KB
        //         size = (limit / 1024).toFixed(2) + "KB";
        //     } else if (limit < 1 * 1024 * 1024 * 1024) { //如果小于1GB转化成MB
        //         size = (limit / (1024 * 1024)).toFixed(2) + "MB";
        //     } else { //其他转化成GB
        //         size = (limit / (1024 * 1024 * 1024)).toFixed(2) + "GB";
        //     }

        //     var sizestr = size + "";
        //     var len = sizestr.indexOf("\.");
        //     var dec = sizestr.substr(len + 1, 2);
        //     if (dec == "00") {//当小数点后为00时 去掉小数部分
        //         return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
        //     }
        //     return sizestr;
        // },

        //如果不符合条件，忽略（不会加入到 onFilesAdded 事件参数中）
        onFileAdded(file) {
            // if(!this.beforeUpload(file)){
            //     file.ignored = true
            //     return false
            // }
        },
        //上传之前的事件
        beforeUpload (file) {
            let acceptStr = this.attrs.accept || ''
            let fileSize = this.fileSize //单文件大小限制
            // let limitTotalSize = this.limitTotalSize //总大小限制

            let accept = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase(),
                typePass = ~acceptStr.indexOf(accept) || !acceptStr || acceptStr == 'all',
                sizePass = file.size < fileSize || !fileSize
                // maxLimit = this.files.length < this.max;
                // totalSizeLimit = !(limitTotalSize > 0) ? true : limitTotalSize >= (file.size + this.totalSize)

            let limitNotice = this.conver(fileSize)
            // let totalSizeLimitNotice = this.conver(limitTotalSize)

            !typePass && this.$message.error(`上传文件只能是 ${acceptStr} 格式!`);
            !sizePass && this.$message.error(`上传文件大小不能超过 ${limitNotice}!`);
            // !totalSizeLimit && this.$message.error(`上传文件总大小不能超过 ${totalSizeLimitNotice}!`);

            // !maxLimit && this.$message.error(`上传文件不能超过 ${this.max} 张!`)
            if (typePass && sizePass) {
                this.uploading = true
            }
            return typePass && sizePass;
        },
        onFilesAdded(files, fileList) {
            let isValidate = true //单个文件校验是否通过
            if(files && files.length > 0){
                files.forEach(file => {
                    if(!this.beforeUpload(file) && isValidate){
                        files.ignored = true
                        isValidate = false
                        return false
                    }
                })
            }

            if(!isValidate){
                return
            }

            //文件总数量限制校验
            if(this.max < this.files.length + files.length){
                this.$message.warning(`不能超过${this.max}个文件！`)
                files.ignored = true
                return
            }

            //文件总提交大小校验
            let limitTotalSize = this.limitTotalSize //总大小限制
            if(limitTotalSize > 0) {  //如果有总文件大小限制
                //当前文件列表总大小
                let currFilesSize = (files.map(f => f.size) || [0]).reduce(function(prev,cur,index,array){
                    return prev + cur
                }) //byte

                if(limitTotalSize >= this.totalSize + currFilesSize){ //没有超过大小限制
                    this.totalSize += currFilesSize
                    this.computeMD5(files)
                }else{
                    files.ignored = true
                    let totalSizeLimitNotice = this.conver(limitTotalSize)
                    this.$message.error(`上传文件总大小不能超过 ${totalSizeLimitNotice}!`);
                }
            }else{
                this.computeMD5(files)
            }
        },
        //上传中
        onFileProgress(rootFile, file, chunk) {
            this.uploadingFileCurrent = file

            // console.log(`上传中 ${file.name}，chunk：${chunk.startByte / 1024 / 1024} ~ ${chunk.endByte / 1024 / 1024}`)
        },
        onFileRemoved(file) {
            if(this.limitTotalSize > 0) {
                //删除上传日志时（进度条），判断当前文件是否是上传成功的文件。如果只是删除日志，则不减少（已上传）文件总大小。
                let idx = this.files.findIndex(f => f.FileMd5value == file.uniqueIdentifier)
                if(idx > -1){
                    return
                }

                this.totalSize -= file.size
                if(this.totalSize <= 0){
                    this.totalSize = 0
                }
            }
        },
        fileComplete() {
            const firstArg = arguments[0]
            const file = firstArg.file

            this.statusSet(firstArg.id, 'merging')
            common.mergeChunks({
                fileName: file.name,
                identifier: firstArg.uniqueIdentifier,
                totalSize: file.size,
                type: file.type,
                // fileType: 4
            }, {
                timeout: 300 * 1000
            }).then(res => {
                this.files = this.files.concat(res)
                this.completedFiles.push(firstArg.uniqueIdentifier)

                let delFileIdx = this.completedFilesDels.findIndex(s => s == firstArg.uniqueIdentifier)
                if(delFileIdx != -1) {
                    this.completedFilesDels.splice(delFileIdx, 1)
                }
                this.onFileRemoved(file)
                this.filesChange()
                this.statusSet(firstArg.id, 'merged')
            }).catch(err => {
                this.statusSet(firstArg.id, 'mergeFailed')
            })
        },
        onFileError(rootFile, file, response, chunk) {
            this.$message({
                message: response,
                type: 'error'
            })
        },
        /**
         * 计算md5，实现断点续传及秒传
         * @param file
         */
        computeMD5(files) {
            let flag = 0

            if(files && files.length > 0) {
                this.fileAnalysisLoading = true

                // let chunkSize = this.options.chunkSize;
                let chunkSize = 1024 * 1024 * 0.5; //不需要根据分片设置切片的大小（主要考虑计算耗时——分片多，计算少，分片大，计算可能会卡死（IE））
                let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice

                files.forEach(file => {
                    let fileReader = new FileReader();
                    let spark = new SparkMD5.ArrayBuffer();
                    let currentChunk = 0;
                    let chunks = Math.ceil(file.size / chunkSize);

                    // file.name += ' (文件分析中...)'
                    file.pause();

                    fileReader.onload = (e => {
                        spark.append(e.target.result); // Append array buffer
                        currentChunk++;
                        //使用文件首尾两个分片计算md5
                        // if(currentChunk == 0){
                        //     currentChunk = chunks - 1
                        // }else{
                        //     currentChunk++
                        // }

                        if (currentChunk < chunks) {
                            loadNext();
                        } else {
                            file.uniqueIdentifier = spark.end(); // Compute hash
                            // file.name = file.name.substr(0, file.name.lastIndexOf(' (文件分析中...)'))

                            //所选所有文件md5计算完毕，结束loading
                            flag++
                            if(flag == files.length){
                                this.fileAnalysisLoading = false
                                this.validDuplicate(files)
                            }
                        }
                    });

                    fileReader.onerror = function () {
                        console.log('FileReader onerror was triggered, maybe the browser aborted due to high memory usage.');
                    };

                    function loadNext() {
                        var start = currentChunk * chunkSize,
                            end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize;
                        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end));
                    }
                    loadNext();
                })
            }
        },
        //判断是否有重复文件
        validDuplicate(files) {
            let newIds = files.map(f => f.uniqueIdentifier) //最新选择的文件
            let oldIds = this.value ? this.value.map(f => f.FileMd5value) : [] //已上传成功的文件
            let newIdsDup = (new Set(newIds)).size != newIds.length //新上传文件是否存在重复文件（true：重复；false：不重复）
            //新上传文件和已上传文件是否存在重复（当前上传的和已上传成功的比较）
            let newAndOldidsDup = _.intersection(newIds, oldIds).length > 0
            //也需要和未上传成功的，但是已经加入到上传列表中的文件比较
            if(!newAndOldidsDup){
                //需要排除已上传成功的，但是被删除的，并且在上传进度面板中还存在的
                let listOfProgressPanel = this.uploader.fileList.filter(s => this.completedFilesDels.indexOf(s.uniqueIdentifier) == -1)
                let group = _.groupBy(listOfProgressPanel, 'uniqueIdentifier');
                for(var g in group){
                    if(newAndOldidsDup){
                        return false
                    }
                    newAndOldidsDup = group[g].length >= 2
                }
            }
            //重复文件错误提示
            let dupMsg = newIdsDup ? '选中的文件中包含重复文件，请勿上传重复附件' : newAndOldidsDup ? '所选文件和已上传文件存在重复，请重新选择' : ''

            //判断选中的文件中是否有重复的
            if(newIdsDup || newAndOldidsDup){
                this.$message({
                    message: dupMsg,
                    type: 'error'
                })
                //此时重复文件已经加入到ui中，需要手动删除(无法用files.ignored = true来忽略)
                files.forEach(f => {
                    let fileObj = files.find(file => file.id == f.id)
                    this.uploader.removeFile(fileObj)
                })
            }else{
                //所有文件md5计算完毕，循环启动文件开始上传
                files.forEach(f => {
                    setTimeout(() => {
                        f.resume();
                    }, 100)
                })
            }
        },
        /**
         * 新增的自定义的状态: 'md5'、'transcoding'、'failed'
         * @param id
         * @param status
         */
        statusSet(id, status) {
            let statusMap = {
                merging: {
                    text: '处理中',
                },
                merged: {
                    text: '上传成功',
                },
                mergeFailed: {
                    text: '处理失败',
                }
            }
            this.$nextTick(() => {
                $(`.file_${id} .uploader-file-status span:first`).text(statusMap[status].text)
            })
        },
        statusRemove(id) {
            this.$nextTick(() => {
                $(`.myStatus_${id}`).remove();
            })
        },
        handleDownload(url, filename,o) {
          //   url=`${serviceArea.resource}/FileUpload/Download?id=${o.Id}`
          //  if(!url){
          //       this.$message({
          //           message: '下载地址错误',
          //           type: 'error'
          //       })
          //       return
          //   }
          //   setTimeout(() => {
          //       downloadFile(url, filename)
          //   }, 1000)

            // 第二种下载方式，使用文件流下载
            var dowFileUrl=`api${serviceArea.resource}/FileUpload/Download?id=${o.Id}`
            window.open(dowFileUrl);

            // var instance = axios.create({
            //   baseURL: 'api',
            //   timeout: 300000,
            //   headers: {'Token': getToken()}
            // });

            // instance.get(dowFileUrl, {
            //     responseType:'blob'
            // }).then(res => {
            //     let blob = new Blob([res.data],{
            //               type:res.data.type    //将会被放入到blob中的数组内容的MIME类型
            //             });
            //     let objectUrl = URL.createObjectURL(blob);  //生成一个url
            //     let a = document.createElement("a");
            //     a.href =objectUrl
            //     a.download = res.headers.filename
            //     a.click();
            // });
        },
    }
}
</script>

<style scoped>

/**/

.file-list {
    display: inline-block;
    margin-right: 10px;
    vertical-align: middle;
    line-height: 1;
    list-style: none;
    padding-left: 0;

    box-sizing: border-box;
    margin: 0!important;
}

.file-list li {
    float: left;
    position: relative;
    margin-right: 10px;
}

.file-item {
    display: inline-block;
    position: relative;
    width: 120px;
    height: 120px;
}

.file-item-img{
    cursor: pointer;
}
.file-item-other{
    cursor: auto;
    background-color: #efefef;
}
.file-logo{
    font-size: 40px;
    text-align: center;
    position: absolute;
    margin-top: -20px;
    top: 50%;
    margin-left: -20px;
    left: 50%;
}

.file-img {
    display: inline-block;
    padding: 2px;
    border-radius: 4px;
    vertical-align: middle;
    border-width: 1px;
    border-style: dashed;
}

.file-btn-del {
    background: transparent;
    display: block;
    cursor: pointer;
    position: absolute;
    font-size: 18px;
    top: -6px;
    right: -6px;
}

.file-btn-del:hover {
    transition: all 0.3s;
    color: red;
}

.upload-btn {
    position: relative;
    vertical-align: middle;
}

.icon-btn {
    width: 120px;
    height: 120px;
    padding: 0;
    font-size: 0;
    background: none;
    border-style: dashed;
    text-align: center;
}

.icon-plus {
    font-size: 24px;
    color: #8c939d;
}


/*上传组件相关*/
.uploader{
    width: 100%;
}

.uploader-drop{
    box-sizing: border-box;
}

.file-list{
    list-style-type: none;
    padding-left: 0;
}

.no-file{
    text-align: center;
}

.file-info-warpper{
    position: relative;

}

.file-name{
    padding: 7px 0;
    padding-right: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-button{
    position: absolute;
    right: 0;
    top: 0;
}

</style>
