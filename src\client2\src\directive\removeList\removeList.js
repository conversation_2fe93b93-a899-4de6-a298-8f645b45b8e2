// directive.js （和main.js同级）
// import Vue from 'vue'


// el-select 多选时，需要隐藏某些 tag 的删除按钮
export default{
  componentUpdated(el, bindings) {
    //mapData 当前select的value值
    //selData 禁用的id集合
    const [mapData, selData] = bindings.value 
    const disableIndex = []
    
    selData.forEach((selItem, index) => {
        const selObj = mapData.find(mapItem => mapItem.EmployeeId === selItem)
        if (selObj) disableIndex.push(index)
    })

    setTimeout(() => { // bind时，找不到 tags，所以放个延时定时器
        const tags = el.querySelectorAll('.el-tag__close')
        tags.forEach((tag, index) => {
            if (disableIndex.includes(index)) {
                tag.style.display = 'none' // close 图标隐藏掉
                // tag.classList.add('none')
            }
        })
    })
  }
}


