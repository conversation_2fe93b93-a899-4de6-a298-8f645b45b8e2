import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/Folder/'

export function getAllFolders(data) {
  return request({
    url: busServiceAreaName + 'GetListByConditionAsync',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: busServiceAreaName + 'Edit',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: busServiceAreaName + 'Delete',
    method: 'post',
    data
  })
}

export function addAttas(data) {
  return request({
    url: busServiceAreaName + 'SaveAttachmentAsync',
    method: 'post',
    data
  })
}

export function getAttas(data) {
  return request({
    url: busServiceAreaName + 'GetAttachmentAsync',
    method: 'post',
    data
  })
}

//删除文件
export function delAttas(data) {
  return request({
    url: busServiceAreaName + 'DeleteFileAsync',
    method: 'post',
    data
  })
}

export function move(data) {
  return request({
    url: busServiceAreaName + 'MoveFileAsync',
    method: 'post',
    data
  })
}

export function checkDup(data) {
  return request({
    url: busServiceAreaName + 'CheckFileExistAsync',
    method: 'post',
    data
  })
}