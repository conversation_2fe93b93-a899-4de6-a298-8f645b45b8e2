import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + '/ProjectManagementVersionPlan/'
    //获取首页列表
export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

//新增编辑
export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

//获取详情
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

//发布
export function publish(data) {
    return request({
        url: serviceAreaName + 'PublishVersion',
        method: 'post',
        data
    })
}

//删除
export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//启动
export function start(params) {
    return request({
        url: serviceAreaName + 'StartIterationPlan',
        method: 'post',
        params
    })
}

//完成
export function finish(params) {
    return request({
        url: serviceAreaName + 'FinishIterationPlan',
        method: 'post',
        params
    })
}

//获取项目版本及迭代信息
export function getVersions(params) {
    return request({
        url: serviceAreaName + 'GetVersionPlanList',
        method: 'get',
        params
    })
}