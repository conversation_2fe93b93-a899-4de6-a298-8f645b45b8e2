
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/SystemNotice/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function recall(params) {
    return request({
        url: serviceAreaName + 'Recall',
        method: 'get',
        params
    })
}



export function getComment(params) {
    return request({
        url: serviceAreaName + 'GetCommentAsync',
        method: 'get',
        params
    })
}


export function addComment(data) {
    return request({
        url: serviceAreaName + 'AddCommentAsync',
        method: 'post',
        data
    })
}


export function setTop(data) {
    return request({
        url: serviceAreaName + 'Top',
        method: 'post',
        data
    })
}