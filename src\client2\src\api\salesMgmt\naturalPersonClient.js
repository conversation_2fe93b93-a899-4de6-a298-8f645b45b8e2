import request from "@/utils/request";
import { serviceArea } from "../serviceArea";

let serviceAreaName = serviceArea.business;

export function createApi(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/Add",
    method: "post",
    data,
  });
}
export function getClientListApi(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/GetList",
    method: "post",
    data,
  });
}
export function deleteClientApi(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/Delete",
    method: "post",
    data,
  });
}
export function getClientDetailApi(params) {
  return request({
    url: serviceAreaName + "/CustomerPro/GetDetail",
    method: "get",
    params,
  });
}

export function getProductListApi(data) {
  return request({
    url: serviceAreaName + "/Product/GetListPage",
    method: "post",
    data,
  });
}
export function getClientListOptApi(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/GetSelectNameAvatarPhoneAge",
    method: "post",
    data,
  });
}
export function getSelectNameAvatar(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/GetSelectNameAvatar",
    method: "post",
    data,
  });
}
export function setFollowApi(params) {
  return request({
    url: serviceAreaName + "/CustomerPro/SetFollow",
    method: "get",
    params,
  });
}
export function getUnitListApi(data) {
  return request({
    url: serviceAreaName + "/CorporateClient/GetListPage",
    method: "post",
    data,
  });
}
export function getDepartmentListApi(data) {
  return request({
    url: serviceAreaName + "/CorporateClient/GetDepartmentListPage",
    method: "post",
    data,
  });
}
export function editClientApi(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/Edit",
    method: "post",
    data,
  });
}
export function getTagTreeApi(data) {
  return request({
    url: serviceAreaName + "/CustomerTagClassify/GetListPage",
    method: "post",
    data,
  });
}
export function getTagListApi(data) {
  return request({
    url: serviceAreaName + "/CustomerTag/GetListPage",
    method: "post",
    data,
  });
}
export function getActiveHistoryList(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/GetSalesActivitysHistoryList",
    method: "post",
    data,
  });
}
export function getAllOrganizationalApi(params) {
  return request({
    url: serviceAreaName + "/CorporateClient/GetAllOrganizational",
    method: "get",
    params,
  });
}
export function RelationshipDiagramByCustomerPro(params) {
  return request({
    url: serviceAreaName + "/CustomerPro/GetRelationshipDiagram",
    method: "get",
    params,
  });
}
export function personnelChangeApi(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/PersonnelChange",
    method: "post",
    data,
  });
}


export function getUnitHistoryList(data) {
  return request({
    url: serviceAreaName + "/CustomerPro/GetUnitHistoryList",
    method: "post",
    data,
  });
}




