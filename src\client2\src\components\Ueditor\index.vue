<template>
  <div style="overflow-x: hidden; line-height: auto;">
    <script :id="id" type="text/plain"></script>
  </div>
</template>
<script>
  import './leipiFormDesign'
  export default {
    name: 'Ueditor',
    data() {
      return {
        editor: null,
        fields: 0,
        editorContent: this.content
      }
    },
    props: {
      content: {
        type: String
      },
      id: {
        type: String,
        default: 'editor'
      },
      config: {
        type: Object
      }
    },
    mounted() {
      var _this = this
      if (window[`ue_${this.id}`] !== undefined) {
        window[`ue_${this.id}`].destroy()
      }
      // this.editor = window.UE.getEditor('editor', this.config) // 初始化UE
      // 表单设计器
      _this.editor = window.UE.getEditor(this.id,
        {
          toolleipi: true, // 是否显示，设计器的 toolbars
          textarea: 'design_content',
          // 这里可以选择自己需要的工具按钮名称,此处仅选择如下五个
          // toolbars: [[
          //   'fullscreen', 'source',
          //   '|', 'undo', 'redo',
          //   '|', 'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'removeformat',
          //   '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist',
          //   '|', 'fontfamily', 'fontsize',
          //   '|', 'indent',
          //   '|', 'imagenone',
          //   '|', 'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', 'horizontal',
          //   '|', 'inserttable', 'deletetable', 'mergecells', 'splittocells']],
          toolbars: [[
            'fullscreen', 'source', '|', 'undo', 'redo', '|',
            'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
            'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
            'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
            'directionalityltr', 'directionalityrtl', 'indent', '|',
            'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',
            'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
            'simpleupload', 'insertimage', 'emotion', 'scrawl', 'insertvideo', 'music', 'attachment', 'map', 'gmap', 'insertframe', 'insertcode', 'webapp', 'pagebreak', 'template', 'background', '|',
            'horizontal', 'date', 'time', 'spechars', 'snapscreen', 'wordimage', '|',
            'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',
            'print', 'preview', 'searchreplace', 'help', 'drafts'
          ]],
          wordCount: false,
          elementPathEnabled: false,
          autoHeightEnabled: true,
          initialFrameHeight: 400,
        })
      window[`ue_${this.id}`] = _this.editor
      _this.editor.addListener('ready', function() {
        _this.$emit('ready') // 通知父节点编辑器准备完毕
      })
    },
    watch: {
      content: function(val, oldVal) {
        if (val !== undefined && val !== '') {
          if(window && window[`ue_${this.id}`]){
            window[`ue_${this.id}`].setContent(val)
          }
        }
      }
    },
    methods: {
      getObj() { // 获取内容方法
        var content = this.editor.getContent()
        return window.leipiFormDesign.parse_form(content, this.fields)
      },
      destroyed() {
        this.editor.destroy()
      }
    },
    // destroyed() {
    //   this.editor.destroy()
    // }
  }
</script>

<style>
/* 修复 UEditor 样式被冲突，导致按钮高度被拉长 */
.edui-button-body,
.edui-menubutton-body,
.edui-splitbutton-body,
.edui-combox-body{
  line-height: 20px!important;
}
</style>
