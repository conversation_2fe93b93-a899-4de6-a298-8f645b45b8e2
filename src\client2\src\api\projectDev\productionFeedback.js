
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/ProductionFeedback/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'ListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function handler(data) {
    return request({
        url: serviceAreaName + 'Handler',
        method: 'post',
        data
    })
}

export function addDemand(data) {
    return request({
        url: serviceAreaName + 'AddDemand',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetail',
        method: 'get',
        params
    })
}

export function del(params) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'get',
        params
    })
}

export function setTopOrder(params) {
    return request({
        url: serviceAreaName + 'SetTopOrder',
        method: 'get',
        params
    })
}

export function cancelTopOrder(params) {
    return request({
        url: serviceAreaName + 'CancelTopOrder',
        method: 'get',
        params
    })
}