import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
let serviceAreaUser = serviceArea.user

export function getList(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/SearchTaskManagements',
        method: 'post',
        data
    })
}

//查看主任务详情
export function detail(params) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetTaskManagement',
        method: 'get',
        params
    })
}

//获取子任务列表
export function getSubTasks(params) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetSubTaskManagements',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/AddTaskManagement',
        method: 'post',
        data
    })
}


export function addSubTask(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/AddSubTaskManagement',
        method: 'post',
        data
    })
}

//修改主任务、子任务（公用）
export function editTask(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/UpdateTaskManagement',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/DeleteTaskManagement',
        method: 'post',
        data
    })
}

export function getFeedbacks(params) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetTaskManagementCommentsPage',
        method: 'get',
        params
    })
}

export function comment(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/AddTaskManagementComment',
        method: 'post',
        data
    })
}

export function getOpts(params) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetTaskManagementLogs',
        method: 'get',
        params
    })
}

export function sendMail(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/EmailNotificationTaskManagement',
        method: 'post',
        data
    })
}

export function collection(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/FaoriteTaskManagement',
        method: 'post',
        data
    })
}

export function exportFile(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/Export',
        method: 'post',
        data
    })
}


//任务概况
export function getTaskSituation(data, token) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetTaskGeneralSituation',
        method: 'post',
        data,
        token
    })
}

//任务概况——部门
export function getTaskSituationOfDept(data, token) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetDepartmentTaskGeneralSituation',
        method: 'post',
        data,
        token
    })
}

export function audit(data) {
    return request({
        url: serviceAreaName + '/TaskManagement/ApprovalTask',
        method: 'post',
        data
    })
}

export function isUsed(params) {
    return request({
        url: serviceAreaName + '/TaskManagement/IsExitPredecessorTask',
        method: 'get',
        params
    })
}

//新版甘特图接口
export function getSituationDatas(data, token) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetTaskGeneralSituationListAsync',
        method: 'post',
        data,
        token
    })
}

//新版优化后的甘特图接口
export function GetTaskGeneralSituationDataAsync(data, token) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetTaskGeneralSituationDataAsync',
        method: 'post',
        data,
        token,
        config: {
            timeout: 20 * 1000
        }
    })
}



export function getPieDatas(data, token) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetTaskGeneralSituationPieChartAsync',
        method: 'post',
        data,
        token
    })
}

// 生产任务打卡申诉详情
export function getAppealDetails(params){
  return request({
      url: serviceAreaUser + '/TimecardProduction/GetAppealDetails',
      method: 'get',
      params
  })
}
// 生产任务打卡申诉审批
export function approvalApi(data){
  return request({
      url: serviceAreaUser + '/TimecardProduction/Approval',
      method: 'post',
      data
  })
}