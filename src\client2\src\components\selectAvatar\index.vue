<template>
    <el-row class="selectAvatar_Wrapper">
        <el-card shadow="hover" :body-style="{ padding: '0' }" v-for="p in 32" :key="p"
        class="selectAvatar_Wrapper_card" :class="{'active': isSelect(p)}">
            <img class="selectAvatar_Wrapper_card_img" :src="require(`../../assets/projectAvatar/${p}.png`)" @click="changeRow(p)" />
            <div class="selectAvatar_Wrapper_card_Mode" @click="changeRow(p)"><i class="el-icon-success"></i></div>
        </el-card>
    </el-row>
</template>

<script>

export default {
    name: "select-avatar",
    components: {
    },
    props: {
        value: {
            type: [String, Array], // 单选时传 string   多选时 数组
            default: null
        },
        multable: { // 是否多选
            type: Boolean,
            default: false,
        },
        multableNum: { // 多选时  限制的数量
            type: Number,
            default: 10
        }
    },
    data() {
        return {
            selectData: this.value,
        };
    },
    watch: {
    },
    mounted() {
    },
    methods: {
        // 当前图标是否被选中
        isSelect(index){
            let rowName = `${index}.png`;
            if (this.multable) {
                return this.selectData.some(s=> s === rowName);
            } else {
                return this.selectData == rowName
            }
        },
        changeRow(index){
            let rowName = `${index}.png`;
            if (this.multable) {
                if (this.selectData.some(s=> s === rowName)) {
                    this.selectData.splice(this.selectData.findIndex(s=>s===rowName), 1);
                } else {
                    if (this.selectData.length>=this.multableNum) {
                        this.$message.error(`选择数量不能超过 ${this.multableNum} 个!`)
                    } else {
                        this.selectData.push(rowName);
                    }
                }
            } else {
                this.selectData = this.selectData == rowName ? '' : rowName;
            }
            this.$emit('input', this.selectData)
        },
    }
};
</script>

<style lang="scss" scoped>
.selectAvatar_Wrapper{
    width: 100%;
    height: 100%;
    overflow: hidden;
    overflow-y: auto;
    &_card{
        float: left;
        position: relative;
        width: 60px;
        height: 60px;
        margin-right: 10px;
        margin-bottom: 10px;
        &_img{
            cursor: pointer;
            float: left;
            width: 100%;
            height: 100%;
            opacity: 1;
        }
        &_Mode{
            cursor: pointer;
            position: absolute;
            width: 100%;
            height: 100%;
            display: none;
            & [class*='el-icon']{
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%,- 50%);
                color: #67c23a;
                font-size: 24px;
            }
        }
        &.active{
            .selectAvatar_Wrapper_card_img{
                opacity: 0.3;
            }
            .selectAvatar_Wrapper_card_Mode{
                display: block;
            }
        }
    }
}
</style>
