import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

//新增故障案例库
export function addFailureCase(data) {
  return request({
    url: serviceAreaName + '/FailureCase/AddFailureCase',
    method: 'post',
    data
  })
}

//编辑故障案例库
export function editFailureCase(data) {
  return request({
    url: serviceAreaName + '/FailureCase/EditFailureCase',
    method: 'post',
    data
  })
}

//故障案例库分页列表（分页）
export function getFailureCaseListPage(data) {
  return request({
    url: serviceAreaName + '/FailureCase/GetFailureCaseListPage',
    method: 'post',
    data
  })
}

//故障案例原因分析（无分页）
export function getFailureAnalysises(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetFailureAnalysises',
    method: 'get',
    params
  })
}

//故障案例解决方法（无分页）
export function getFailureSolutions(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetFailureSolutions',
    method: 'get',
    params
  })
}

//故障案例库智能搜索（无分页）
export function getFailureCaseIntelligentSearch(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetFailureCaseIntelligentSearch',
    method: 'get',
    params
  })
}

//所有故障案例（无分页）
export function getFailureCaseList(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetFailureCaseList',
    method: 'get',
    params
  })
}

//故障案例详情
export function getFailureCaseDetail(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetFailureCaseDetail',
    method: 'get',
    params
  })
}

//故障案例完整详情
export function getCompleteFailureCaseDetail(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetCompleteFailureCaseDetail',
    method: 'get',
    params
  })
}

//验证原因分析
export function ValidateFailureAnalysis(data) {
  return request({
    url: serviceAreaName + '/FailureCase/ValidateFailureAnalysis',
    method: 'post',
    data
  })
}

//验证解决方案
export function validateFailureSolution(data) {
  return request({
    url: serviceAreaName + '/FailureCase/ValidateFailureSolution',
    method: 'post',
    data
  })
}

export function addFailure(data) {
  return request({
    url: serviceAreaName + '/FailureCase/Add',
    method: 'post',
    data
  })
}

export function getListPage(data) {
  return request({
    url: serviceAreaName + '/FailureCase/GetListPage',
    method: 'post',
    data
  })
}
export function del(data) {
  return request({
    url: serviceAreaName + '/FailureCase/Delete',
    method: 'post',
    data
  })
}

export function getDetails(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetDetails',
    method: 'get',
    params
  })
}


export function editFailure(data) {
  return request({
    url: serviceAreaName + '/FailureCase/Edit',
    method: 'post',
    data
  })
}
export function getListByCondition(data) {
  return request({
    url: serviceAreaName + '/FailureCase/GetListByCondition',
    method: 'post',
    data
  })
}

export function getSubitem(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetSubitem',
    method: 'get',
    params
  })
}


export function caseCreate(data) {
  return request({
    url: serviceAreaName + '/ApplicationFailureCase/Create',
    method: 'post',
    data
  })
}

export function approval(data) {
  return request({
    url: serviceAreaName + '/ApplicationFailureCase/Approval',
    method: 'post',
    data
  })
}

export function licationGetDetails(params) {
  return request({
    url: serviceAreaName + '/ApplicationFailureCase/GetDetails',
    method: 'get',
    params
  })
}


export function getApplicationCaseList(data) {
  return request({
    url: serviceAreaName + '/ApplicationFailureCase/GetApplicationCaseList',
    method: 'post',
    data
  })
}

export function neglect(data) {
  return request({
    url: serviceAreaName + '/ApplicationFailureCase/Neglect',
    method: 'post',
    data
  })
}

export function getCode(params) {
  return request({
    url: serviceAreaName + '/FailureCase/GetCode',
    method: 'get',
    params
  })
}

export function batchEdit(data) {
  return request({
    url: serviceAreaName + '/FailureCase/BatchEdit',
    method: 'post',
    data
  })
}
