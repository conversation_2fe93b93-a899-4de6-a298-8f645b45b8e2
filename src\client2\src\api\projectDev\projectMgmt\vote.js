import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/Vote/'

export function add(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function del(data) {
    return request({
      url: busServiceAreaName + 'Delete',
      method: 'post',
      data
    })
}

export function edit(data) {
    return request({
      url: busServiceAreaName + 'Edit',
      method: 'post',
      data
    })
}


export function getList(data) {
    return request({
      url: busServiceAreaName + 'GetListPage',
      method: 'post',
      data
    })
}

export function detail(params) {
    return request({
      url: busServiceAreaName + 'GetDetails',
      method: 'get',
      params
    })
}



export function getListByCondition(data) {
    return request({
      url: busServiceAreaName + 'GetListByCondition',
      method: 'post',
      data
    })
}


export function getTotalDetails(params) {
    return request({
      url: busServiceAreaName + 'GetTotalDetailsAsync',
      method: 'get',
      params
    })
}

export function saveVote(data) {
    return request({
      url: busServiceAreaName + 'SaveVoteAsync',
      method: 'post',
      data
    })
}






