import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business

/**列表 */
export function getTaskList(data) {
  data = data || {};
  return request({
    url: busServiceAreaName + '/TaskDetail/GetListPage',
    method: 'post',
    data
  })
}

/**新增 */
export function addTask(data) {
  return request({
    url: busServiceAreaName + '/TaskDetail/Add',
    method: 'post',
    data
  })
}

/**编辑 */
export function editTask(data) {
  return request({
    url: busServiceAreaName + '/TaskDetail/Edit',
    method: 'post',
    data
  })
}

/**删除 */
export function deleteTask(data) {
  return request({
    url: busServiceAreaName + '/TaskDetail/Delete',
    method: 'post',
    data
  })
}

// export function planDelTask(data) {
//   return request({
//     url: busServiceAreaName + '/TaskDetail/PlanDelTask',
//     method: 'post',
//     data
//   })
// }



/**详情 */
export function getDetail(params) {
  return request({
    url: busServiceAreaName + '/TaskDetail/GetDetails',
    method: 'get',
    params
  })
}

/**指派 */
export function assign(data) {
  return request({
    url: busServiceAreaName + '/TaskDetail/Assign',
    method: 'post',
    data
  })
}

/**更新状态 */
export function updateStatus(data) {
  return request({
    url: busServiceAreaName + '/TaskDetail/UpdateStatus',
    method: 'post',
    data
  })
}

/**任务汇报，更新进度 */
export function updateProgress(data) {
  return request({
    url: busServiceAreaName + '/TaskDetail/UpdateProgress',
    method: 'post',
    data
  })
}

