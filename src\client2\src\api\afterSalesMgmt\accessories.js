import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/StructPart/'
let serviceSPSArea = serviceArea.business + '/StructPartsSpecification/'
let serviceSupplierArea = serviceArea.business + '/Supplier/'
let serviceSPTArea = serviceArea.business + '/StructPartTemplate/'
let serviceTAreaComment = serviceArea.business + '/Comment/'

//配件更换记录接口
export function getAllComment(data){
    return request({
        url: serviceTAreaComment + 'GetAllComment',
        method: 'post',
        data
    })
}
//供应商
export function getListByCondition(params){
    return request({
        url: serviceSupplierArea + 'GetListByCondition',
        method: 'get',
        params
    })
}
//结构配件增删改查
export function addStructural(data){
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}


export function getStructuralList(data){
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}


export function editStructural(data){
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detailStructural(params){
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}
// export function getBusinessRelation(params){
//     return request({
//         url: serviceAreaName + 'GetBusinessRelation',
//         method: 'get',
//         params
//     })
// }
export function getBusinessRelationPage(data){
    return request({
        url: serviceAreaName + 'GetBusinessRelationPage',
        method: 'post',
        data
    })
}


export function delStructural(data){
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//规格型号增删改查
export function addPart(data){
    return request({
        url: serviceSPSArea + 'Add',
        method: 'post',
        data
    })
}


export function getPartList(data){
    return request({
        url: serviceSPSArea + 'GetListPage',
        method: 'post',
        data
    })
}


export function getAllList(params){
    return request({
        url: serviceSPSArea + 'GetAllList',
        method: 'get',
        params
    })
}
export function detailPart(params){
    return request({
        url: serviceSPSArea + 'GetDetails',
        method: 'get',
        params
    })
}

export function editPart(data){
    return request({
        url: serviceSPSArea + 'Edit',
        method: 'post',
        data
    })
}

export function delPart(data){
    return request({
        url: serviceSPSArea + 'Delete',
        method: 'post',
        data
    })
}


//设备配件模板
export function tempList(data){
    return request({
        url: serviceSPTArea + 'GetListPage',
        method: 'post',
        data
    })
}

export function addtemp(data){
    return request({
        url: serviceSPTArea + 'Add',
        method: 'post',
        data
    })
}
export function edittemp(data){
    return request({
        url: serviceSPTArea + 'Edit',
        method: 'post',
        data
    })
}
export function deltemp(data){
    return request({
        url: serviceSPTArea + 'Delete',
        method: 'post',
        data
    })
}
export function getPartSpecification(data){
    return request({
        url: serviceSPTArea + 'GetPartSpecification',
        method: 'post',
        data
    })
}



export function addPartSpecification(data){
    return request({
        url: serviceSPTArea + 'AddPartSpecification',
        method: 'post',
        data
    })
}

export function deletePartSpecification(data){
    return request({
        url: serviceSPTArea + 'DeletePartSpecification',
        method: 'post',
        data
    })
}