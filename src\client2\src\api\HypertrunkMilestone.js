import request from "@/utils/request";
import { serviceArea } from "./serviceArea";

let serviceAreaName = serviceArea.business + "/HypertrunkMilestone/";

export function getDetails(params) {
  return request({
    url: serviceAreaName + "GetDetails",
    method: "get",
    params,
  });
}

export function addApi(data) {
  return request({
    url: serviceAreaName + "Add",
    method: "post",
    data,
  });
}

export function editApi(data) {
  return request({
    url: serviceAreaName + "Edit",
    method: "post",
    data,
  });
}

export function deleteApi(data) {
  return request({
    url: serviceAreaName + "Delete",
    method: "post",
    data,
  });
}

export function followUpApi(data) {
  return request({
    url: serviceAreaName + "FollowUp",
    method: "post",
    data,
  });
}

export function getTreeData(data) {
  return request({
    url: serviceAreaName + "GetTreeData",
    method: "post",
    data,
  });
}

export function getOrganizeGoal(params) {
  return request({
    url: serviceAreaName + "GetOrganizeGoal",
    method: "get",
    params,
  });
}

export function financeFollowUp(data) {
  return request({
    url: serviceAreaName + "FinanceFollowUp",
    method: "post",
    data,
  });
}
export function getApprovalDetails(data) {
  return request({
    url: serviceAreaName + "GetApprovalDetails",
    method: "post",
    data,
  });
}
export function approval(data) {
  return request({
    url: serviceAreaName + "Approval",
    method: "post",
    data,
  });
}
export function getCalSumApi(params) {
  return request({
    url: serviceAreaName + "CalSum",
    method: "get",
    params,
  });
}
export function getCalSelfSumApi(params) {
  return request({
    url: serviceAreaName + "CalSelfSum",
    method: "get",
    params,
  });
}



