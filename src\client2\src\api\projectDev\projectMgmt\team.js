import request from '@/utils/request'
import { serviceArea } from '../../serviceArea'

let serviceAreaName = serviceArea.business + '/TeamMember/'


export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(data) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'post',
        data
    })
}

export function batchAdd(data) {
    return request({
        url: serviceAreaName + 'BatchAdd',
        method: 'post',
        data
    })
}

//对应Id员工参与工作的按月统计
export function getWorkStatisticsForMonth(params) {
    return request({
        url: serviceAreaName + 'GetWorkStatisticsForMonth',
        method: 'get',
        params
    })
}

//根据项目id获取团队（仅员工信息）
export function getByProjectId(params) {
    return request({
        url: serviceAreaName + 'GetByProjectId',
        method: 'get',
        params
    })
}

//获得项目下团队（成员信息和员工信息）
export function getTeamMemberListByProjectId(params) {
    return request({
        url: serviceAreaName + 'GetTeamMemberListByProjectId',
        method: 'get',
        params
    })
}

//获得项目下团队是否在变更中
export function teamMemberIsInChange(params) {
    return request({
        url: serviceAreaName + 'TeamMemberIsInChange',
        method: 'get',
        params
    })
}

export function responsibilities(data) {
    return request({
        url: serviceAreaName + 'Responsibilities',
        method: 'post',
        data
    })
}

export function getProjectInfoStatistics(data) {
    return request({
        url: serviceAreaName + 'GetProjectInfoStatistics',
        method: 'post',
        data
    })
}

export function getQuestionStatistics(data) {
    return request({
        url: serviceAreaName + 'GetQuestionStatistics',
        method: 'post',
        data
    })
}
