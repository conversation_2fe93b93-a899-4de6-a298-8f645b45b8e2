
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HRLeaveBalanceData/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'ListPage/',
        method: 'post',
        data
    })
}

export function getDetailsByEmployeesId(params) {
    return request({
        url: serviceAreaName + 'GetDetailsByEmployeesId',
        method: 'get',
        params
    })
}



export function getHrComputationRules(params) {
    return request({
        url: serviceAreaName + 'GetHrComputationRules',
        method: 'get',
        params
    })
}

export function getHrComputationRuleDetail(params) {
    return request({
        url: serviceAreaName + 'GetHrComputationRuleDetail',
        method: 'get',
        params
    })
}

export function editHrComputationRule(data) {
    return request({
        url: serviceAreaName + 'EditHrComputationRule',
        method: 'post',
        data
    })
}




