import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.resource
let busServiceAreaName = serviceArea.business
let userServiceAreaName = serviceArea.user

//图片(type==1)
export function postFile(data, config) {
    return request({
        url: serviceAreaName + '/FileUpload/UploadFile',
        method: 'post',
        data,
        config
    })
}

//视频(type==3)
export function postVideoFile(data, config) {
    return request({
        url: serviceAreaName + '/FileUpload/UploadFile',
        method: 'post',
        data,
        config
    })
}

//其他附件（不限定格式）type==4
export function postOther(data, config){
    return request({
        url: serviceAreaName + '/FileUpload/UploadFile',
        method: 'post',
        data,
        config
    })
}



//检测文件
export function testFile(params) {
    return request({
        url: serviceAreaName + '/FileUpload/UploadChunkRichText',
        method: 'get',
        params
    })
}

//上传切片
export function uploadChunk(data, config) {
    return request({
        url: serviceAreaName + '/FileUpload/UploadChunk',
        method: 'post',
        data,
        config
    })
}


//合并文件
export function mergeChunks(data, config){
    return request({
        url: serviceAreaName + '/FileUpload/MergeChunks',
        method: 'post',
        data,
        config
    })
}

// export function downloadFile(fileurl) {
//     return request({
//         url: `/download/${fileurl}`,
//         method: 'get'
//     })
// }

//富文本上传视频切片
export function postUploadChunk(data, config) {
    return request({
        url: serviceAreaName + '/FileUpload/UploadChunkRichText',
        method: 'post',
        data,
        config
    })
}

export function sort(data) {
    return request({
        url: busServiceAreaName + '/Sort/SetSort',
        method: 'post',
        data
    })
}

//保存常用联系人
export function saveTopContacts(data) {
    return request({
        url: userServiceAreaName + '/SystemTopContact/StatisticsSystemTopContact',
        method: 'post',
        data
    })
}
// 修改记录/历程
export function getCourseRecordList(data) {
  return request({
    url: busServiceAreaName + "/CommonHistory/GetList",
    method: "post",
    data,
  });
}