import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

// 分页查询
export function getListPage(data) {
    return request({
        url: serviceAreaName + '/ProductManagement/GetListPage',
        method: 'post',
        data
    })
}

// 获取全部产品
export function getAllProductList() {
    return request({
        url: serviceAreaName + '/ProductManagement/GetAllProductList',
        method: 'get'
    })
}

// 根据产品ID获取详情
export function getProductDetailById(params) {
    return request({
        url: serviceAreaName + '/ProductManagement/GetProductDetailById',
        method: 'get',
        params
    })
}

// 操作数据集合
// 添加/修改
export function AddOrUpdateProduct(data) {
    return request({
        url: serviceAreaName + '/ProductManagement/AddOrUpdateProduct',
        method: 'post',
        data
    })
}

// 删除产品
export function DeleteProduct(data) {
    return request({
        url: serviceAreaName + '/ProductManagement/DeleteProduct',
        method: 'post',
        data
    })
}