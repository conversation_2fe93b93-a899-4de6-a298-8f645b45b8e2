import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
// export function getList(data) {
//     return request({
//         url: serviceAreaName + '/CalendarSetting/GetListPage',
//         method: 'post',
//         data
//     })
// }

// export function detail(params) {
//     return request({
//         url: serviceAreaName + '/CalendarSetting/GetDetails',
//         method: 'get',
//         params
//     })
// }

export function add(data) {
    return request({
        url: serviceAreaName + '/CalendarSetting/Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/CalendarSetting/Edit',
        method: 'post',
        data
    })
}


export function getSettingByYear(params) {
    return request({
        url: serviceAreaName + '/CalendarSetting/GetSettingByYear',
        method: 'get',
        params
    })
}

export function getRestDay(params) {
    return request({
        url: serviceAreaName + '/CalendarSetting/GetRestDay',
        method: 'get',
        params
    })
}

export function getRestDayByDate(data) {
    return request({
        url: serviceAreaName + '/CalendarSetting/GetRestDayByDate',
        method: 'post',
        data
    })
}


// export function clearByDate(params) {
//     return request({
//         url: serviceAreaName + '/CalendarSetting/ClearByDate',
//         method: 'get',
//         params
//     })
// }

// export function del(data){
//     return request({
//         url: serviceAreaName + '/AppVersion/Delete',
//         method: 'post',
//         data
//     })
// }
