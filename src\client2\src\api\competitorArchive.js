import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

//新增
export function addCompetitorArchive(data) {
    return request({
        url: serviceAreaName + '/CompetitorArchive/Add',
        method: 'post',
        data
    })
}

//编辑
export function editCompetitorArchive(data) {
    return request({
        url: serviceAreaName + '/CompetitorArchive/Edit',
        method: 'post',
        data
    })
}

//删除
export function deleteCompetitorArchive(data) {
    return request({
        url: serviceAreaName + '/CompetitorArchive/Delete',
        method: 'post',
        data
    })
}
//分页列表
export function getCompetitorArchiveListPage(data) {
    return request({
        url: serviceAreaName + '/CompetitorArchive/GetListPage',
        method: 'post',
        data
    })
}
//完整列表
export function getCompetitorArchiveList() {
    return request({
        url: serviceAreaName + '/CompetitorArchive/GetList',
        method: 'get',
    })
}

//导出
export function exportCompetitorArchive(data) {
    return request({
        url: serviceAreaName + '/CompetitorArchive/Export',
        method: 'post',
        data
    })
}

//详情
export function getCompetitorArchiveDetail(params) {
    return request({
        url: serviceAreaName + '/CompetitorArchive/GetCompetitorArchiveDetail',
        method: 'get',
        params
    })
}

//获取动态属性集合
export function getCompetitorDynamicAttributeList(params) {
    return request({
        url: serviceAreaName + '/CompetitorArchive/GetCompetitorDynamicAttributeList',
        method: 'get',
        params
    })
} 