<template>
  <span :class="_class" :style="getStyle()" class="cus-tag omit">
    <slot>
      <span>{{ text }}</span>
      <i class="el-icon-close" v-if="closable" @click.stop="close"></i>
    </slot>
  </span>
</template>

<script>
import { olorRgb } from "@/utils";

export default {
  name: "app-tag-pure",
  props: {
    effect: {
      type: String,
      default: "plain", //plain / dark
    },
    tagClass: {
      type: [String, Array, Object],
      default: null,
    },
    text: {
      type: String,
      default: "",
    },
    color: {
      type: String,
      default: "#267FEB", //主色
    },
    // 是否可关闭
    closable: {
      type: Boolean,
      default: false,
    },
    maxWidth: {
      type: Number,
      default: -1
    },
  },
  computed: {
    _class() {
      // let a = ''
      // let b = {}
      // let c = []

      // let aa = Object.prototype.toString.call(a)
      // let bb = Object.prototype.toString.call(b)
      // let cc = Object.prototype.toString.call(c)

      let result = [this.effect];

      let classType = Object.prototype.toString.call(this.tagClass);
      if (classType == "[object Array]") {
        return result.concat(classType);
      } else if (classType == "[object String]") {
        return result.push(classType);
      } else if (classType == "[object Object]") {
        return result.concat(classType);
      }
      return "";
    },
  },
  data() {
    return {};
  },
  methods: {
    getStyle() {
      let result = {}
      if (this.color) {
        if (this.effect == "plain") {
          result = {
            background: `${olorRgb(this.color)}`,
            border: `1px solid ${this.color}`,
            color: this.color,
          };
        } else {
          result = {
            background: this.color,
            border: `1px solid ${this.color}`,
            color: "#ffffff",
          };
        }
      }

      if(this.maxWidth > -1) {
        result['max-width'] = `${this.maxWidth}px`
      }

      return result
    },
    close(){
      this.$emit('close')
    }

    //十六进制转rgba
    // olorRgb(color){
    //     var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
    //     var sColor = color.toLowerCase();
    //     if(sColor && reg.test(sColor)){
    //     if(sColor.length === 4){
    //         var sColorNew = "#";
    //         for(var i=1; i<4; i+=1){
    //         sColorNew += sColor.slice(i,i+1).concat(sColor.slice(i,i+1));
    //         }
    //         sColor = sColorNew;
    //     }
    //     //处理六位的颜色值
    //     var sColorChange = [];
    //     for(var i=1; i<7; i+=2){
    //         sColorChange.push(parseInt("0x"+sColor.slice(i,i+2)));
    //     }
    //     return "rgba(" + sColorChange.join(",") + ", 0.15)";
    //     }else{
    //         return sColor;
    //     }
    // },
  },
};
</script>

<style lang="scss" scoped>
.cus-tag {
  display: inline-block;
  vertical-align: top;
  border-width: 1px;
  // border-style: solid;
  box-sizing: border-box;
  white-space: nowrap;
  border-radius: 2px;
  font-size: 12px;
  padding: 0 4px;
  height: 20px;
  line-height: 19px !important;
  .el-icon-close{
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
