import request from "@/utils/request";
import { serviceArea } from "./serviceArea";

let serviceAreaName = serviceArea.business + "/WorkbenchBlock/";
export function getWorkbenchStatistics(params) {
  return request({
    url: serviceAreaName + "WorkbenchStatistics",
    method: "get",
    params,
  });
}

/**
 * 获取全局待办事项
 * @param {*} data 
 * @returns 
 */
export function getGlobalSchedule(data) {
  return request({
    url: serviceAreaName + "GetGlobalSchedule",
    method: "post",
    data,
  });
}




