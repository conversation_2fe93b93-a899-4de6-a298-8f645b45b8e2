/**评论接口 */
import request from '@/utils/request'
import { serviceArea } from '../../serviceArea'

let serviceAreaName = serviceArea.business + '/Comment/'
/**获取评论列表 */
export function getAllComment(data) {
    return request({
        url: serviceAreaName + 'GetAllComment',
        method: 'post',
        data
    })
}

/**新增 */
export function addRange(data) {
    return request({
        url: serviceAreaName + 'AddRange',
        method: 'post',
        data
    })
}

/**撤回评论 */
export function deleteComment(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

/**类型统计 */
export function getAllCommentCount(data) {
    return request({
        url: serviceAreaName + 'GetAllCommentCount',
        method: 'post',
        data
    })
}


export function geByParentBusinessId(params) {
    return request({
        url: serviceAreaName + 'GeByParentBusinessId',
        method: 'get',
        params
    })
}


export function getAllHypertrunkComment(data) {
    return request({
        url: serviceAreaName + 'GetAllHypertrunkComment',
        method: 'post',
        data
    })
}




