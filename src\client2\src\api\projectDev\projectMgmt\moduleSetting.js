import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/Modular/'



export function getModularList(data) {
    return request({
        url: busServiceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function addList(data) {
    return request({
        url: busServiceAreaName + 'AddList',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: busServiceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function edit(data) {
    return request({
        url: busServiceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: busServiceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//获取模块相关（需求、任务、问题）统计信息
export function getSummary(params) {
    return request({
        url: busServiceAreaName + 'GetTotalAsync',
        method: 'get',
        params
    })
}

export function move(data) {
    return request({
        url: busServiceAreaName + 'RelationMoveAsync',
        method: 'post',
        data
    })
}


