import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/ReturnVisits/'
export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data){
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function detail(params){
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

//提交
export function follow(data){
    return request({
        url: serviceAreaName + 'AddReturnVisitsDetail',
        method: 'post',
        data
    })
}

export function save(data){
    return request({
        url: serviceAreaName + 'AddDetail',
        method: 'post',
        data
    })
}

export function createApproval(data){
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function getEmployees(params) {
    return request({
        url: serviceAreaName + 'GetEmployee',
        method: 'get',
        params
    })
}

export function getStatisticalStatesNumber(params) {
    return request({
        url: serviceAreaName + 'GetStatisticalStatesNumber',
        method: 'get',
        params
    })
}


