<template>
  <div :class="{'hidden':hidden}" class="pagination-container">
    <el-pagination :background="background" :current-page.sync="currentPage" :page-size.sync="pageSize" :layout="layout"
      :total="total" v-bind="$attrs" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
  </div>
</template>

<script>
  import { scrollTo } from '@/utils/scrollTo'
  import { getPaginationObj, setPaginationObj } from '../../utils'

  export default {
    name: 'Pagination',
    props: {
      total: {
        required: true,
        type: Number
      },
      page: {
        type: Number,
        default: 1
      },
      size: {
        type: Number,
        default: 20
      },
      pageSizes: {
        type: Array,
        default() {
          return [10, 20, 30, 50]
        }
      },
      layout: {
        type: String,
        default: 'total, sizes, prev, pager, next, jumper'
      },
      background: {
        type: Boolean,
        default: true
      },
      autoScroll: {
        type: <PERSON>olean,
        default: true
      },
      hidden: {
        type: <PERSON><PERSON><PERSON>,
        default: false
      },
      viewBusinessType: {
        type: Number,
        default: -1,
      },
    },
    computed: {
      currentPage: {
        get() {
          return this.page
        },
        set(val) {
          this.$emit('update:page', val)
        }
      },
      pageSize: {
        get() {
          return this.size
        },
        set(val) {
          if(this.viewBusinessType > 0) {
            let pageObj = getPaginationObj(this.viewBusinessType)
            if(!pageObj) {
              pageObj = {
                PageSize: val
              }
            }else{
              pageObj.PageSize = val
            }
  
            setPaginationObj(this.viewBusinessType, pageObj)
          }

          this.$emit('update:size', val)
        }
      }
    },
    methods: {
      handleSizeChange(val) {
        this.$emit('size-change', { page: this.currentPage, size: val })
        if (this.autoScroll) {
          scrollTo(0, 800)
        }
      },
      handleCurrentChange(val) {
        this.$emit('pagination', { page: val, size: this.pageSize })
        if (this.autoScroll) {
          scrollTo(0, 800)
        }
      },

      getPageinationObj() {

      },
    }
  }
</script>

<style scoped>
  .pagination-container {
    background: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }

  .pagination-container.hidden {
    display: none;
  }
</style>