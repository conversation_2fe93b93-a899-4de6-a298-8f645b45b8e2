import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/ImplementationManage/'

//根据实施工程ID获取地区下的实施地区与地区基本信息
export function getRegionList(params) {
    return request({
        url: serviceAreaName + 'GetImplementDetailAndImplementationRegionalList',
        method: 'get',
        params
    })
}

//获得实施地区详情
export function getRegionDetail(params) {
    return request({
        url: serviceAreaName + 'GetImplementationRegionalDetails',
        method: 'get',
        params
    })
}

//添加（批量）地区
export function addRegionList(data) {
    return request({
        url: serviceAreaName + 'AddImplementationRegionalList',
        method: 'post',
        data
    })
}

//获得地区详情 和 工序列表
export function getImplementationDetail(params) {
    return request({
        url: serviceAreaName + 'GetImplementationRegionalDetailAndImplementationProcedureList',
        method: 'get',
        params
    })
}

//根据地区获取问题列表
export function getQuestions(data) {
    return request({
        url: serviceAreaName + 'GetImplementationQuestionListPage',
        method: 'post',
        data
    })
}

//根据地区设备列表
export function getEqus(data) {
    return request({
        url: serviceAreaName + 'GetImplementationEquipmentListPage',
        method: 'post',
        data
    })
}

//根据地区设备列表
export function getEqusNoPage(data) {
    return request({
        url: serviceAreaName + 'GetImplementationEquipmentListByCondition',
        method: 'post',
        data
    })
}


//获取实施模板
export function getTemplates(data) {
    return request({
        url: serviceAreaName + 'GetImplementationTemplateListByCondition',
        method: 'post',
        data
    })
}

//添加实施设备
export function addImpEqus(data) {
    return request({
        url: serviceAreaName + 'AddImplementationEquipmentList',
        method: 'post',
        data
    })
}

//获取工序列表
export function getProcedureList(data) {
    return request({
        url: serviceAreaName + 'GetImplementationProcedureListByCondition',
        method: 'post',
        data
    })
}

//新增编辑工序（集合）
export function AddOrEditProcedureList(data) {
    return request({
        url: serviceAreaName + 'EditImplementationProcedureList',
        method: 'post',
        data
    })
}

//编辑实施地区
export function editImpRegion(data) {
    return request({
        url: serviceAreaName + 'EditImplementationRegional',
        method: 'post',
        data
    })
}


//删除实施地区
export function delImpRegion(data) {
    return request({
        url: serviceAreaName + 'DeleteImplementationRegional',
        method: 'post',
        data
    })
}


//删除实施地区
export function delImpEqu(data) {
    return request({
        url: serviceAreaName + 'DeleteImplementationEquipment',
        method: 'post',
        data
    })
}

//导入工序、撤销、上一个工序、下一个工序
export function setEquProce(data) {
    return request({
        url: serviceAreaName + 'SetEquipmentToProcedure',
        method: 'post',
        data
    })
}

//获取问题列表
export function getQues(data) {
    return request({
        url: serviceAreaName + 'GetImplementationQuestionListPage',
        method: 'post',
        data
    })
}

//创建问题
export function addQues(data) {
    return request({
        url: serviceAreaName + 'AddImplementationQuestion',
        method: 'post',
        data
    })
}

export function editQues(data) {
    return request({
        url: serviceAreaName + 'EditImplementationQuestion',
        method: 'post',
        data
    })
}

export function getQeus(params) {
    return request({
        url: serviceAreaName + 'GetImplementationQuestionDetails',
        method: 'get',
        params
    })
}

export function delQues(data) {
    return request({
        url: serviceAreaName + 'DeleteImplementationQuestion',
        method: 'post',
        data
    })
}

export function assign(params) {
    return request({
        url: serviceAreaName + 'SetImplementationQuestionEmployee',
        method: 'get',
        params
    })
}


//获取工序详情
export function getImpProceDetails(params) {
    return request({
        url: serviceAreaName + 'GetImplementationProcedureDetails',
        method: 'get',
        params
    })
}

//获取工序事项列表
export function getImpItems(data) {
    return request({
        url: serviceAreaName + 'GetImplementationItemListByCondition',
        method: 'post',
        data
    })
}

//获取工序设备列表
export function getProcesEqus(data) {
    return request({
        url: serviceAreaName + 'GetImplementationItemListPage',
        method: 'post',
        data
    })
}


//设置设备状态
export function setImpEquStatus(params) {
    return request({
        url: serviceAreaName + 'SetImplementationEquipmentStatus',
        method: 'get',
        params
    })
}

//实施事项管理（新增、编辑）
export function editImpItems(data) {
    return request({
        url: serviceAreaName + 'EditImplementationItemList',
        method: 'post',
        data
    })
}

//获取实施事项详情
export function getImpItemDetails(params) {
    return request({
        url: serviceAreaName + 'GetImplementationItemDetails',
        method: 'get',
        params
    })
}

//编辑实施事项
export function editImpItem(data) {
    return request({
        url: serviceAreaName + 'EditImplementationItem',
        method: 'post',
        data
    })
}

//新增设备审批
export function addEquApproval(data) {
    return request({
        url: serviceAreaName + 'ImplementationEquipmentApproval',
        method: 'post',
        data
    })
}

export function editEqu(data) {
    return request({
        url: serviceAreaName + 'editEqu',
        method: 'post',
        data
    })
}

//获取设备详情
export function getImpEquDetails(params) {
    return request({
        url: serviceAreaName + 'GetImplementationEquipmentDetails',
        method: 'get',
        params
    })
}

//新增设备变更
export function addEquChange(data) {
    return request({
        url: serviceAreaName + 'EditImplementationEquipment',
        method: 'post',
        data
    })
}

//获取设备变更详情
export function getImpEquChangeDetails(params) {
    return request({
        url: serviceAreaName + 'GetImplementationEquipmentChangeDetails',
        method: 'get',
        params
    })
}
//设备变更审批
export function changeEquApproval(data) {
    return request({
        url: serviceAreaName + 'ImplementationEquipmentChangeApproval',
        method: 'post',
        data
    })
}

//获取地区下的问题处理人
export function getImplementationQuestionEmployeeList(params) {
    return request({
        url: serviceAreaName + 'GetImplementationQuestionEmployeeList',
        method: 'get',
        params
    })
}




//根据实施工程ID获取地区下的实施地区与地区基本信息
export function getRegionListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetImplementationRegionalListByCondition',
        method: 'post',
        data
    })
}

//获得地区详情 和 工序列表
export function getImplementationListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetImplementationProcedureListByCondition',
        method: 'post',
        data
    })
}