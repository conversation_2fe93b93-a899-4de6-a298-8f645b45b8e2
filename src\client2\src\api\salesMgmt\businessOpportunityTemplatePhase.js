import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/BusinessOpportunityTemplatePhase/'

/********************************************* 阶段 *********************************************/
//新增阶段
export function add(data) {
    return request({
        url: serviceAreaName + 'EditAllList',
        method: 'post',
        data
    })
}

//删除阶段
export function deleteStage(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}


//分页查询阶段
export function getStageListPage(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

