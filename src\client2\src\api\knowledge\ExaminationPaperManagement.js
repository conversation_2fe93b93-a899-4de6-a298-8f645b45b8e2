import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/ExaminationPaperManagement/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}
export function TestPaperMaking(params) {
    return request({
        url: serviceAreaName + 'TestPaperMaking',
        method: 'get',
        params
    })
}

export function TestPaperMakingByPaperId(params) {
    return request({
        url: serviceAreaName + 'TestPaperMakingByPaperId',
        method: 'get',
        params
    })
}
export function SubmitPaper(data) {
    return request({
        url: serviceAreaName + 'SubmitPaper',
        method: 'post',
        data
    })
}


export function GetNumberByDifficultyLevel(params) {
    return request({
        url: serviceAreaName + 'GetNumberByDifficultyLevel',
        method: 'get',
        params
    })
}