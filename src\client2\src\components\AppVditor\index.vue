<template>
  <div class="vditor-wrapper" :class="readonly ? 'readonly' : ''">
    <div class="jindu" v-show="jindushow">
      <el-progress
        class="elProgress"
        type="circle"
        :width="80"
        :percentage="percentage"
        :show-text="showText"
      ></el-progress>
      <span class="analyze" v-show="anaShow">文件分析中</span>
      <el-button class="cancelUpload" type="primary" @click="close" v-show="cancelShow">
        取消上传
      </el-button>
    </div>

    <div :id="id" v-viewer></div>

    <span :class="`uploadVideoId${id} uploadVideo`" style="display:none;" @click="upVideo">
      确认
    </span>
    <el-upload
      style="display:none;"
      :show-upload-list="false"
      :before-upload="beforeAvatarUpload"
      :on-success="handleSuccess"
      accept=".jpg,.jpeg,.png,.gif"
      :max-size="2048"
      :headers="imgHeaders"
      multiple
      action="/api/Resources/FileUpload/UploadRichTextFile"
      :class="`uploadImageId${id} uploadImage`"
    ></el-upload>
  </div>
</template>

<script>
import * as businessMap from "@/api/businessMap";
import Vditor from "vditor";
import "vditor/dist/index.css";
import { getToken } from "@/utils/auth";
import { sizeUnitConversion } from '@/utils/index'
import { uploadByPieces, closeUpload, Video } from "../QuillEditor/video.js";

const uploadConfig = {
  action: "/api/Resources/FileUpload/UploadRichTextFile", // 必填参数 图片上传地址
  methods: "POST", // 必填参数 图片上传方式
  token: getToken(), // 可选参数 如果需要token验证，假设你的token有存放在sessionStorage
  name: "file", // 必填参数 文件的参数名
  size: 700, // 可选参数   图片大小，单位为Kb, 1M = 1024Kb
  accept: "image/png, image/gif, image/jpeg, image/bmp, image/x-icon", // 可选 可上传的图片格式
  type: "audio/mp4,video/mp4",
};

export default {
  name: "AppVditor",
  model: {
    prop: "selected",
    event: "change",
  },
  props: {
    selected: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      required: false,
      default() {
        return "markdown-editor-" + +new Date() + ((Math.random() * 1000).toFixed(0) + "");
      },
    },
    isHideTools: {
      type: Boolean,
      required: false,
      default: false,
    },
    isPin: {
      type: Boolean,
      required: false,
      default: true,
    },
    height: {
      type: [Number, String],
      required: false,
      default: 160,
    },
    width: {
      type: String,
      required: false,
      default: "auto",
    },
    mode: {
      type: String,
      required: false,
      default: "ir", //wysiwyg ir sv
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      vditor: "",
      jindushow: false, //控制上传进度显示隐藏
      percentage: 0,
      showText: false, //控制上传百分比显示隐藏
      anaShow: false, //控制文件分析文字显示隐藏

      imgHeaders: { token: getToken() },
      cancelShow: false, //控制取消上传视频显示隐藏

      imgList: ['png', 'jpeg', 'jpg', 'bmp'],
      imgLimit: 1024 * 1024 * 10, // 10mb   
      videoList: ['mp4', 'm3u8', 'rmvb', 'avi', 'swf', '3gp', 'mkv', 'flv'],
      videoLimit: 1024 * 1024 * 500, // 2g   1024 * 1024 * 1020 * 2
    };
  },
  watch:{
    selected:{
      handler(val){
        //如果是“详情”，子需要展示markdown内容，不需要初始化md编辑器。
        this.$nextTick(()=>{
          if (this.readonly) {
            const reviewDom = document.getElementById(`${this.id}`);
            if(!reviewDom) return
            Vditor.preview(reviewDom, this.selected, {
              speech: {
                enable: false,
              },
              anchor: 1,
              after() {},
            });
          }
        })
      },
      immediate:true
    }
  },
  mounted() {
    //非详情，需要初始化编辑器
    if(!this.readonly){
      let that = this;
      that.vditor = new Vditor(`${that.id}`, {
        cdn: '',
        height: that.height,
        //   minHeight: 160,//自适应，回车自动拉伸高度（给了最小高度，回车就没发自动拉伸高度）
        width: that.width,
        toolbarConfig: {
          pin: that.isPin,
          hide: that.isHideTools,
        },
        //禁止自带图片双击预览功能
        image: {
          isPreview: false
        },
        //支持拖动大小
        resize: {
          enable: true,
        },
        cache: {
          enable: false,
        },
        toolbar: [
          "emoji",
          "headings",
          "bold",
          "italic",
          "strike",
          "link",
          "|",
          "list",
          "ordered-list",
          "check",
          "outdent",
          "indent",
          "|",
          "quote",
          "line",
          "code",
          "inline-code",
          "insert-before",
          "insert-after",
          "|",
          // "upload",
          // "record",

          {
            //自定义上传
            hotkey: "",
            name: "custom-upload",
            tipPosition: "n", // 'n', 'ne', 'nw', 's', 'se', 'sw', 'w', 'e'
            tip: "上传图片",
            icon: `<svg t="1722333116875" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4203" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M559.104 0c3.413333 0 6.144 0 9.557333 0.682667 3.413333 0.682667 6.144 1.365333 9.557334 2.730666l8.192 4.096c2.730667 2.048 5.461333 4.096 7.509333 6.144 2.730667 2.048 4.778667 4.778667 6.826667 7.509334 1.365333 2.048 3.413333 5.461333 4.778666 8.192 1.365333 2.730667 2.048 6.144 2.730667 8.874666 1.365333 3.413333 1.365333 6.144 2.048 9.557334 0 3.413333 0 6.826667-0.682667 9.557333-0.682667 3.413333-1.365333 6.826667-2.048 9.557333-1.365333 3.413333-2.048 6.144-4.096 8.874667-1.365333 2.730667-3.413333 5.461333-5.461333 8.192-2.048 2.048-4.096 4.778667-6.826667 6.826667-2.730667 2.048-4.778667 3.413333-8.192 4.778666a21.162667 21.162667 0 0 1-8.874666 4.096l-8.874667 2.048-6.144 0.682667H136.533333c-18.432 0-34.133333 15.701333-34.133333 34.133333l-0.682667 329.728 95.573334-109.226666 278.528 255.317333L607.573333 438.954667l314.026667 253.269333V464.896c0-2.730667 0-6.144 0.682667-9.557333 0.682667-2.730667 1.365333-6.144 2.730666-8.874667 1.365333-3.413333 2.730667-6.144 4.096-8.874667 2.048-2.730667 4.096-4.778667 6.144-7.509333 2.048-2.048 4.778667-4.096 7.509334-6.144 2.048-2.048 5.461333-3.413333 8.192-4.778667 2.730667-1.365333 6.144-2.730667 8.874666-3.413333 3.413333-0.682667 6.144-1.365333 9.557334-1.365333 3.413333-0.682667 6.826667 0 9.557333 0 3.413333 0.682667 6.826667 1.365333 9.557333 2.048 3.413333 1.365333 6.144 2.730667 8.874667 4.096 2.730667 1.365333 5.461333 3.413333 8.192 5.461333 2.048 2.048 4.778667 4.096 6.826667 6.826667 2.048 2.730667 3.413333 5.461333 4.778666 8.192 2.048 2.730667 3.413333 5.461333 4.096 8.874666 0.682667 2.730667 1.365333 6.144 2.048 9.557334l0.682667 5.461333-0.682667 436.906667v16.384c-1.365333 58.026667-47.104 105.813333-105.130666 105.813333H142.677333c-14.336-0.682667-27.989333-0.682667-40.96-0.682667-55.978667 0-101.717333-45.738667-101.717333-101.717333V103.082667C0 46.421333 45.738667 0.682667 101.717333 0.682667L559.104 0zM205.482667 503.125333L102.4 622.592v264.192c0 18.432 15.018667 34.133333 34.133333 34.133333l105.130667 0.682667 172.032-227.328-208.213333-191.146667z m419.84 81.237334l-255.317334 336.554666h516.778667c18.432 0 34.133333-15.018667 34.133333-34.133333l0.682667-62.805333-296.277333-239.616zM793.258667 0c3.413333 0 6.826667 0 10.24 0.682667 2.730667 0.682667 6.144 1.365333 9.557333 2.730666 2.730667 1.365333 6.144 2.730667 8.874667 4.778667 2.730667 2.048 5.461333 4.096 7.509333 6.144 2.730667 2.730667 4.778667 5.461333 6.144 8.192 2.048 2.730667 3.413333 5.461333 4.778667 8.874667 1.365333 2.730667 2.730667 6.144 3.413333 9.557333 0.682667 3.413333 0.682667 6.826667 0.682667 10.24v127.658667h128.341333c2.730667 0 6.144 0 9.557333 0.682666 3.413333 0.682667 6.826667 2.048 9.557334 3.413334 3.413333 1.365333 6.144 2.730667 8.874666 4.778666 2.730667 1.365333 5.461333 3.413333 8.192 6.144 2.048 2.048 4.096 4.778667 6.144 7.509334 2.048 2.730667 3.413333 6.144 4.778667 8.874666 1.365333 3.413333 2.048 6.826667 2.730667 9.557334 0.682667 3.413333 1.365333 6.826667 1.365333 10.24 0 3.413333-0.682667 6.826667-1.365333 10.24-0.682667 2.730667-1.365333 6.144-2.730667 9.557333-1.365333 2.730667-2.730667 6.144-4.778667 8.874667-2.048 2.730667-4.096 5.461333-6.144 7.509333a32.085333 32.085333 0 0 1-8.192 6.144c-2.730667 2.048-5.461333 3.413333-8.874666 4.778667-2.730667 1.365333-6.144 2.730667-9.557334 3.413333-3.413333 0.682667-6.826667 0.682667-9.557333 0.682667h-128.341333V409.6c0 2.730667 0 6.144-0.682667 9.557333-0.682667 3.413333-2.048 6.826667-3.413333 9.557334-1.365333 3.413333-2.730667 6.144-4.778667 8.874666a32.085333 32.085333 0 0 1-6.144 8.192c-2.048 2.048-4.778667 4.096-7.509333 6.144-2.730667 2.048-6.144 3.413333-8.874667 4.778667-3.413333 1.365333-6.826667 2.048-9.557333 2.730667-3.413333 0.682667-6.826667 1.365333-10.24 1.365333-3.413333 0-6.826667-0.682667-10.24-1.365333-2.730667-0.682667-6.144-1.365333-9.557334-2.730667-2.730667-1.365333-6.144-2.730667-8.874666-4.778667-2.730667-2.048-5.461333-4.096-7.509334-6.144a32.085333 32.085333 0 0 1-6.144-8.192 36.795733 36.795733 0 0 1-4.778666-8.874666 39.458133 39.458133 0 0 1-3.413334-9.557334c-0.682667-3.413333-0.682667-6.826667-0.682666-9.557333V281.258667H613.717333c-2.730667 0-6.144 0-9.557333-0.682667a39.458133 39.458133 0 0 1-9.557333-3.413333 36.795733 36.795733 0 0 1-8.874667-4.778667 32.085333 32.085333 0 0 1-8.192-6.144c-2.048-2.048-4.096-4.778667-6.144-7.509333-2.048-2.730667-3.413333-6.144-4.778667-8.874667-1.365333-3.413333-2.048-6.826667-2.730666-9.557333a52.701867 52.701867 0 0 1-1.365334-10.24c0-3.413333 0.682667-6.826667 1.365334-10.24 0.682667-2.730667 1.365333-6.144 2.730666-9.557334 1.365333-2.730667 2.730667-6.144 4.778667-8.874666 2.048-2.730667 4.096-5.461333 6.144-7.509334a32.085333 32.085333 0 0 1 8.192-6.144c2.730667-2.048 5.461333-3.413333 8.874667-4.778666 2.730667-1.365333 6.144-2.730667 9.557333-3.413334 3.413333-0.682667 6.826667-0.682667 9.557333-0.682666h128.341334V51.2c0-3.413333 0-6.826667 0.682666-10.24 0.682667-3.413333 2.048-6.826667 3.413334-9.557333 1.365333-3.413333 2.730667-6.144 4.778666-8.874667a32.085333 32.085333 0 0 1 6.144-8.192c2.048-2.048 4.778667-4.096 7.509334-6.144 2.730667-2.048 6.144-3.413333 8.874666-4.778667 3.413333-1.365333 6.826667-2.048 9.557334-2.730666 3.413333-0.682667 6.826667-0.682667 10.24-0.682667z" p-id="4204"></path></svg>`,
            // className: "right",
            click(event, vditor) {
              that.handleClick(event, vditor, 'image')
            },
          },
          {
            hotkey: "",
            name: "custom-upload",
            tipPosition: "n", // 'n', 'ne', 'nw', 's', 'se', 'sw', 'w', 'e'
            tip: "上传视频",
            icon: `<svg t="1722333124270" class="icon" viewBox="0 0 1030 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4346" xmlns:xlink="http://www.w3.org/1999/xlink" width="201.171875" height="200"><path d="M983.04 0H40.96C20.48 0 0 20.48 0 40.96v948.906667c0 13.653333 20.48 34.133333 40.96 34.133333h948.906667c20.48 0 40.96-20.48 40.96-40.96V40.96c-6.826667-20.48-27.306667-40.96-47.786667-40.96zM423.253333 102.4h245.76L593.92 273.066667H354.986667l68.266666-170.666667z m-320.853333 0h211.626667L238.933333 273.066667h-136.533333V102.4z m819.2 819.2h-819.2v-546.133333h819.2v546.133333z m0-648.533333h-211.626667l75.093334-170.666667h143.36V273.066667z" p-id="4347"></path><path d="M443.733333 757.76l197.973334-116.053333c6.826667-6.826667 6.826667-20.48 0-20.48L443.733333 498.346667c-6.826667-6.826667-20.48 0-20.48 13.653333v232.106667c0 6.826667 6.826667 13.653333 20.48 13.653333z" p-id="4348"></path></svg>`,
            // className: "right",
            click(event, vditor) {
              that.handleClick(event, vditor, 'video')
            },
          },

          // "table",
          "|",
          "undo",
          "redo",
          "|",
          // "fullscreen",
          "edit-mode",
          {
            name: "more",
            toolbar: [
              //"both",
              "code-theme",
              "content-theme",
              "export",
              "outline",
              "preview",
              //"devtools",
              // "info",
              //"help",
            ],
          },
        ],
        after: () => {
          let val = that.selected;
          that.vditor.setValue(val);

          if (that.readonly) {
            setTimeout(() => {
              let reviewDom = document.getElementById(`preview-${that.id}`);
              Vditor.preview(reviewDom, val, {
                speech: {
                  enable: false,
                },
                anchor: 1,
                after() {},
              });
            }, 10);
          }
        },
        input: val => {
          that.$emit("change", val);
        },
        blur (val) {
          that.$emit("change", val);
        },
        mode: that.mode,
        preview: {
          mode: "both",
          actions: [],
        },
        upload: {
          accept: "image/*", // 规定上传的图片格式
          url: "/api/uploadFile", // 请求的接口
          multiple: true,
          fieldName: "file",
          max: 10 * 1024 * 1024, //上传图片的大小（）
          // extraData: { 'access_token': this.token }, // 为 FormData 添加额外的参数
          linkToImgUrl: "/api/admin/uploadFile",
          validate: files => {
            // const isLessthan10M = files[0].size / 1024 / 1024 < 10;
            // if (!isLessthan10M) {
            //   this.$message.error('上传图片大小不能超过 10MB!')
            // }
            // this.$message({
            //   message: `上传图片大小不能超过 10MB!`,
            //   type: 'error'
            // })
          },
          // 自定义上传
          handler: files => {
            if(files && files.length > 0) {

              //验证上传图片是否合法
              if(!that.validFiles(files)) {
                return false
              }

              files.forEach(file => {
                let formData = new FormData();
                formData.append('upload', file);
                businessMap.uploadRichTextFile(formData).then(res => {
                  let path = res.Path
                  let fileName = res.FileName

                  that.insertMedia(path, fileName)

                  // let length = this.editor.getSelection().index　//获取当前鼠标焦点位置
                  // this.editor.insertEmbed(length, 'image',url)
                  // this.editor.setSelection(length + 1)
                })
              });
            }

            // that.$message({
            //   message: `暂不支持图片上传`,
            //   type: "error",
            // });
          },
          // 上传图片回显处理
          format(files, responseText) {
            var data = JSON.parse(responseText);

            // 上传图片请求状态
            if (data.status) {
              let filName = data.msg;
              let lastTipNum = filName.substr(
                filName.lastIndexOf("/", filName.lastIndexOf("/") - 1) + 1
              );
              let index = lastTipNum.lastIndexOf("\/");
              let succ = {};
              succ[filName] = "/api" + data.data;
              //图片回显
              return JSON.stringify({
                data,
                data,
                data: {
                  errFiles: [],
                  succMap: succ,
                },
              });
            } else {
              Message({
                message: "图片上传失败",
                type: "error",
              });
            }
          },
          error(msg) {
            console.log(msg + "上传失败");
          },
        },
      });
      this.unwatch = this.$watch("selected", val => {
        if (this.vditor && this.getValue() !== val) {
          this.setValue(val);
        }
      });
    }
  },
  methods: {
    getValue() {
      return this.vditor.getValue();
    },
    getHTML() {
      return this.vditor.getHTML();
    },
    setValue(value) {
      return this.vditor.setValue(value);
    },
    disabled() {
      return this.vditor.disabled();
    },
    handleClick(event, vditor, type) {
      let that = this
      if (type == "video") {
        this.upVideo();
      }else if(type == 'image') {
        document.querySelector(`.uploadImageId${that.id} input`).click()
      }
    },
    close() {
      closeUpload();
      this.jindushow = false;
    },
    getFileSuffix(filename) {
      let suffix = ''
      let idx = filename.lastIndexOf('.')
      if(idx > -1) {
        suffix = filename.substring(filename.lastIndexOf('.') + 1)
      }
      return suffix
    },

    
    validFiles(files, validType = 1) {
      let that = this
      if(files && files.length > 0) {
        let suffixs = files.map(s => that.getFileSuffix(s.name).toLowerCase())

        let txt = validType == 1 ? '图片' : '视频'
        let list = validType == 1 ? that.imgList : that.videoList
        let limit = validType == 1 ? that.imgLimit : that.videoLimit

        let temp = suffixs.some(s => !list.find(ss => ss == s))

        if(temp) {
          that.$message.error(`上传${txt}支持格式为：` + list.join('、'));
          return false
        }

        let sizes = files.map(s => s.size)
        temp = sizes.some(s => s > limit)
        if(temp) {
          that.$message.error(`${txt}大小不能超过${sizeUnitConversion(limit)}`);
          return false
        }
      }

      return true

    },
    upVideo() {
      let that = this;
      var fileInput = document.createElement("input");
      fileInput.Id = "cInput";
      fileInput.setAttribute("type", "file");
      if (uploadConfig.name) {
        fileInput.setAttribute("name", uploadConfig.name);
      }
      fileInput.setAttribute("accept", uploadConfig.type);
      fileInput.classList.add("hiddle");
      fileInput.addEventListener("change", function() {

        let fileObj = fileInput.files[0]

        if(!that.validFiles([fileObj], 2)) {
          return false
        }

        // let suffix = that.getFileSuffix(fileObj.name)
        // if(that.videoList.findIndex(s => s == suffix) == -1) {
        //   that.$message.error("上传视频支持格式为：" + that.videoList.join('、'));
        //   return false
        // }

        let size = fileObj.size;
        
        if (size > that.videoLimit) {
          let temp = sizeUnitConversion(that.videoLimit)
          that.$message.error(`视频大小不能超过${temp}`);
        } else {
          that.jindushow = true;
          that.anaShow = true;
          that.showText = false;
          that.cancelShow = false;
          that.percentage = 0;
          uploadByPieces({
            file: fileObj, //视频实体
            pieceSize: 100, //分片大小
            progress: data => {
              that.cancelShow = true;
              that.anaShow = false;
              that.showText = true;
              // console.log('进度:'+data)
              that.percentage = data;
            },
            success: data => {
              // console.log("分片上传视频成功",data);
              that.anaShow = false;
              that.showText = true;
              that.percentage = 100;

              //插入视频
              let name = data.FileName;
              let path = data.Path

              that.insertMedia(path, name, 2)

              that.jindushow = false;
            },
            error: e => {
              that.anaShow = false;
              that.showText = true;
              that.jindushow = false;
              that.$message.error(e);
            },
          });
        }
      });
      let container = document.getElementById(`${this.id}`);
      container.appendChild(fileInput);
      fileInput.click();
    },
    beforeAvatarUpload(file) {
      return this.validFiles([file])
    },
    // 图片
    handleSuccess (res, file) {
      // let length = this.editor.getSelection().index;
      // this.editor.insertEmbed(length, 'image', res.TMessageData.Path)
      // this.editor.setSelection(length + 1)

      let imgData = res.TMessageData
      //插入图片
      let name = imgData.FileName;
      let path = imgData.Path

      // let insertText = `![${name}](${path})`; // Markdown 格式的图片插入文本
      // this.vditor.insertValue(insertText);

      this.insertMedia(path, name)

    },

    /**
     * 插入媒体文件到编辑器里面
     */
    insertMedia(path, title, type = 1) {
      if(path && title) {
        let _path = encodeURI(path)
        let _title = title
        if(type == 1) {
          let insertText = `![${_title}](${_path})`; // Markdown 格式的图片插入文本
          this.vditor.insertValue(insertText);
        }else if(type == 2) {
          let insertText = `<video controls>
                              <source src="${_path}" type="video/mp4" />
                            </video>`; // Markdown 格式的图片插入文本
          this.vditor.insertValue(insertText);
        }
      }
    },
    
  },
  beforeDestory() {
    if (this.unwatch) {
      this.unwatch();
    }
    if (this.vditor) {
      this.vditor.destory();
    }
  },
};
</script>

<style lang="scss" scoped>
// .vditor-wrapper:not(.readonly) {
//   /deep/.vditor-reset {
//     padding: 10px !important;
//   }
// }

.vditor-wrapper {
  /deep/.vditor-toolbar {
    padding-left: 5px !important;
  }

  /deep/a {
    color: $color-primary;
    text-decoration: underline;
  }

  /deep/ul {
    li {
      list-style-type: disc;
    }
  }

  /deep/ol {
    li {
      list-style-type: decimal;
    }
  }

  /deep/.hiddle{
    display: none;
  }
}

.cancelUpload{
  margin-top: 20px;
  // position: absolute;
  // top:67%;
  // left:50%;
  // transform: translate(-50%,0);
}

.jindu{
  width:100%;
  height:100%;
  position:absolute;
  top:0;
  left:0;
  background:rgba(255,255,255,0.6);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .elProgress{
    // position: absolute;
    // top:50%;
    // left:50%;
    // transform: translate(-50%,-50%);
  }
  .analyze{
    // position: absolute;
    // top:50%;
    // left:50%;
    // transform: translate(-50%,-50%);
    background:rgba(255,255,255,0);
  }
}

</style>
