import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/CustomerSalesStrategy/'

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function statistics(params) {
    return request({
        url: serviceAreaName + 'Statistics',
        method: 'get',
        params
    })
}

export function getStatisticsCustomerStatusType(params) {
    return request({
        url: serviceAreaName + 'StatisticsCustomerStatusType',
        method: 'get',
        params
    })
}

