/**问题接口 */
import request from '@/utils/request'
import { serviceArea } from '../../serviceArea'

let serviceAreaName = serviceArea.business + '/Question/'
/**新增 */
export function addQuestion(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

/**删除 */
export function deleteQuestion(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

/**编辑 */
export function editQuestion(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

/**分页列表 */
export function getListPage(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

/**获得详情 */
export function getDetails(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

/**修改状态 */
export function updateStatus(data) {
    return request({
        url: serviceAreaName + 'UpdateStatus',
        method: 'post',
        data
    })
}

/**指派 */
export function assign(data) {
    return request({
        url: serviceAreaName + 'Assign',
        method: 'post',
        data
    })
}

/**问题处理，更新进度 */
export function updateProgress(data) {
    return request({
        url: serviceAreaName + 'UpdateProgress',
        method: 'post',
        data
    })
}

