
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/SystemJob/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}



export function addCategory(data) {
    return request({
        url: serviceAreaName + 'AddCategory',
        method: 'post',
        data
    })
}


export function editCategory(data) {
    return request({
        url: serviceAreaName + 'EditCategory',
        method: 'post',
        data
    })
}

export function deleteCategory(data) {
    return request({
        url: serviceAreaName + 'DeleteCategory',
        method: 'post',
        data
    })
}

export function getCategoryListPage(data) {
    return request({
        url: serviceAreaName + 'GetCategoryListPage',
        method: 'post',
        data
    })
}

export function adjustmentCategory(data) {
    return request({
        url: serviceAreaName + 'AdjustmentCategory',
        method: 'post',
        data
    })
}

