import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/StudyRecord/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

// export function del(data) {
//     return request({
//         url: serviceAreaName + 'Delete',
//         method: 'post',
//         data
//     })
// }

// export function edit(data) {
//     return request({
//         url: serviceAreaName + 'Edit',
//         method: 'post',
//         data
//     })
// }

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function GetStudyRecordData(params) {
    return request({
        url: serviceAreaName + 'GetStudyRecordData',
        method: 'get',
        params
    })
}

export function GetIntegralListPage(data) {
    return request({
        url: serviceAreaName + 'GetIntegralListPage',
        method: 'post',
        data
    })
}

export function GetIntegralDetail(params) {
    return request({
        url: serviceAreaName + 'GetIntegralDetail',
        method: 'get',
        params
    })
}
export function AdjustingIntegration(data) {
    return request({
        url: serviceAreaName + 'AdjustingIntegration',
        method: 'post',
        data
    })
}