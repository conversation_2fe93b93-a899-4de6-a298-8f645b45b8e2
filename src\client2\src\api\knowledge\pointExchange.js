import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/RedeemPrize/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function modifyClassifyId(data) {
    return request({
        url: serviceAreaName + 'ModifyClassifyId',
        method: 'post',
        data
    })
}
export function modifyStatus(data) {
    return request({
        url: serviceAreaName + 'ModifyStatus',
        method: 'post',
        data
    })
}


export function GetApplyDetect(params) {
    return request({
        url: serviceAreaName + 'GetApplyDetect',
        method: 'get',
        params
    })
}
export function GetApplyListPage(data) {
    return request({
        url: serviceAreaName + 'GetApplyListPage',
        method: 'post',
        data
    })
}


export function approval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function addApply(data) {
    return request({
        url: serviceAreaName + 'AddApply',
        method: 'post',
        data
    })
}

export function setApprovalPrestore(data) {
    return request({
        url: serviceAreaName + 'SetApprovalPrestore',
        method: 'post',
        data
    })
}
export function getApprovalPrestore(params) {
    return request({
        url: serviceAreaName + 'GetApprovalPrestore',
        method: 'get',
        params
    })
}
export function getApplyDetails(params) {
    return request({
        url: serviceAreaName + 'GetApplyDetails',
        method: 'get',
        params
    })
}


