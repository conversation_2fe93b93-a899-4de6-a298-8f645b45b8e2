
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/EnterpriseQualification/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function BatchEditValidDate(data) {
    return request({
        url: serviceAreaName + 'BatchEdit',
        method: 'post',
        data
    })
}


/** 资质管理 4个页面 提醒设置(修改有效期) */
export function EditAlertSetting(data) {
    return request({
        url: serviceArea.business + '/Common/EditAlertSetting',
        method: 'post',
        data
    })
}

export function GetAlertSetting(params) {
    return request({
        url: serviceArea.business + '/Common/GetAlertSetting',
        method: 'get',
        params
    })
}


