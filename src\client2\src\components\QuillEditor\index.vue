<template>
  <div :class="`quillEditorId${myQuillEditorId} quillEditor`"
   v-loading="videoLoading">
    <div class="jindu" v-show="jindushow">
      <el-progress class="elProgress" type="circle" :percentage="percentage" :show-text="showText"></el-progress>
      <span class="analyze" v-show="anaShow">文件分析中</span>
      <el-button class="cancelUpload" type="primary" @click="close" v-show="cancelShow">取消上传</el-button>
    </div>
    <quill-editor
      :class="`ql-editor myQuillEditorId${myQuillEditorId} myQuillEditor`"
      v-model="content"
      v-bind="$attrs" 
      :ref="`myQuillEditor${myQuillEditorId}`"
      :options="editorOption"
      @focus="onEditorFocus($event)"
      @blur="onEditorBlur($event)"
      @change="onEditorChange($event)">
    </quill-editor>
    <span :class="`uploadVideoId${myQuillEditorId} uploadVideo`" style="display:none;" @click='upVideo'>确认</span>
    <el-upload
      :show-upload-list="false"
      :before-upload="beforeAvatarUpload"
      :on-success="handleSuccess"
      accept=".jpg,.jpeg,.png,.gif"
      :max-size="2048"
      :headers='imgHeaders'
      multiple
      action="/api/Resources/FileUpload/UploadRichTextFile"
      :class="`uploadImageId${myQuillEditorId} uploadImage`"
      >
    </el-upload>
  </div>
</template>

<script>

import { uploadByPieces,closeUpload,Video } from "./video.js";
  import { getToken } from '@/utils/auth';
  
  import { quillEditor } from 'vue-quill-editor'
  import * as Quill from 'quill'  //引入编辑器
  Quill.register({ 'formats/video': Video }, true); //视频标签类型
   //quill编辑器的字体
    var fonts = ['SimSun', 'SimHei','Microsoft-YaHei','KaiTi','FangSong','Arial','Times-New-Roman','sans-serif'];  
    var Font = Quill.import('formats/font');  
    Font.whitelist = fonts; //将字体加入到白名单 
    Quill.register(Font, true);
    var sizes =['12px', '14px', '16px' ,'18px', '20px', '22px', '24px', '26px', '32px', '48px','52px','56px']
    var Size = Quill.import('formats/size');
    Size.whitelist = sizes;
    Quill.register(Size, true);
  import * as businessMap from "@/api/businessMap";
  /*富文本编辑图片上传配置*/
  const uploadConfig = {
    action:  '/api/Resources/FileUpload/UploadRichTextFile',  // 必填参数 图片上传地址
    methods: 'POST', // 必填参数 图片上传方式
    token: getToken(), // 可选参数 如果需要token验证，假设你的token有存放在sessionStorage
    name: 'file', // 必填参数 文件的参数名
    size: 700, // 可选参数   图片大小，单位为Kb, 1M = 1024Kb
    accept: 'image/png, image/gif, image/jpeg, image/bmp, image/x-icon', // 可选 可上传的图片格式
    type:'audio/mp4,video/mp4'
  }
  // 工具栏配置
  const toolbarOptions = [
    [{ font: fonts }], // 字体种类-----[{ font: [] }]
    [{ size: sizes }], // 字体大小-----[{ size: ['small', false, 'large', 'huge'] }]
    [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题-----[{ header: [1, 2, 3, 4, 5, 6, false] }]
    ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线 -----['bold', 'italic', 'underline', 'strike']
    ['blockquote', 'code-block'], // 引用  代码块-----['blockquote', 'code-block']
    // [{ header: 1 }, { header: 2 }], // 1、2 级标题-----[{ header: 1 }, { header: 2 }]
    [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表-----[{ list: 'ordered' }, { list: 'bullet' }]
    [{ script: 'sub' }, { script: 'super' }], // 上标/下标-----[{ script: 'sub' }, { script: 'super' }]
    [{ indent: '-1' }, { indent: '+1' }], // 缩进-----[{ indent: '-1' }, { indent: '+1' }]
    [{'direction': 'rtl'}], // 文本方向-----[{'direction': 'rtl'}]
    [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色-----[{ color: [] }, { background: [] }]
    [{ align: [] }], // 对齐方式-----[{ align: [] }]
    ['clean'], // 清除文本格式-----['clean']
    ['image', 'video'], // 链接、图片、视频-----['link', 'image', 'video']
  ]
  export default {
    name: 'editor',
    props: {
      value: {
        type: String,
      },
      theme: {
        type: String,
        default: 'snow'
      }
    },
    data() {
      let self = this, dyMyDate = new Date();
      return {
        myQuillEditorId: dyMyDate.getTime(),// myQuillEditorId  默认为空  页面使用多个富文本时  需传入值  用来区分富文本
        selectionIndex: -1, //上传之前，保存光标位置
        showText:false,//控制上传百分比显示隐藏
        anaShow:false,//控制文件分析文字显示隐藏
        percentage:0,
        videoLoading:false,
        jindushow:false,//控制上传进度显示隐藏
        isFirst:true,
        cancelShow:false,//控制取消上传视频显示隐藏
        imgHeaders:{token: getToken()},
        editor: null,   // 富文本编辑器对象
        content: `<p></p><p><br></p><ol></ol>`, // 富文本编辑器默认内容
        editorOption: { //  富文本编辑器配置
            modules: {
                toolbar: {
                  container: toolbarOptions,
                  handlers: {
                    'image': function (value) {
                        if (value) {
                            document.querySelector(`.uploadImageId${self.myQuillEditorId} input`).click()
                            self.setSelectionIndex()
                        } else {
                            this.quill.format('image', false);
                        }
                    },
                    'video':function(val){
                      document.querySelector(`.uploadVideoId${self.myQuillEditorId}`).click()
                      self.setSelectionIndex()
                    }
                  }
                }
            },
            theme: 'snow', //snow、bubble
            placeholder: '请输入正文',
        },
      }
    },
    components: {
      quillEditor
    },
    watch: {
      value(val) {
        this.content = val
        //解决首次进入光标位置不对的bug
        if(this.isFirst && val.length>0){
          let that=this;
          setTimeout(() => {
            that.editor.setSelection(val.length)
            that.isFirst=false;
          },200)
        }
      },
      theme: {
        handler(val) {
          if (val) {
            if(val == 'bubble') {
              this.editorOption.toolbar = false
              this.editorOption.placeholder = ''
            }
            this.editorOption.theme = val
          }
        },
        immediate: true
      },
    },
    
destroyed(){
  if(this.jindushow){
    this.close();
  }
},
    created(){
    },
    mounted() {
      this.editor = this.$refs[`myQuillEditor${this.myQuillEditorId}`].quill;
      this.stickyPicture();//  自定义粘贴图片功能
    },
    beforeDestroy(){
      this.editor = null;
      delete this.editor;
    },
    methods: {
      //获取富文本光标位置
      setSelectionIndex() {
        if(this.editor) {
          let selectionObj = this.editor.getSelection()
          //如果没有获取到光标位置
          if(!selectionObj) {
            //则聚焦到富文本
            this.editor.focus()
            //再次获取光标对象
            selectionObj = this.editor.getSelection()
          }
          //保存光标位置
          this.selectionIndex = selectionObj.index
        }
      },
      close(){
        closeUpload();
        this.jindushow=false;
      },
      upVideo(){

        let that=this;
        var fileInput = document.createElement('input')
        fileInput.Id='cInput'
        fileInput.setAttribute('type', 'file')
        if (uploadConfig.name) {
          fileInput.setAttribute('name', uploadConfig.name)
        }
        fileInput.setAttribute('accept', uploadConfig.type)
        fileInput.classList.add('ql-video')
        fileInput.addEventListener('change', function() {
          let size=fileInput.files[0].size/1024/1024/1024;
          if(size>2){
            that.$message.error('视频大小不能超过2G!');
          }else{
            that.jindushow=true;
            that.anaShow=true;
            that.showText=false;
            that.cancelShow=false;
            that.percentage=0;
            uploadByPieces({
              file: fileInput.files[0],//视频实体
              pieceSize: 100, //分片大小
              progress:data => {
                that.cancelShow=true;
                that.anaShow=false;
                that.showText=true;
                // console.log('进度:'+data)
                that.percentage=data;
              },
              success: data => {
                // console.log("分片上传视频成功",data);

                try{

                  that.anaShow=false;
                  that.showText=true;
                  that.percentage=100;
                  
                  //如果没有获取到光标位置（保险起见）
                  if(that.selectionIndex == -1) {
                    that.setSelectionIndex()
                  }

                  //需要富文本获取光标，editor.getSelection()才能获取到不返回null，所以调整思路，才打开上传弹框的时候，就保存光标位置；
                  //上传成功后，直接插入到该位置即可

                  // let length = that.editor.getSelection().index　//获取当前鼠标焦点位置（所以不需要临时来获取光标位置）
                  let length = that.selectionIndex

                  that.editor.insertEmbed(length, 'video',{src:data.Path}) //后端无法生成封面图片，,poster:data.Poster
  
                  that.editor.setSelection(length + 1)
                  that.jindushow=false;
                }catch(err){
                  console.log('错误信息：' + JSON.stringify(err))
                }
              },
              error: e => {
                that.anaShow=false;
                that.showText=true;
                that.jindushow=false;

                that.$message.error(e);
              },
            });
          }
        })
        let container=document.querySelector(`.quillEditorId${this.myQuillEditorId}`);
        container.appendChild(fileInput);
        fileInput.click();
      },
      stickyPicture(){
        this.editor.root.addEventListener('paste', evt => {
          if (evt.clipboardData && evt.clipboardData.files && evt.clipboardData.files.length) {
              evt.preventDefault();
              [].forEach.call(evt.clipboardData.files, file => {
                  if (!file.type.match(/^image\/(gif|jpe?g|a?png|bmp)/i)) {
                      return;
                  }
                  var formData = new FormData();
                  formData.append('upload', file);
                  businessMap.uploadRichTextFile(formData).then(res => {
                    let url = res.Path
                    let length = this.editor.getSelection().index　//获取当前鼠标焦点位置
                    this.editor.insertEmbed(length, 'image',url)
                    this.editor.setSelection(length + 1)
                  })
              });
          }
        }, false);
      },
      beforeAvatarUpload(file) {
        const isLt2G = file.size / 1024 / 1024/1024 < 2;
        if (!isLt2G) {
          this.$message({
            message: '上传图片大小不能超过 2G!',
            type: "error"
          });
        }
        return isLt2G;
      },
      // 图片
      handleSuccess (res, file) {
        let length = this.editor.getSelection().index;
        this.editor.insertEmbed(length, 'image', res.TMessageData.Path)
        this.editor.setSelection(length + 1)
      },
      // 准备富文本编辑器
      onEditorReady (editor) {
      },
      // 富文本编辑器 失去焦点事件
      onEditorBlur (editor) {},
      // 富文本编辑器 获得焦点事件
      onEditorFocus (editor) {},
      // 富文本编辑器 内容改变事件
      onEditorChange (editor) {
        this.$emit('edit', this.content);
      },
      setHeight(height) {
        this.editor.container.style.height = `${height}px`
      },
    }
  }
</script>

<style scoped lang="scss">
.cancelUpload{
   position: absolute;
    top:67%;
    left:50%;
    transform: translate(-50%,0);
}
.quillEditor{
  position: relative;
  border-radius: 10px;
}

.jindu{
  width:100%;
  height:100%;
  position:absolute;
  top:0;
  left:0;
  background:rgba(255,255,255,0.6);
  z-index: 1000;
  .elProgress{
    position: absolute;
    top:50%;
    left:50%;
    transform: translate(-50%,-50%);
  }
  .analyze{
    position: absolute;
    top:50%;
    left:50%;
    transform: translate(-50%,-50%);
    background:rgba(255,255,255,0);
  }
}
.myQuillEditor{
   white-space: normal;
   overflow: hidden;
   padding:0;
   height:100%;
}
.uploadImage{
  display: none;
}

.ql-editor{
  /deep/.ql-container.ql-bubble{
    border: 1px solid $border-color-light!important;
  }
}
</style>