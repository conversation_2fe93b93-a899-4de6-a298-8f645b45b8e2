
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/AppraiseDepartment/'
let planServiceName = serviceArea.business + '/AppraisePlan/'

export function getListDept(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function addDept(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function editDept(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detailDept(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function delDept(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}


/* 绩效考核 */
export function addPlan(data) {
    return request({
        url: planServiceName + 'Add',
        method: 'post',
        data
    })
}

export function editPlan(data) {
    return request({
        url: planServiceName + 'Edit',
        method: 'post',
        data
    })
}

export function delPlan(data) {
    return request({
        url: planServiceName + 'Delete',
        method: 'post',
        data
    })
}

export function detailPlan(params) {
    return request({
        url: planServiceName + 'GetDetails',
        method: 'get',
        params
    })
}


export function getListPlan(data) {
    return request({
        url: planServiceName + 'GetListPage',
        method: 'post',
        data
    })
}

//下一步，个人绩效承诺采集（第一步点击）
export function startPersonalGatherAndNext(data) {
    return request({
        url: planServiceName + 'StartPersonalGatherAndNext',
        method: 'post',
        data
    })
}

//第二步点击
export function finishPersonalGatherAndNext(data) {
    return request({
        url: planServiceName + 'FinishPersonalGatherAndNext',
        method: 'post',
        data
    })
}

//第三步点击
export function finishMidtermReviewAndStart(data) {
    return request({
        url: planServiceName + 'FinishMidtermReviewAndStart',
        method: 'post',
        data
    })
}

//第四步点击
export function completeAndPublicResult(data) {
    return request({
        url: planServiceName + 'CompleteAndPublicResult',
        method: 'post',
        data
    })
}

//第五步点击
export function completeAndEnd(data) {
    return request({
        url: planServiceName + 'CompleteAndEnd',
        method: 'post',
        data
    })
}

export function saveMidtermReview(data) {
    return request({
        url: planServiceName + 'SaveMidtermReview',
        method: 'post',
        data
    })
}

export function saveFinal(data) {
    return request({
        url: planServiceName + 'SaveFinal',
        method: 'post',
        data
    })
}
