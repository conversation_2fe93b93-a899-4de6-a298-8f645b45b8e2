/**
 *   参考：
 *       https://www.cnblogs.com/xiahj/p/vue-simple-uploader.html
 *       https://github.com/simple-uploader/Uploader/blob/develop/README_zh-CN.md
 *       https://github.com/simple-uploader/vue-uploader
 *       对于大文件，增量计算md5（不然ie11下会卡死）
 *       https://github.com/satazor/js-spark-md5
 *       https://www.jianshu.com/p/6cc9db0f2c7d
 */

<template>
<div>
    <!-- <div class="app-upload-file">
        <ul class="file-list clearfix" v-show="files.length > 0">
            <li
                v-for="(item, idx) in files"
                :key="idx"
                @mouseover="() => delIdx = idx"
                @mouseout="() => delIdx = -1"
            >
                <a>
                    <img
                        v-show="fileType == 1"
                        @click="handlePictureCardPreview(item[fileProp.filePath])"
                        class="file-item file-item-img"
                        alt="点击放大"
                        :src="item[fileProp.filePath]"
                    >
                    <video
                        style='width: 180px; height: 180px; background: #ccc;'
                        controls="controls"
                        v-show="fileType == 3"
                        :src="item[fileProp.filePath]"
                        >
                    </video>
                    <div>
                        <div v-show="fileType == 4" class="file-item file-item-other">
                            <i class="el-icon-document file-logo"></i>
                        </div>
                        <div class="file-info-warpper clearfix">
                            <div class="file-name" :style="{ width: fileType == 4 ? '120px' : '180px' }" :title="item.FileName">
                                <span>{{ item[fileProp.fileName] }}</span>
                            </div>
                            <div class="file-button">
                                <el-button type='text' @click="handleDownload(item[fileProp.filePath], item[fileProp.fileName],item)">下载</el-button>
                            </div>
                        </div>
                    </div>
                </a>
                <i
                    v-if="!readonly"
                    v-show="delIdx == idx"
                    class="file-btn-del el-icon-remove"
                    @click.stop="removeFile(idx)"
                ></i>
            </li>
        </ul>
    </div> -->

    <div
        v-loading='fileAnalysisLoading'
        element-loading-text="文件分析中..."
        >
        <uploader
            ref="uploader"
            :options="options"
            :file-status-text="statusText"
            :autoStart="false"
            @file-added="onFileAdded"
            @files-added="onFilesAdded"
            @file-complete="fileComplete"
            @file-progress="onFileProgress"
            @file-error="onFileError"
            @file-removed="onFileRemoved"
            @file-compression="onFileCompression"
            :key="uploaderKey"
            class="uploader"
        >
            <!-- <uploader-unsupport></uploader-unsupport> -->
            <uploader-drop v-show="showUploadBtn && isSupportDropUpload">
                <!-- <p>Drop files here to upload or</p>
                <uploader-btn :attrs="attrs">select images</uploader-btn>
                <uploader-btn :directory="false">select folder</uploader-btn> -->
                <!-- 将文件拖拽至此区域或者  -->
                <uploader-btn :directory="false" style="cursor: pointer; width: 100%;">
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 120px; ">
                        <svg-icon icon-class="上传" className="icon-big border-color-light"></svg-icon>
                        <div style="line-height: normal;">点击上传或将文件拖入此区域</div>
                        <div class="text-secondary" style="line-height: normal; font-size: 12px; text-align: center;">（注：不支持文件夹上传）</div>
                    </div>
                </uploader-btn>
            </uploader-drop>
            <uploader-unsupport>
                <template>您的浏览器不支持大文件上传，请改用其他（Chrome、QQ、360）浏览器或较高（IE10+）版本的IE浏览器，以获取更好的体验！</template>
            </uploader-unsupport>
            <uploader-list>
                <ul class="uploader-file-list" slot-scope="props">
                    <!-- <el-button type="primary">
                        <uploader-btn id="global-uploader-btn" :attrs="attrs" ref="uploadBtn" v-show="props.fileList.length < max">
                            上传附件<i class="el-icon-upload el-icon--right"></i>
                        </uploader-btn>
                    </el-button> -->

                    <el-button-group v-if="showUploadBtn && !isSupportDropUpload">
                      <!-- 文字按钮 -->
                      <uploader-btn v-if="isTextBtn" v-show="showUploadBtn" id="global-uploader-btn" :attrs="attrs" ref="uploadBtn" class="el-button el-button--text el-button--small">
                          上传附件
                      </uploader-btn>

                      <!-- 主题蓝色按钮  -->
                      <uploader-btn v-else v-show="showUploadBtn" id="global-uploader-btn" :attrs="attrs" ref="uploadBtn" class="el-button el-button--primary el-button--mini">
                          上传附件<i class="el-icon-upload el-icon--right"></i>
                      </uploader-btn>
                       
                        <!-- <el-button v-show="!readonly" type="text" @click="() => showUploadProgress = !showUploadProgress">
                            <span v-show="!showUploadProgress">显示</span><span v-show="showUploadProgress">隐藏</span>上传进度
                        </el-button> -->
                    </el-button-group>
                    <div>
                        <div class="uploader-list">
                            <fileItem :readonly='readonly' :crossItemView="true" @reviewFile="handleReview" @handleDelate='() => removeFile(idx)' v-for="(f, idx) in files" :fileObj='f' :key="f.Id"></fileItem>
                            <!-- <ul>
                                <li v-for="(f, idx) in files" :key="f.Id">
                                    <div status="success" class='uploader-file file_' :class="{'isDisabled': readonly}">
                                        <div class="uploader-file-progress" :class="{'isDisabled': readonly}" style="transform: translateX(0%);"></div> 
                                        <div class="uploader-file-info">
                                            <div class="uploader-file-name omit fl" :title="f.FileName">111-{{ f.FileName }}</div>
                                            <div class="uploader-file-size fr">
                                                {{ conver(f.FileSize) }}
                                            </div>
                                            <div class="uploader-file-actions fr">
                                                <i v-if="!readonly" class="el-icon-delete pointer" title="删除"  @click.stop="removeFile(idx)"></i>
                                                <i style="padding-right: 2px; cursor: pointer;" class="el-icon-download" title="下载" @click="handleDownload(f.Path, f.FileName, f)"></i>
                                                <i v-show="isImg(f)" style="cursor: pointer;" class="el-icon-view" title="查看" @click="handlePictureCardPreview(f)"></i>
                                            </div>
                                            <div class="uploader-file-status fr">
                                                <span style="display: none;"><span></span> <em></em>
                                                    <i></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul> -->
                        </div>

                        <li v-for="file in props.fileList" :key="file.id" v-show="isShowProcessItem(file)">
                            <uploader-file :class="'file_' + file.id" ref="files" :file="file" :list="true"></uploader-file>
                        </li>
                        <div class="no-file" v-if="!props.fileList.length">
                            <!-- <i class="iconfont icon-empty-file"></i>
                            暂无待上传文件 -->
                        </div>

                        <noData style="padding: 20px 0;" v-if="isShowNoDataTip && !props.fileList.length"></noData>
                    </div>
                </ul>
            </uploader-list>
        </uploader>

        <el-dialog :visible.sync="dialogVisible" :append-to-body="true">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</div>
</template>

<script>
import * as common from '@/api/common'
import { getToken } from '@/utils/auth'
import SparkMD5 from 'spark-md5'
import { downloadFile, sizeUnitConversion } from '@/utils/index'
import { serviceArea } from '../../api/serviceArea'
import fileItem from '@/components/fileItem'
import noData from "@/views/common/components/noData"
import { maps } from "@/utils/commonEnum";
import {debounce} from 'lodash'

export default {
    name: 'app-upload-big-file',
    components: {
        fileItem,
        noData,

    },
    props: {
        value: Array, // [{Id: '', Path: ''}]
        readonly: {
            type: Boolean,
            default: false
        },
        //上传文件最大数量
        max: {
            type: Number,
            default: 3
        },
        //已存在多少个文件
        // existsTotal: {
        //     type: Number,
        //     default: 0
        // },
        //上传文件格式限制（如果 fileType == 4，不校验格式——表示接受所有格式）
        accept: {
            type: String,
            default: 'jpg,jpeg,png',
        },
        fileType: {
            type: Number,
            default: 1, //1:图片;2:excel;3:视频;4:其他附件
        },
        //上传文件大小限制（默认不超过3mb）
        fileSize: {
            type: Number,
            default: 1024 * 1024 * 10,
        },
        //总大小限制（1024 * 1024 * 1024 = 1024mb）
        limitTotalSize: {
            type: Number,
            default: -1 //表示不限制
        },
        //外部（异步）检查重复接口
        checkDuplicate: {
            type: Function,
            default: null
        },
        isShowNoDataTip: {
            type: Boolean,
            default: false
        },
        //是否支持拖拽上传
        isSupportDropUpload: {
            type: Boolean,
            default: false
        },
        // 上传按钮是否文字按钮
        isTextBtn: {
            type: Boolean,
            default: false
        }
    },

    data () {
        return {
            maps: maps,
            dialogVisible: false,
            dialogImageUrl: '',
            // // // // uploadedList: [],
            uploadingFileCurrent: null,
            completedFiles: [],//已经上传完成的文件编号
            completedFilesDels: [],//已经上传完成，但是被删除的文件编号列表
            // existasTotalTmp: 0,
            delIdx: -1,
            files: [],
            uploading: false,
            fileAnalysisLoading: false, //大文件分析loading（大文件计算md5值）
            fileProp: {
              fileKey: 'Id',
              filePath: 'Path',
              fileName: 'FileName',
            },
            options: {
                categoryMap: {
                    image: ['gif', 'jpg', 'jpeg', 'png', 'bmp', 'webp'],
                    video: ['mp4', 'm3u8', 'rmvb', 'avi', 'swf', '3gp', 'mkv', 'flv'],
                    audio: ['mp3', 'wav', 'wma', 'ogg', 'aac', 'flac'],
                    document: ['doc', 'txt', 'docx', 'pages', 'epub', 'pdf', 'numbers', 'csv', 'xls', 'xlsx', 'keynote', 'ppt', 'pptx']
                },
                singleFile: false,
                fileParameterName: 'file',
                target: `/api${serviceArea.resource}/FileUpload/UploadChunk?t=` + Date.now(),
                testChunks: true,
                headers: {
                    'Token': getToken(),
                },
                query: function(file, chunk, isTestMode) { //为上传切片接口添加额外的参数
                    return {
                        // id: file.id,
                        type: file.fileType
                    }
                },
                // allowDuplicateUploads: true,//如果说一个文件以及上传过了是否还允许再次上传
                simultaneousUploads: 2,//并发上传数量
                chunkSize: 100 * 1024, // 100kb
                // 服务器分片校验函数，秒传及断点续传基础
                checkChunkUploadedByResponse: function (chunk, message) {
                    let objMessage = JSON.parse(message);
                    if (objMessage.SkipUpload) {
                        return true;
                    }
                    return (objMessage.Uploadeds || []).indexOf(chunk.offset + 1) >= 0
                },
                //切片上传成功后回调
                processResponse: (response, cb) => {
                    cb(null, response) //默认的，继续上传


// fileName: file.name, xxxx
// identifier: firstArg.uniqueIdentifier, xxxxx
// totalSize: file.size, xxxxxx
// type: file.type,

// chunkNumber: 53
// chunkSize: 102400
// currentChunkSize: 102400
// totalSize: 37764281
// identifier: 50d99695b2afd9870027105eaac1b339
// filename: b.mp4
// relativePath: b.mp4
// totalChunks: 368
// type: video/mp4
                    // this.statusText.success = '合并中'

                    // if(response.NeedMerge){ //上传完成，需要合并了
                    //     this.mergeChunks().then(err => {
                    //         //合并成功（业务代码）
                    //         cb(null, response) //默认的，继续上传
                    //     }).catch(err => {
                    //         cb(err, response) //合并失败
                    //     })
                    // }else{
                    //     cb(null, response) //默认的，继续上传
                    // }
                },
                //生成文件唯一标示的函数
                // generateUniqueIdentifier: function(file) {
                //     return file.name + 'xxxx'
                // }
            },
            attrs: {
                accept: []
            },
            statusText: {
                success: '',//上传成功
                error: '出错了',
                uploading: '上传中',
                paused: '暂停中',
                waiting: '等待中',
                compression: '文件压缩中',
            },
            showUploadProgress: true,
            //uploader 控件中包含的文件，用于监听每个文件的状态，供外部根据状态做业务判断
            totalSize: 0, //上传的附件总大小（kb）
            uploaderKey:new Date().getTime()
        }
    },
    computed: {
        imageList() {
          return this.files.filter(s => this.isImage(s.FileName));
        },
        // showUploadBtn() {
        //     // if(!this.options.singleFile){ //如果是单文件，组件会覆盖，不需要判断
        //         if(this.max > 0){
        //             //不考虑已经上传完成的（已经上传完成的会加到调用页面）
        //             let isCompletes = this.uploader.fileList.filter(i => this.completedFiles.findIndex(o => o == i.uniqueIdentifier) > -1)
        //             if(this.max < this.files.length + isCompletes.length){
        //                 return false
        //             }
        //         }
        //         return true
        //     // }
        // },
        showUploadBtn () {
            return (this.files.length < this.max) && !this.readonly
        },
        uploader() {
            return this.$refs.uploader.uploader;
        },
    },
    mounted () {

        this.attrs.accept = this.accept.split(',')
        this.initFiles(this.value)
    },
    watch: {
        value: {
            handler (newval) {
                this.initFiles(newval)
            },
            deep: true
        },
    },
    methods: {
        handleReview(path,fileType) {
          const findIndex = this.imageList.findIndex(t=>t.Path === path);
          
          if(fileType == 'image') {
            this.$viewerApi({
              options: {
                initialViewIndex: findIndex === -1 ? 0 :findIndex
              },
              images: this.imageList.map(t=>t.Path),
            })
          }else{
            window.open(path);
          }
        },
        isImage(fileName) {
          let fileSuffix = ''
          let idx = fileName.lastIndexOf('.')
          if(idx > -1) {
            fileSuffix = fileName.substr(idx + 1)
          }

          return this.getFileType(fileSuffix) == 'image'
        },
        getFileType(suffix) {
            let result = 'weizhiwenjian'
            for(let i = 0; i < maps.length; i++) {
                let obj = maps[i].list.find(s => s == suffix.toLowerCase())
                if(obj) {
                    result = maps[i].suffix
                    break
                }
            }
            return result
        },
        initFiles (data) {
            let self = this;
            this.files = data || []
            // if (_.isString(data)) {
            //     this.files = data.split(',').map(v => ({
            //         [self.fileProp.fileKey]: v.Id,
            //         [self.fileProp.filePath]: v.Path
            //     }))
            // } else {
            // }
            // // // // // this.uploadedList = data && data.map(f => {
            // // // // //     return {
            // // // // //         "isFolder": false,
            // // // // //         "isRoot": false,
            // // // // //         "id": f.Id,
            // // // // //         "fileType": "video/mp4",
            // // // // //         "name": f.FileName,
            // // // // //         "size": f.FileSize,
            // // // // //         "relativePath": f.FileName,
            // // // // //         "paused": false,
            // // // // //         "error": false,
            // // // // //         "allError": false,
            // // // // //         "aborted": false,
            // // // // //         "averageSpeed": 0,
            // // // // //         "currentSpeed": 0,
            // // // // //         "_lastProgressCallback": 1586227757144,
            // // // // //         "_prevUploadedSize": 30927525,
            // // // // //         "_prevProgress": 1,
            // // // // //         "uniqueIdentifier": f.FileMd5value,
            // // // // //         "_firstResponse": true,
            // // // // //         "_progeressId": 0
            // // // // //     }
            // // // // // })

            //计算已存在文件总体积
            if(this.files && this.files.length > 0){
                this.totalSize = (this.files.map(f => f.FileSize) || [0]).reduce(function(prev,cur,index,array){
                    return prev + cur
                }) //byte
            }
        },
        removeFile (index) {
            if(this.limitTotalSize > 0) {
                this.totalSize -= this.files[index].FileSize
                if(this.totalSize <= 0){
                    this.totalSize = 0
                }
            }

            let delFileId = this.files[index].FileMd5value
            if(this.completedFilesDels.indexOf(delFileId) == -1){
                this.completedFilesDels.push(delFileId)
            }

            this.files.splice(index, 1)
            this.existasTotalTmp = this.files.length
            
            let fileObj = this.uploader.fileList.find(file => file.uniqueIdentifier == delFileId)

            if(fileObj) {
                this.uploader.removeFile(fileObj)
            }

            this.filesChange('delete')
        },
        clearFiles() {
            // cance
            this.totalSize = 0
            this.completedFilesDels = []
            // for(let i = 0; i < this.files.length; i++) {
            //     let delFileId = this.files[i].FileMd5value
            //     let fileObj = this.uploader.fileList.find(file => file.uniqueIdentifier == delFileId)
            //     this.uploader.removeFile(fileObj)
            // }
            this.uploader.cancel()
            this.filesChange()
        },
        filesChange (optType) {
            this.$emit("change", this.files, optType)
        },
        conver: sizeUnitConversion,
        // conver(limit) {
        //     var size = "";
        //     if (limit < 0.1 * 1024) { //如果小于0.1KB转化成B
        //         size = limit.toFixed(2) + "B";
        //     } else if (limit < 0.1 * 1024 * 1024) {//如果小于0.1MB转化成KB
        //         size = (limit / 1024).toFixed(2) + "KB";
        //     } else if (limit < 1 * 1024 * 1024 * 1024) { //如果小于1GB转化成MB
        //         size = (limit / (1024 * 1024)).toFixed(2) + "MB";
        //     } else { //其他转化成GB
        //         size = (limit / (1024 * 1024 * 1024)).toFixed(2) + "GB";
        //     }
        //     var sizestr = size + "";
        //     var len = sizestr.indexOf("\.");
        //     var dec = sizestr.substr(len + 1, 2);
        //     if (dec == "00") {//当小数点后为00时 去掉小数部分
        //         return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
        //     }
        //     return sizestr;
        // },

        //如果不符合条件，忽略（不会加入到 onFilesAdded 事件参数中）
        onFileAdded(file) {
            // if(!this.beforeUpload(file)){
            //     file.ignored = true
            //     return false
            // }
        },
        //上传之前的事件
        beforeUpload (file) {


            let acceptStr = this.attrs.accept || ''
            let fileSize = this.fileSize //单文件大小限制
            // let limitTotalSize = this.limitTotalSize //总大小限制

            let accept = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase(),
                typePass = ~acceptStr.indexOf(accept) || !acceptStr || acceptStr == 'all',
                sizePass = file.size < fileSize || !fileSize
                // maxLimit = this.files.length < this.max;
                // totalSizeLimit = !(limitTotalSize > 0) ? true : limitTotalSize >= (file.size + this.totalSize)

            // let totalSizeLimitNotice = this.conver(limitTotalSize)

            !typePass && this.typeErrorMsg();
            !sizePass && this.sizeErrorMsg();
            // !totalSizeLimit && this.$message.error(`上传文件总大小不能超过 ${totalSizeLimitNotice}!`);

            // !maxLimit && this.$message.error(`上传文件不能超过 ${this.max} 张!`)
            if (typePass && sizePass) this.uploading = true
            return typePass && sizePass;
        },
        
        sizeErrorMsg:debounce(function(){
          const size =  this.conver(this.fileSize)
          this.$message.error(`上传文件大小不能超过${size}!`);
        },1000,{leading:true,trailing:false}),

        typeErrorMsg:debounce(function(){
          const acceptStr = this.attrs.accept || ''
          this.$message.error(`上传文件只能是 ${acceptStr} 格式!`);
        },1000,{leading:true,trailing:false}),



        async onFilesAdded(files, fileList) {

            let isValidate = true //单个文件校验是否通过

            // let contentFold = files.find(s => s.relativePath.indexOf('/') > -1)
            // if(contentFold) {

            //     files.ignored = true
            //     isValidate = false

            //     let filteredFiles = files.filter(s => s.relativePath.indexOf('/') == -1)
            //     if(filteredFiles && filteredFiles.length > 0) {
            //         this.$refs.uploader.uploader.addFiles(filteredFiles, filteredFiles);
            //     }

            //     return false
            // }


            //如果选中文件包含路径符号，表示选中项中包含文件夹，不支持，提示重新选择
            if(files.find(s => s.relativePath.indexOf('/') > -1)) {
                // this.$message.error('不支持文件夹上传，请从新选择')
                files.ignored = true
                isValidate = false
                return false
            }


            if(files && files.length > 0){
                files.forEach(file => {
                    if(!this.beforeUpload(file) && isValidate){
                        files.ignored = true
                        isValidate = false
                        this.uploaderKey = new Date().getTime()
                        return false
                    }
                })
            }

            if(!isValidate){
                this.fileAnalysisLoading = false
                this.uploader.cancel()
                return
            }

            //文件总数量限制校验
            if(this.max < this.files.length + files.length){
                this.$message.warning(`不能超过${this.max}个文件！`)
                files.ignored = true
                // 清除文件分析loading状态
                this.fileAnalysisLoading = false
                // 取消当前选择的所有文件
                this.uploader.cancel()
                this.uploaderKey = new Date().getTime()
                return
            }

            //文件总提交大小校验
            let limitTotalSize = this.limitTotalSize //总大小限制
            if(limitTotalSize > 0) {  //如果有总文件大小限制
                //当前文件列表总大小
                let currFilesSize = (files.map(f => f.size) || [0]).reduce(function(prev,cur,index,array){
                    return prev + cur
                }) //byte

                if(limitTotalSize >= this.totalSize + currFilesSize){ //没有超过大小限制
                    this.totalSize += currFilesSize
                    this.computeMD5(files)
                }else{
                    files.ignored = true
                    let totalSizeLimitNotice = this.conver(limitTotalSize)
                    this.$message.error(`上传文件总大小不能超过 ${totalSizeLimitNotice}!`);
                }
            }else{
                this.computeMD5(files)
            }
        },
        //上传中
        onFileProgress(rootFile, file, chunk) {
            this.uploadingFileCurrent = file

            // console.log(`上传中 ${file.name}，chunk：${chunk.startByte / 1024 / 1024} ~ ${chunk.endByte / 1024 / 1024}`)
        },
        onFileCompression() {
            //文件处于压缩中，借用“文件分体弹框”（假装文件在分析，用户不一定关注文件当前所处状态）
            // this.fileAnalysisLoading = true;
        },
        onFileRemoved(file) {
            if(this.limitTotalSize > 0) {
                //删除上传日志时（进度条），判断当前文件是否是上传成功的文件。如果只是删除日志，则不减少（已上传）文件总大小。
                let idx = this.files.findIndex(f => f.FileMd5value == file.uniqueIdentifier)
                if(idx > -1){
                    return
                }

                this.totalSize -= file.size
                if(this.totalSize <= 0){
                    this.totalSize = 0
                }
            }
        },
        fileComplete() {
            const firstArg = arguments[0]
            const file = firstArg.file

            this.statusSet(firstArg.id, 'merging')
            common.mergeChunks({
                fileName: file.name,
                identifier: firstArg.uniqueIdentifier,
                totalSize: file.size,
                type: file.type,
                // fileType: 4
            }, {
                timeout: 300 * 1000
            }).then(res => {
                this.files = this.files.concat(res)
                this.completedFiles.push(firstArg.uniqueIdentifier)

                let delFileIdx = this.completedFilesDels.findIndex(s => s == firstArg.uniqueIdentifier)
                if(delFileIdx != -1) {
                    this.completedFilesDels.splice(delFileIdx, 1)
                }
                this.onFileRemoved(file)
                this.filesChange()
                this.statusSet(firstArg.id, 'merged')
            }).catch(err => {
                this.statusSet(firstArg.id, 'mergeFailed')
            })
        },
        onFileError(rootFile, file, response, chunk) {
            this.$message({
                message: response,
                type: 'error'
            })
        },
        /**
         * 计算md5，实现断点续传及秒传
         * @param file
         */
        computeMD5(files) {
            let flag = 0

            if(files && files.length > 0) {
                this.fileAnalysisLoading = true

                // let chunkSize = this.options.chunkSize;
                let chunkSize = 1024 * 1024 * 0.5; //不需要根据分片设置切片的大小（主要考虑计算耗时——分片多，计算少，分片大，计算可能会卡死（IE））
                let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice

                files.forEach(file => {
                    let fileReader = new FileReader();
                    let spark = new SparkMD5.ArrayBuffer();
                    let currentChunk = 0;
                    let chunks = Math.ceil(file.size / chunkSize);

                    console.log('总共分为多少：' + chunks)

                    // file.name += ' (文件分析中...)'
                    file.pause();

                    fileReader.onload = (e => {
                        spark.append(e.target.result); // Append array buffer
                        currentChunk++;
                        //使用文件首尾两个分片计算md5
                        // if(currentChunk == 0){
                        //     currentChunk = chunks - 1
                        // }else{
                        //     currentChunk++
                        // }

                        if (currentChunk < chunks) {
                            loadNext();
                        } else {
                            file.uniqueIdentifier = spark.end(); // Compute hash
                            // file.name = file.name.substr(0, file.name.lastIndexOf(' (文件分析中...)'))
                            console.log(777,file.uniqueIdentifier)
                            //所选所有文件md5计算完毕，结束loading
                            flag++
                            if(flag == files.length){
                                this.fileAnalysisLoading = false
                                this.CompreVerification(files)
                            }
                        }
                    });

                    fileReader.onerror = function () {
                        console.log('FileReader onerror was triggered, maybe the browser aborted due to high memory usage.');
                    };
                    
                    function loadNext() {
                        var start = currentChunk * chunkSize,
                            end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize;
                        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end));
                    }
                    loadNext();
                })
            }
        },
        //综合验证（外部验证和内部当前段中的文件验证）
        async CompreVerification(files) {
            let isDup = false
            if(this.checkDuplicate) {
                await this.checkDuplicate(files).then(res => {
                    isDup = res
                })
            }
            if(isDup) {
                this.uploader.cancel()
                this.$message({
                    message: '选中的文件中包含重复文件，请勿上传重复附件',
                    type: 'error'
                })
            }else{
                this.validDuplicate(files)
            }
        },
        //判断是否有重复文件
        validDuplicate(files) {
            let newIds = files.map(f => f.uniqueIdentifier) //最新选择的文件
            let oldIds = this.value ? this.value.map(f => f.FileMd5value) : [] //已上传成功的文件
            let newIdsDup = (new Set(newIds)).size != newIds.length //新上传文件是否存在重复文件（true：重复；false：不重复）
            //新上传文件和已上传文件是否存在重复（当前上传的和已上传成功的比较）
            let newAndOldidsDup = _.intersection(newIds, oldIds).length > 0
            //也需要和未上传成功的，但是已经加入到上传列表中的文件比较
            if(!newAndOldidsDup){
                //需要排除已上传成功的，但是被删除的，并且在上传进度面板中还存在的
                let listOfProgressPanel = this.uploader.fileList.filter(s => this.completedFilesDels.indexOf(s.uniqueIdentifier) == -1)
                let group = _.groupBy(listOfProgressPanel, 'uniqueIdentifier');
                for(var g in group){
                    if(newAndOldidsDup){
                        return false
                    }
                    newAndOldidsDup = group[g].length >= 2
                }
            }
            //重复文件错误提示
            let dupMsg = newIdsDup ? '选中的文件中包含重复文件，请勿上传重复附件' : newAndOldidsDup ? '所选文件和已上传文件存在重复，请重新选择' : ''

            //判断选中的文件中是否有重复的
            if(newIdsDup || newAndOldidsDup){
                this.$message({
                    message: dupMsg,
                    type: 'error'
                })
                //此时重复文件已经加入到ui中，需要手动删除(无法用files.ignored = true来忽略)
                files.forEach(f => {
                    let fileObj = files.find(file => file.id == f.id)
                    this.uploader.removeFile(fileObj)
                })
            }else{
                //所有文件md5计算完毕，循环启动文件开始上传
                files.forEach(f => {
                    setTimeout(() => {
                        f.resume();
                    }, 100)
                })
            }
        },
        /**
         * 新增的自定义的状态: 'md5'、'transcoding'、'failed'
         * @param id
         * @param status
         */
        statusSet(id, status) {
            let statusMap = {
                merging: {
                    text: '处理中',
                },
                merged: {
                    text: '上传成功',
                },
                mergeFailed: {
                    text: '处理失败',
                }
            }
            this.$nextTick(() => {
                $(`.file_${id} .uploader-file-status span:first`).text(statusMap[status].text)
            })
        },
        statusRemove(id) {
            this.$nextTick(() => {
                $(`.myStatus_${id}`).remove();
            })
        },
        isShowProcessItem(file) {
            let newFileId = file.uniqueIdentifier
            

            return this.files.findIndex(s => s.FileMd5value == newFileId) == -1
            // let _fileList = this.files

        },
        handleDownload(url, filename,o) {
          //   url=`${serviceArea.resource}/FileUpload/Download?id=${o.Id}`
          //  if(!url){
          //       this.$message({
          //           message: '下载地址错误',
          //           type: 'error'
          //       })
          //       return
          //   }
          //   setTimeout(() => {
          //       downloadFile(url, filename)
          //   }, 1000)

            // 第二种下载方式，使用文件流下载
            var dowFileUrl=`api${serviceArea.resource}/FileUpload/Download?id=${o.Id}`
            window.open(dowFileUrl);
            // if(url) {
            //     window.open(url);
            // }

            // var instance = axios.create({
            //   baseURL: 'api',
            //   timeout: 300000,
            //   headers: {'Token': getToken()}
            // });

            // instance.get(dowFileUrl, {
            //     responseType:'blob'
            // }).then(res => {
            //     let blob = new Blob([res.data],{
            //               type:res.data.type    //将会被放入到blob中的数组内容的MIME类型
            //             });
            //     let objectUrl = URL.createObjectURL(blob);  //生成一个url
            //     let a = document.createElement("a");
            //     a.href =objectUrl
            //     a.download = res.headers.filename
            //     a.click();
            // });
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.Path;
            this.dialogVisible = true;
        },
        isImg(file) {
            let fileType = file.FileName.substring(file.FileName.lastIndexOf('.') + 1)
            let typeMap = this.options.categoryMap
            let type = ''
            Object.keys(typeMap).forEach((_type) => {
                const extensions = typeMap[_type]
                if (extensions.indexOf(fileType) > -1) {
                    type = _type
                }
            })
            return type == 'image'
        },
    }
}
</script>

<style scoped>

/**/

.file-list {
    display: inline-block;
    margin-right: 10px;
    vertical-align: middle;
    line-height: 1;
    list-style: none;
    padding-left: 0;

    box-sizing: border-box;
    margin: 0!important;
}

.file-list li {
    float: left;
    position: relative;
    margin-right: 10px;
}

.file-item {
    display: inline-block;
    position: relative;
    width: 120px;
    height: 120px;
}

.file-item-img{
    cursor: pointer;
}
.file-item-other{
    cursor: auto;
    background-color: #efefef;
}
.file-logo{
    font-size: 40px;
    text-align: center;
    position: absolute;
    margin-top: -20px;
    top: 50%;
    margin-left: -20px;
    left: 50%;
}

.file-img {
    display: inline-block;
    padding: 2px;
    border-radius: 4px;
    vertical-align: middle;
    border-width: 1px;
    border-style: dashed;
}

.file-btn-del {
    background: transparent;
    display: block;
    cursor: pointer;
    position: absolute;
    font-size: 18px;
    top: -6px;
    right: -6px;
}

.file-btn-del:hover {
    transition: all 0.3s;
    color: red;
}

.upload-btn {
    position: relative;
    vertical-align: middle;
}

.icon-btn {
    width: 120px;
    height: 120px;
    padding: 0;
    font-size: 0;
    background: none;
    border-style: dashed;
    text-align: center;
}

.icon-plus {
    font-size: 24px;
    color: #8c939d;
}


/*上传组件相关*/
.uploader{
    width: 100%;
}

.uploader-drop{
    box-sizing: border-box;
}

.file-list{
    list-style-type: none;
    padding-left: 0;
}

.no-file{
    text-align: center;
}

.file-info-warpper{
    position: relative;
}

.file-name{
    padding: 7px 0;
    padding-right: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-button{
    position: absolute;
    right: 0;
    top: 0;
}


.uploader-file {
    position: relative;
    /* height: 24px;
    line-height: 24px; */
    overflow: hidden;
    /* border-bottom: 1px solid #cdcdcd; */
}

/* .uploader-file.isDisabled{
    border-bottom: 1px solid #edeff3;
} */

.uploader-file[status="waiting"] .uploader-file-pause,
.uploader-file[status="uploading"] .uploader-file-pause {
    display: block;
}
.uploader-file[status="paused"] .uploader-file-resume {
    display: block;
}
.uploader-file[status="error"] .uploader-file-retry {
    display: block;
}
.uploader-file[status="waiting"] .uploader-file-remove {
    display: block;
}
/* .uploader-file[status="success"] .uploader-file-remove {
display: none;
} */
.uploader-file[status="error"] .uploader-file-progress {
    background: #ffe0e0;
}
.uploader-file-progress {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #e2eeff;
    transform: translateX(-100%);
}
.uploader-file-progress.isDisabled{
    background: #fafbfc;
}
.uploader-file-progressing {
    transition: all .4s linear;
}
.uploader-file-info {
    position: relative;
    z-index: 1;
    height: 100%;
    overflow: hidden;
}
.uploader-file-info:hover {
    background-color: rgba(240, 240, 240, 0.2);
}
.uploader-file-info i,
.uploader-file-info em {
    font-style: normal;
}
.uploader-file-name,
.uploader-file-size,
.uploader-file-meta,
.uploader-file-status,
.uploader-file-actions {
    position: relative;
    height: 100%;
    margin:0 10px;
}
.uploader-file-name {
    width: 35%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.uploader-file-name a:hover {
    /* text-decoration: underline; */
    cursor: auto;
}
.uploader-file-icon {
    width: 24px;
    height: 24px;
    display: inline-block;
    vertical-align: top;
    margin-top: 13px;
    margin-right: 8px;
}
.uploader-file-icon::before {
    content: "📃";
    display: block;
    height: 100%;
    font-size: 24px;
    line-height: 1;
}
.uploader-file-icon[icon="folder"]::before {
    content: "📂";
}
.uploader-file-icon[icon="image"]::before {
    content: "📊";
}
.uploader-file-icon[icon="video"]::before {
    content: "📹";
}
.uploader-file-icon[icon="audio"]::before {
    content: "🎵";
}
.uploader-file-icon[icon="document"]::before {
    content: "📋";
}
.uploader-file-size {
    width: 13%;
}
.uploader-file-meta {
    width: 8%;
}
.uploader-file-status {
    max-width:90px;
}
.uploader-file-actions {
    max-width:70px;
}
/*.uploader-file-actions > span {
    display: none;
    float: left;
    width: 16px;
    height: 16px;
    margin-top: 10px;
    margin-right: 4px;
    cursor: pointer;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAABkCAYAAAD0ZHJ6AAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAJcEhZcwAACxMAAAsTAQCanBgAAARkSURBVGje7ZnfS1NRHMAH4ptPkvQSuAdBkCxD8FUQJMEULUgzy1KyyPVQ4JMiiP4Bvg6EwUQQfMmwhwRDshwaKUjDVCgoSdDNHkzTJZ6+Z37Purve8+PeTb2TM/ggu+ew89l33x8H9BBCPG7GowXTJej3+wnDvEm0JuLC04+EYWftVAUv+fiCvDUdQR1BHUEdQR3BTIygvixoQS14XgTtthLVdpNWwXRLqvQ724LplFRtyrYF0yVpFLQrKRVMh6RZ0I6kkmCqklaCqpKZH0FX56Crq9jVfdDVk0RfFrSgFsxkQVmLcdKCVrKySCrryhPEyYShhzOcrFtG0EoilfHHk1CRU5rF6ZjNZhlVOW6RnMSVyyilKies4pO41diVy8wIujoHXV3FGdMHXTtJKLFYTLhZtq4vC1rwXApCZTIqgR6g1PBMCO9DL3bMMSqBHqDU8EyISDAHiGKvWwcCQG2KgjlAFCDAOhAAap0K5gKLphk8mqJgLrCIgoxRJ4J5wKpJ7gAoMkn5EBXBPGDVJHcAFJmkfIhQcAql1oBpTvTol9gG9pm4RHAKpdaAaU706JfYBvaZuJVgPQrt4sFlnOh5MC/p3lmJYD0K7eLBZZzoeTAv6d5ZnuAYHjpgEOnk5F0ufhG6v1ggOIaHDhhEOjl5l4tfhO4vthLcwAMrFNvLJO5vEwhu4IEViu1lEve3WQmyoihQFBzG/V0CQVYUBYqCw7i/SxTBcpsRbFeIYLnNCLZbCY5b5KAnxRwct8hBj9McZFVMW0ihRNBuFdMWUigRlFaxuQ9WWYjRMTiIe5z0wSoLMToGB3GPsA9aTZIJoB+nRgBnM1tzOkkmgH6cGgGczWzNpzqLx3n/aULJJgezeNw07oxQySbVywKjBOgFRnDs+VEsx8FlgVEC9AIjOPb8KJYjvSzoG7UW1IJaUAtqQS14toLNM5fN5APdwBJA8G83Pk/aK/rgzVvXzeQD3cASQPBvNz5P2ssTzAaGUIrHEO6zI5gNDKEUjyHcxxWkh4Ylcowwk1QQpIeGJXKMMJO0EgwqyjGCioJBJvDrxRMSuVOTJEXfbz1/bHwWtBL0yoQehK6RucgE+bGzanzulQh6E3IgQV+xpc8kcrfuSO7eTfJ3ZYmQw0Oy9azVKOk1C/bJ5D5F38YPeLfx0rjWJxHsS0SqsSYuxySjj5qO5Oj7xQWy2VBtFOwzCy6ryH3YfE3uh64Y1xckgstJPydEjkkeHv07Iy4Xaao15+KCWTBx6M/db+T9xivSErqaJDdzXI6yLRE8Vgg0coex/SPJvT0SbWu0KpZtbgSpCH3NRt7I5OxHkObc6heU+/M/J5vrpBFM5GBLqCQux14COXs5CNXK5OjPGm1tSMrJSOMNYQ4mVTGV/L6zTL7+DovkbFUxbSW0Wo05l8hJWsU+cRWfSh+Mt5Lb1ck/J1TvVsdDaR/MiEni+llsdZuZp62EViu+96bpNjNPWwmtVnzvFd5m9IVVC54x/wA7gNvqFG9vXQAAAABJRU5ErkJggg==") no-repeat 0 0;
}
.uploader-file-actions > span:hover {
    background-position-x: -21px;
}*/
.uploader-file-actions > i{
  font-size: 16px;
  font-weight: 900;
  color:#9a9a9a;
}
.uploader-file-actions > i:hover {
    color:#3468CD;
}
.uploader-file-actions .uploader-file-pause {
    background-position-y: 0;
}
.uploader-file-actions .uploader-file-resume {
    background-position-y: -17px;
}
.uploader-file-actions .uploader-file-retry {
    background-position-y: -53px;
}
.uploader-file-actions .uploader-file-remove {
    display: block;
    background-position-y: -34px;
}

.uploader-list {
    position: relative;
}
.uploader-list > ul {
    list-style: none;
    margin: 0;
    padding: 0
}
</style>
