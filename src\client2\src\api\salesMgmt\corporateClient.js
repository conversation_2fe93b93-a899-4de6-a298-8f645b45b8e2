import request from "@/utils/request";
import { serviceArea } from "../serviceArea";

let serviceAreaName = serviceArea.business + "/CorporateClient/";

export function add(data) {
  return request({
    url: serviceAreaName + "Add",
    method: "post",
    data,
  });
}

export function del(data) {
  return request({
    url: serviceAreaName + "Delete",
    method: "post",
    data,
  });
}

export function edit(data) {
  return request({
    url: serviceAreaName + "Edit",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: serviceAreaName + "GetListPage",
    method: "post",
    data,
  });
}

export function detail(params) {
  return request({
    url: serviceAreaName + "GetDetails",
    method: "get",
    params,
  });
}

export function getStatistics(params) {
  return request({
    url: serviceAreaName + "GetStatistics",
    method: "get",
    params,
  });
}


export function getOrganizationalDiagram(data) {
  return request({
    url: serviceAreaName + "OrganizationalDiagram",
    method: "post",
    data,
  });
}

export function getDynamicState(params) {
  return request({
    url: serviceAreaName + "GetDynamicState",
    method: "get",
    params,
  });
}


export function relationshipDiagram(params) {
  return request({
      url: serviceAreaName + 'RelationshipDiagram',
      method: 'get',
      params
  })
} 

export function getDepartmentsList(params) {
  return request({
      url: serviceAreaName + 'GetDepartmentsList',
      method: 'get',
      params
  })
} 

export function getCustomerProCardList(params) {
  return request({
      url: serviceAreaName + 'GetCustomerProCardList',
      method: 'get',
      params
  })
} 

export function checkName(data) {
  return request({
    url: serviceAreaName + "CheckName",
    method: "post",
    data,
  });
}
