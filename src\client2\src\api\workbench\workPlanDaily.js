import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/WorkPlanDaily/'



export function getListPage(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
//新增日报
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

//获取上一次写日报的抄送人
export function loadLast(params) {
    return request({
        url: serviceAreaName + 'LoadLast',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}


export function detail(params) {
    return request({
        url: serviceAreaName + 'getDetails',
        method: 'get',
        params
    })
}

export function getListByEmpId(params) {
    return request({
        url: serviceAreaName + 'GetList',
        method: 'get',
        params
    })
}

export function importLastReport(params) {
    return request({
        url: serviceAreaName + 'ImportLastReport',
        method: 'get',
        params
    })
}

export function loadLastTemplate(params) {
    return request({
        url: serviceAreaName + 'LoadLastTemplate',
        method: 'get',
        params
    })
}


export function getStatistics(data) {
    return request({
        url: serviceAreaName + 'Statistics',
        method: 'post',
        data
    })
}


export function getNotSubmittedEmployee(data) {
    return request({
        url: serviceAreaName + 'NotSubmittedEmployee',
        method: 'post',
        data
    })
}


export function getEmployee(params) {
    return request({
        url: serviceAreaName + 'GetEmployee',
        method: 'get',
        params
    })
}


export function getListPageBoardNew(data) {
    return request({
        url: serviceAreaName + 'ListPageBoardNew',
        method: 'post',
        data
    })
}


export function getListPageBoardInfo(data) {
    return request({
        url: serviceAreaName + 'ListPageBoardInfo',
        method: 'post',
        data
    })
}

export function getToDay(params) {
    return request({
        url: serviceAreaName + 'GetToDay',
        method: 'get',
        params
    })
}

