import request from '@/utils/request'
import { getToken } from '@/utils/auth' // 验权
let md5 = require('js-md5');

import { serviceArea } from './serviceArea'

let userServiceAreaName = serviceArea.user

// import { S_IXGRP } from 'constants'

// export function login(username, password) {
//     return request({
//         url: '/check/login',
//         method: 'post',
//         data: {
//             Account: username,
//             Password: password,
//             AppKey: 'openauth'
//         }
//     })
// }

// export function getInfo(token) {
//   return request({
//     url: '/check/getusername',
//     method: 'get',
//     params: { token }
//   })
// }



export function getModules(rId) {
    return request({
        url: userServiceAreaName + '/Modules/GetModules',
        method: 'get',
        params: { token: getToken(), roleId: rId }
    })
}

export function getModulesTree() {
    return request({
        url: userServiceAreaName + '/Modules/GetModulesTree',
        method: 'get',
        params: {}
    })
}

// export function getOrgs() {
//     return request({
//         url: '/Check/GetOrgs',
//         // url: '/Organization/GetOrganizations',
//         method: 'get',
//         params: { token: getToken() }
//     })
// }

export function getSubOrgs(orgId) {
    return request({
        url: userServiceAreaName + '/Check/GetSubOrgs',
        method: 'get',
        params: { orgId: orgId }
    })
}

export function logout() {
    return request({
        url: userServiceAreaName + '/Check/Logout',
        method: 'post'
    })
}

export function sign(username, password, enterpriseCode, experiencePage) {
    return request({
        url: userServiceAreaName + '/Check/Sign',
        method: 'post',
        data: {
            Account: username,
            Password: md5(password).toUpperCase(),
            EnterpriseCode: enterpriseCode,
            type: 1,
            experiencePage: experiencePage || false
        }
    })
}