import request from "@/utils/request";
import { serviceArea } from "./serviceArea";

let serviceAppraName = serviceArea.business + "/Kingdee";

export function getKingdeeDepartmentApi(params) {
  return request({
    url: serviceAppraName + "/GetKingdeeDepartment",
    method: "get",
    params,
  });
}

export function getKingdeeProjectApi(params) {
  return request({
    url: serviceAppraName + "/GetKingdeeProject",
    method: "get",
    params,
  });
}
