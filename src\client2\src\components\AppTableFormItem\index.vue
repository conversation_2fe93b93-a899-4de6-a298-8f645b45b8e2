<template>
  <el-col class="table-form-item-col" :sm="sm" :xs="24">
    <el-form-item v-bind="$attrs" :label="label" style="width: 100%;" ref="formItem">
      <slot></slot>
    </el-form-item>
  </el-col>
</template>

<script>
export default {
  name: "app-table-form-item",
  props: {
    label: {
      type: String,
      default: "",
    },
    labelWidth: {
      type: Number,
      default: 100,
    },
    //用于控制table搜索表单中的一项的显示方式（三列或独占一行）
    sm: {
      type: Number,
      default: 8,
    },
    /**
     * 查询条件区域布局方式：
     * classic：经典模式，最老的布局方式
     * simple：简易模式，只显示一个查询条件（综合输入框），其他查询条件下拉展示，后面跟着表格批量操作按钮区块；
     */
    layoutMode: {
      type: String,
      default: "classic",
    },
  },
  data() {
    return {};
  },
  mounted() {
    if (
      this.$refs.formItem.$el.firstChild.style &&
      this.$refs.formItem.$el.firstChild.nextSibling.style
    ) {
      var dynamicWidth =
        "calc( " +
        this.$refs.formItem.$el.style.width +
        " - " +
        this.$refs.formItem.$el.firstChild.style.width +
        " )";
      this.$refs.formItem.$el.firstChild.nextSibling.style.width = dynamicWidth;
    }
  },
};
</script>

<style>
/* .table-form-item-col .el-form-item__content {
   width: calc(100% - 100px); 
} */
</style>
