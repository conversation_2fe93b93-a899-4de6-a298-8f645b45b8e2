import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + '/Param'

/******************************************** 基础参数 ********************************************/
/**新增基础参数 */
export function addParam(data) {
    return request({
        url: serviceAreaName + '/AddParam',
        method: 'post',
        data
    })
}

/**删除基础参数 */
export function deleteParam(data) {
    return request({
        url: serviceAreaName + '/DeleteParam',
        method: 'post',
        data
    })
}

/**修改基础参数 */
export function editParam(data) {
    return request({
        url: serviceAreaName + '/EditParam',
        method: 'post',
        data
    })
}

/**获取基础参数详情 */
export function getParamDetails(params) {
    return request({
        url: serviceAreaName + '/GetParamDetails',
        method: 'get',
        params
    })
}

/**获取基础参数分页列表 */
export function getParamListPage(data) {
    return request({
        url: serviceAreaName + '/GetParamListPage',
        method: 'post',
        data
    })
}

/**获取条件查询基础参数 */
export function getParamListByCondition(data) {
    return request({
        url: serviceAreaName + '/GetParamListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 参数节点 ********************************************/
/**新增参数节点 */
export function addParamNode(data) {
    return request({
        url: serviceAreaName + '/AddParamNode',
        method: 'post',
        data
    })
}

/**删除参数节点 */
export function deleteParamNode(data) {
    return request({
        url: serviceAreaName + '/DeleteParamNode',
        method: 'post',
        data
    })
}

/**修改参数节点 */
export function editParamNode(data) {
    return request({
        url: serviceAreaName + '/EditParamNode',
        method: 'post',
        data
    })
}

/**获取参数节点详情 */
export function getParamNodeDetails(params) {
    return request({
        url: serviceAreaName + '/GetParamNodeDetails',
        method: 'get',
        params
    })
}

/**获取参数节点分页列表 */
export function getParamNodeListPage(data) {
    return request({
        url: serviceAreaName + '/GetParamNodeListPage',
        method: 'post',
        data
    })
}

/**获取条件查询参数节点类型 */
export function getParamNodeListByCondition(data) {
    return request({
        url: serviceAreaName + '/GetParamNodeListByCondition',
        method: 'post',
        data
    })
}

/******************************************** 参数单位 ********************************************/
/**新增参数单位 */
export function addParamUnit(data) {
    return request({
        url: serviceAreaName + '/AddParamUnit',
        method: 'post',
        data
    })
}

/**删除参数单位 */
export function deleteParamUnit(data) {
    return request({
        url: serviceAreaName + '/DeleteParamUnit',
        method: 'post',
        data
    })
}

/**修改参数单位 */
export function editParamUnit(data) {
    return request({
        url: serviceAreaName + '/EditParamUnit',
        method: 'post',
        data
    })
}

/**获取参数单位详情 */
export function getParamUnitDetails(params) {
    return request({
        url: serviceAreaName + '/GetParamUnitDetails',
        method: 'get',
        params
    })
}

/**获取参数单位分页列表 */
export function getParamUnitListPage(data) {
    return request({
        url: serviceAreaName + '/GetParamUnitListPage',
        method: 'post',
        data
    })
}

/**获取条件查询的参数单位 */
export function getParamUnitListByCondition(data) {
    return request({
        url: serviceAreaName + '/GetParamUnitListByCondition',
        method: 'post',
        data
    })
}
