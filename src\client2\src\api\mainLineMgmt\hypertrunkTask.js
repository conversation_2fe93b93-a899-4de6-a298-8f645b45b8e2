import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkTask/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function followUp(data) {
    return request({
        url: serviceAreaName + 'FollowUp',
        method: 'post',
        data
    })
}

/**
 * 新版跟进接口
 * @param {*} data 
 * @returns 
 */
export function followReport(data) {
    return request({
        url: serviceAreaName + 'FollowReport',
        method: 'post',
        data
    })
}

export function createApproval(data){
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function addApproval(data){
    return request({
        url: serviceAreaName + 'AddApproval',
        method: 'post',
        data
    })
}
export function editApproval(data){
    return request({
        url: serviceAreaName + 'EditApproval',
        method: 'post',
        data
    })
}

export function approvalAbandon(data){
    return request({
        url: serviceAreaName + 'ApprovalAbandon',
        method: 'post',
        data
    })
}

export function approvalFail(data){
    return request({
        url: serviceAreaName + 'FailApproval',
        method: 'post',
        data
    })
}

export function reCheck(data) {
    return request({
        url: serviceAreaName + 'ReCheck',
        method: 'post',
        data
    })
}



export function restart(data){
    return request({
        url: serviceAreaName + 'Restart',
        method: 'post',
        data
    })
}

//新增、编辑撤销
export function revocation(data){
    return request({
        url: serviceAreaName + 'Revocation',
        method: 'post',
        data
    })
}

export function batchAddTree(data){
    return request({
        url: serviceAreaName + 'BatchAddTree',
        method: 'post',
        data
    })
}

export function batchAddEditTree(data){
    return request({
        url: serviceAreaName + 'BatchAddEditTree',
        method: 'post',
        data
    })
}

export function getDraftHypertrunk(params){
    return request({
        url: serviceAreaName + 'GetDraftHypertrunk',
        method: 'get',
        params
    })
}

export function getPrincipalEmployee(params){
    return request({
        url: serviceAreaName + 'GetPrincipalEmployee',
        method: 'get',
        params
    })
}

export function abandon(data){
    return request({
        url: serviceAreaName + 'Abandon',
        method: 'post',
        data
    })
}

export function fail(data){
    return request({
        url: serviceAreaName + 'Fail',
        method: 'post',
        data
    })
}

export function emergencyHandleSheet(data){
    return request({
        url: serviceAreaName + 'EmergencyHandleSheet',
        method: 'post',
        data
    })
}

export function getListNew(data){
    return request({
        url: serviceAreaName + 'GetListPageNew',
        method: 'post',
        data
    })
}

export function editReportFrequency(data){
    return request({
        url: serviceAreaName + 'EditReportFrequency',
        method: 'post',
        data
    })
}


export function bindAssociation(data){
    return request({
        url: serviceAreaName + 'BindAssociation',
        method: 'post',
        data
    })
}


export function calculateTimeDifference(data){
    return request({
        url: serviceAreaName + 'CalculateTimeDifference',
        method: 'post',
        data
    })
}



export function getListPageTemplate(data){
    return request({
        url: serviceAreaName + 'GetListPageTemplate',
        method: 'post',
        data
    })
}


export function addTemplate(data){
    return request({
        url: serviceAreaName + 'AddTemplate',
        method: 'post',
        data
    })
}

export function editTemplate(data){
    return request({
        url: serviceAreaName + 'EditTemplate',
        method: 'post',
        data
    })
}

export function getTemplateDetails(params) {
    return request({
        url: serviceAreaName + 'GetTemplateDetails',
        method: 'get',
        params
    })
}

export function editTemplateEnabled(data){
    return request({
        url: serviceAreaName + 'EditTemplateEnabled',
        method: 'post',
        data
    })
}


export function subTaskToParent(data){
    return request({
        url: serviceAreaName + 'SubTaskToParent',
        method: 'post',
        data
    })
}


export function getMaintenanceList(params){
    return request({
        url: serviceAreaName + 'GetMaintenanceList',
        method: 'get',
        params
    })
}
 
export function editMaintenanceList(data){
    return request({
        url: serviceAreaName + 'EditMaintenanceList',
        method: 'post',
        data
    })
}


export function startEarly(data){
    return request({
        url: serviceAreaName + 'StartEarly',
        method: 'post',
        data
    })
}


export function getHypertrunkGuidepos(data){
  return request({
      url: serviceAreaName + 'GetHypertrunkGuidepos',
      method: 'post',
      data
  })
}

