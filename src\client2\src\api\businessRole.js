import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business+'/BusinessRole/';

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function EditEmployee(data){
    return request({
        url: serviceAreaName + 'EditEmployee',
        method: 'post',
        data
    })
}
export function DeleteEmployee(data){
    return request({
        url: serviceAreaName + 'DeleteEmployee',
        method: 'post',
        data
    })
}

export function getRegionalListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetRegionalListByCondition',
        method: 'post',
        data
    })
}


export function getDepartmentListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetDepartmentListByCondition',
        method: 'post',
        data
    })
}

export function getOnlyLookOneselfData(params) {
    return request({
        url: serviceAreaName + 'GetOnlyLookOneselfData',
        method: 'get',
        params
    })
}


