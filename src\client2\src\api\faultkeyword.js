import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/FaultKeyword/GetListPage',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/FaultKeyword/Edit',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/FaultKeyword/Add',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + '/FaultKeyword/Delete',
        method: 'post',
        data
    })
}
