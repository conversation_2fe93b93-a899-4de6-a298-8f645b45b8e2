import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

//新增
export function addProjectManagementChange(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementChange/AddProjectManagementChange',
        method: 'post',
        data
    })
}

//修改
export function editProjectManagementChange(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementChange/EditProjectManagementChange',
        method: 'post',
        data
    })
}

//批量删除
export function deleteProjectManagementChange(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementChange/DeleteProjectManagementChange',
        method: 'post',
        data
    })
}

//分页查询
export function getProjectManagementChangeListPage(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementChange/GetProjectManagementChangeListPage',
        method: 'post',
        data
    })
}


//详情
export function getProjectManagementChangeDetail(params) {
    return request({
        url: serviceAreaName + '/ProjectManagementChange/GetProjectManagementChangeDetail',
        method: 'get',
        params
    })
}

//审批
export function approval(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementChange/Approval',
        method: 'post',
        data
    })
}



