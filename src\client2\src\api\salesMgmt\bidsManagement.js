import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/bids/'

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getCustomerListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetCustomerListByCondition',
        method: 'post',
        data
    })
}
export function getBusinessOpportunityListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetBusinessOpportunityListByCondition',
        method: 'post',
        data
    })
}

export function getOrdersHistory(params) {
    return request({
        url: serviceAreaName + 'GetBidHistory',
        method: 'get',
        params
    })
}

export function createApproval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}


export function getEmployees(params) {
    return request({
        url: serviceAreaName + 'GetPrincipal',
        method: 'get',
        params
    })
}

export function addCustomerRelation(data) {
    return request({
        url: serviceAreaName + 'AddCustomerRelation',
        method: 'post',
        data
    })
}

export function addBusinessOpportunityRelation(data) {
    return request({
        url: serviceAreaName + 'AddBusinessOpportunityRelation',
        method: 'post',
        data
    })
}


export function delCustomerRelation(data) {
    return request({
        url: serviceAreaName + 'DeleteCustomerRelation',
        method: 'post',
        data
    })
}

export function delBusinessOpportunityRelation(data) {
    return request({
        url: serviceAreaName + 'DeleteBusinessOpportunityRelation',
        method: 'post',
        data
    })
}

export function getStatistics(params) {
    return request({
        url: serviceAreaName + 'GetStatistics',
        method: 'get',
        params
    })
}


export function getOrderListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetOrderListByCondition',
        method: 'post',
        data
    })
}

export function addBidsOrderRelation(data) {
    return request({
        url: serviceAreaName + 'AddBidsOrderRelation',
        method: 'post',
        data
    })
}

export function deleteBidsOrderRelation(data) {
    return request({
        url: serviceAreaName + 'DeleteBidsOrderRelation',
        method: 'post',
        data
    })
}

