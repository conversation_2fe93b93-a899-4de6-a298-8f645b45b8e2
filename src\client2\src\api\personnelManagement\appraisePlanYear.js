
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/AppraisePlanYear/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}



//下一步，个人绩效承诺采集（第一步点击）
export function startPersonalGatherAndNext(data) {
    return request({
        url: serviceAreaName + 'StartPersonalGatherAndNext',
        method: 'post',
        data
    })
}

//第二步点击
export function finishPersonalGatherAndNext(data) {
    return request({
        url: serviceAreaName + 'FinishPersonalGatherAndNext',
        method: 'post',
        data
    })
}

//第三步点击
export function finishMidtermReviewAndStart(data) {
    return request({
        url: serviceAreaName + 'FinishMidtermReviewAndStart',
        method: 'post',
        data
    })
}

export function endAndNext(data) {
    return request({
        url: serviceAreaName + 'EndAndNext',
        method: 'post',
        data
    })
}
