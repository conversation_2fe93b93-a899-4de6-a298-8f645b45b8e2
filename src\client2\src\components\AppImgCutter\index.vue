<template>
    <div class="img-wrapper">
        <span class="file-btn-del" v-show="obj && obj.path && !disabled">
            <i class="el-icon-remove" @click.stop="removeFile"></i>
        </span>

        <viewer v-if="obj && obj.path" :images="[obj.path]" style="height: 120px;">
            <img style="width: 120px; height: 120px" v-for="(item, index) in [obj.path]" :src="item" :key="index">
        </viewer>
        <ImgCutter v-on:cutDown="cutDown" :sizeChange='false' :WatermarkText='""' :append-to-body="true">
            <button v-show="!obj || !obj.path" slot="open" type="button" class="el-button el-button--primary el-button--small upload-btn icon-btn">
                <i class="el-icon-plus icon-plus"></i>
            </button>
        </ImgCutter>
    </div>
</template>

<script>
import * as common from '@/api/common'
import ImgCutter from './ImgCutter'
export default {
    name: "app-img-cutter",
    directives: {},
    components: {
        ImgCutter,
    },
    mixins: [],
    props: {
        val: {
            type: Object,
            default: null
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        val: {
            handler(obj) {
                this.obj = obj ? JSON.parse(JSON.stringify(obj)) : null
            },
            deep: true
        },
    },
    computed: {
    },
    created() {
    },
    mounted() { },
    data() {
        return {
            obj: this.val ? JSON.parse(JSON.stringify(this.val)) : null,
            
        };
    },
    methods: {
        removeFile() {
            this.obj = null
            this.filesChange()
        },
        filesChange() {
            this.$emit("change", this.obj)
        },
        cutDown(res1) {

            let fileObj = res1.file
            // if(this.fileType == 1){
            //     fileObj = await compress(file).then(res => {
            //         return res
            //     })
            // }

            let self = this;
            let config = {
                headers: { 'Content-Type': 'multipart/form-data' },
            }

            let formData = new FormData();

            formData.append("file", fileObj, fileObj.name);
            formData.append('type', 1)

            let postRes = common.postFile(formData, config) //上传成功返回集合
            
            if (postRes) {
                postRes.then(res => {
                    this.obj = {
                        id: res.Id,
                        path: res.Path
                    }
                    this.filesChange()

                    this.$message({
                        showClose: true,
                        message: "上传成功。",
                        type: "success"
                    });

                }).catch(err => {
                })

            }
        },
    }
};
</script>


<style lang="scss" scoped>
.img-wrapper{
    position: relative;
    width: 120px;
    height: 120px;
    text-align: center;
}

.icon-btn{
    width: 120px;
    height: 120px;
    padding: 0;
    font-size: 0;
    background: none;
    border-style: dashed;
    text-align: center;
}

.upload-btn{
    position: relative;
    vertical-align: middle;
}

.icon-plus{
    font-size: 24px;
    color: #8c939d;
}

.file-btn-del {
    background: transparent;
    background: #fff;
    border-radius: 50%;
    display: block;
    cursor: pointer;
    position: absolute;
    height: 18px;
    width: 18px;
    font-size: 18px;
    top: -6px;
    right: -6px;
}

.file-btn-del:hover {
    transition: all 0.3s;
    color: red;
    z-index: 9;
}

.re-choose-btn{
    // height: 20px; 
    // line-height: 20px; 
    text-align: center; 
    // color: #fff;
    // background: red;
    // position: absolute;
    // bottom: 0;
    // left: 0;
    // right: 0;
    font-size: 12px;
    cursor: pointer;
}
</style>
