
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/Supplier/'

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}


export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}
export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function getAll(data) {
    return request({
        url: serviceAreaName + 'GetListByConditionType',
        method: 'post',
        data
    })
}





export function addType(data) {
    return request({
        url: serviceAreaName + 'AddType',
        method: 'post',
        data
    })
}




export function editType(data) {
    return request({
        url: serviceAreaName + 'EditType',
        method: 'post',
        data
    })
}

export function delType(data) {
    return request({
        url: serviceAreaName + 'DeleteType',
        method: 'post',
        data
    })
}
export function getListType(data) {
    return request({
        url: serviceAreaName + 'GetListPageType',
        method: 'post',
        data
    })
}


export function detailType(params) {
    return request({
        url: serviceAreaName + 'GetDetailsType',
        method: 'get',
        params
    })
}
