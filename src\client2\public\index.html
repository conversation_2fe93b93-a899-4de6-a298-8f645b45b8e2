<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>加载中...</title>
    <meta name="Keywords" content="佳运通,后台,管理系统" />
    <meta name="Description" content="佳运通后台管理系统" />
    <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> -->
    <script type="text/javascript" src="./wangEditor/wangEditor.js"></script>
    <!-- 高德版本 地图 -->
    <!-- <script type="text/javascript">
        window._AMapSecurityConfig = {
            securityJsCode: '09f3d0c6fef3205405d6594bffffa907'
            // serviceHost:'您的代理服务器域名或地址/_AMapService',  
            // 例如 ：serviceHost:'http://*******:80/_AMapService',
        }
    </script>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=de13aada309258f42c9e4e192fd0a2d9"></script> -->
    
    <style>
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 99999;
            display: flex;
            flex-flow: row nowrap;
            justify-content: center;
            align-items: center;
            background: none repeat scroll 0 0 #ffffff;
        }
        
        .sk-spinner-wordpress.sk-spinner {
            background-color: #bdc3c7;
            width: 40px;
            height: 40px;
            border-radius: 40px;
            position: relative;
            -webkit-animation: sk-innerCircle 1s linear infinite;
            animation: sk-innerCircle 1s linear infinite;
        }
        
        .sk-spinner-wordpress .sk-inner-circle {
            display: block;
            background-color: #ffffff;
            width: 16px;
            height: 16px;
            position: absolute;
            border-radius: 8px;
            top: 5px;
            left: 5px;
        }
        
        @keyframes sk-innerCircle {
            0% {
                -webkit-transform: rotate(0);
                transform: rotate(0);
            }
            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="preloader">
            <div class="sk-spinner sk-spinner-wordpress">
                <div class="sk-inner-circle"></div>
            </div>
        </div>
    </div>
    <script src="./global-status-config.js"></script>
</body>

</html>