import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkTaskFile/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function getHistory(data) {
    return request({
        url: serviceAreaName + 'GetHistory',
        method: 'post',
        data
    })
}


export function getAttachmentList(data) {
    return request({
        url: serviceAreaName + 'GetAttachmentList',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function packDownload(data, config) {
    return request({
        url: serviceAreaName + 'PackDownload',
        method: 'post',
        data,
        config
    })
}


export function getTreeData(data) {
    return request({
        url: serviceAreaName + 'GetTreeData',
        method: 'post',
        data
    })
}


export function packDownloadPlus(data) {
    return request({
        url: serviceAreaName + 'PackDownloadPlus',
        method: 'post',
        data
    })
}

export function getEmployeeList(data) {
    return request({
        url: serviceAreaName + 'GetEmployeeList',
        method: 'post',
        data
    })
}


 


