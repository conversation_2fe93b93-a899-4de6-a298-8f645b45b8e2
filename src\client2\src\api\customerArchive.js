import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

//新增
export function addCustomerArchive(data) {
    return request({
        url: serviceAreaName + '/CustomerArchive/Add',
        method: 'post',
        data
    })
}

//编辑
export function editCustomerArchive(data) {
    return request({
        url: serviceAreaName + '/CustomerArchive/Edit',
        method: 'post',
        data
    })
}

//删除
export function deleteCustomerArchive(data) {
    return request({
        url: serviceAreaName + '/CustomerArchive/Delete',
        method: 'post',
        data
    })
}

//分页列表
export function getCustomerArchiveListPage(data) {
    return request({
        url: serviceAreaName + '/CustomerArchive/GetListPage',
        method: 'post',
        data
    })
}

//完整列表
export function getCustomerArchiveList(params) {
    return request({
        url: serviceAreaName + '/CustomerArchive/GetList',
        method: 'get',
        params
    })
}

//导出
export function exportCustomerArchive(data) {
    return request({
        url: serviceAreaName + '/CustomerArchive/Export',
        method: 'post',
        data
    })
}

//详情
export function getCustomerArchiveDetail(params) {
    return request({
        url: serviceAreaName + '/CustomerArchive/GetCustomerArchiveDetail',
        method: 'get',
        params
    })
}

//分享
export function shareCustomerArchive(data) {
    return request({
        url: serviceAreaName + '/CustomerArchive/ShareCustomerArchive',
        method: 'post',
        data
    })
}

//获取分享详情
export function getCustomerArchiveShareDetail(params) {
    return request({
        url: serviceAreaName + '/CustomerArchive/GetCustomerArchiveShareDetail',
        method: 'get',
        params
    })
}
