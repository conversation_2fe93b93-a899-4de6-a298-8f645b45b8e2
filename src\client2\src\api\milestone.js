import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

export function getList(data) {
    return request({
        url: serviceAreaName + '/Milestone/GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + '/Milestone/GetDetails',
        method: 'get',
        params
    })
}
//获取风险概况数据
export function getProjectManagmentRiskProfile(params) {
    return request({
        url: serviceAreaName + '/ProjectManagmentRisk/GetProjectManagmentRiskProfile',
        method: 'get',
        params
    })
}
export function add(data) {
    return request({
        url: serviceAreaName + '/Milestone/Add',
        method: 'post',
        data
    })
}

export function del(data){
    return request({
        url: serviceAreaName + '/Milestone/Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/Milestone/Edit',
        method: 'post',
        data
    })
}

// 提交审批
export function submitAudit(data) {
    return request({
        url: serviceAreaName + '/Milestone/SubmitApplication',
        method: 'post',
        data
    })
}

// 审批
export function nodeAudit(data) {
    return request({
        url: serviceAreaName + '/Milestone/NodeAudit',
        method: 'post',
        data
    })
}

//转审（指派）
export function assign(data) {
    return request({
        url: serviceAreaName + '/Milestone/Assign',
        method: 'post',
        data
    })
}


export function getProjectTaskStatistics(params) {
    return request({
        url: serviceAreaName + '/TaskManagement/GetProjectTaskStatistics',
        method: 'get',
        params
    })
}

//提交开始审批
export function submitStartApprovalAsync(data) {
  return request({
      url: serviceAreaName + '/Milestone/SubmitStartApprovalAsync',
      method: 'post',
      data
  })
}

//提交完成审批
export function submitAccomplishApprovalAsync(data) {
  return request({
      url: serviceAreaName + '/Milestone/SubmitAccomplishApprovalAsync',
      method: 'post',
      data
  })
}
// 整体视图
export function overallViewListPageAsync(data) {
  return request({
      url: serviceAreaName + '/Milestone/OverallViewListPageAsync',
      method: 'post',
      data
  })
}

export function getFlowOperation(params) {
  return request({
      url: serviceAreaName + '/Milestone/GetFlowOperation',
      method: 'get',
      params
  })
}


export function changeProgress(data) {
    return request({
        url: serviceAreaName + '/Milestone/EditProgress',
        method: 'post',
        data
    })
}

// 根据项目id查出所有里程碑数据
export function getListByProjectId(params) {
  return request({
      url: serviceAreaName + '/Milestone/GetListByProjectId',
      method: 'get',
      params
  })
}
// 整体视图版本迭代里程碑数据
export function overallViewVersionIterationListPageAsync(data) {
  return request({
      url: serviceAreaName + '/Milestone/OverallViewVersionIterationListPageAsync',
      method: 'post',
      data
  })
}
