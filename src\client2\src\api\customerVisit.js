import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

//新增
export function addCustomerVisit(data) {
    return request({
        url: serviceAreaName + '/CustomerVisit/Add',
        method: 'post',
        data
    })
}

//编辑
export function editCustomerVisit(data) {
    return request({
        url: serviceAreaName + '/CustomerVisit/Edit',
        method: 'post',
        data
    })
}

//删除
export function deleteCustomerVisit(data) {
    return request({
        url: serviceAreaName + '/CustomerVisit/Delete',
        method: 'post',
        data
    })
}

//分页列表
export function getCustomerVisitListPage(data) {
    return request({
        url: serviceAreaName + '/CustomerVisit/GetListPage',
        method: 'post',
        data
    })
}

//完整列表
export function getCustomerVisitList(params) {
    return request({
        url: serviceAreaName + '/CustomerVisit/GetList',
        method: 'get',
        params
    })
}

//导出
export function exportCustomerVisit(data) {
    return request({
        url: serviceAreaName + '/CustomerVisit/Export',
        method: 'post',
        data
    })
}

//详情
export function getCustomerVisitDetail(params) {
    return request({
        url: serviceAreaName + '/CustomerVisit/GetCustomerVisitDetail',
        method: 'get',
        params
    })
}


export function getCommentsById(data) {
    return request({
        url: serviceAreaName + '/CustomerVisit/GetCommentsPage',
        method: 'post',
        data
    })
}

export function addComment(data) {
    return request({
        url: serviceAreaName + '/CustomerVisit/AddComment',
        method: 'post',
        data
    })
}

export function getDetailByMessageId(params) {
    return request({
        url: serviceAreaName + '/CustomerVisit/GetDetailsByMessageId',
        method: 'get',
        params
    })
}