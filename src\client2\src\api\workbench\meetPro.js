import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/MeetPro/'


export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

/**
 * 讨论结束
 * @param {*} data 
 * @returns 
 */
export function discussionEnd(data) {
    return request({
        url: serviceAreaName + 'DiscussionEnd',
        method: 'post',
        data
    })
}


/**
 * 完结会议
 * @param {*} data 
 * @returns 
 */
export function finish(data) {
    return request({
        url: serviceAreaName + 'Finish',
        method: 'post',
        data
    })
}

/**
 * 获取纪要查阅人员
 * @param {*} data 
 * @returns 
 */
export function minutesConfirmEmployee(data) {
    return request({
        url: serviceAreaName + 'MinutesConfirmEmployee',
        method: 'post',
        data
    })
}


/**
 * 撤回会议
 * @param {*} data 
 * @returns 
 */
export function retract(data) {
    return request({
        url: serviceAreaName + 'Retract',
        method: 'post',
        data
    })
}



/**
 * 转成实时会议
 * @param {*} data 
 * @returns 
 */
export function convertRealtime(data) {
    return request({
        url: serviceAreaName + 'ConvertRealtime',
        method: 'post',
        data
    })
}

/**
 * 已完成资料学习
 * @param {*} data 
 * @returns 
 */
export function completedStudy(data) {
    return request({
        url: serviceAreaName + 'CompletedStudy',
        method: 'post',
        data
    })
}



//创建项目审批
export function createApproval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function viewCount(params) {
    return request({
        url: serviceAreaName + 'ViewCount',
        method: 'get',
        params
    })
}

