
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/Knowledge/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}
export function pageview(params) {
    return request({
        url: serviceAreaName + 'Pageview',
        method: 'get',
        params
    })
}
export function statisticsCount(params) {
    return request({
        url: serviceAreaName + 'StatisticsCount',
        method: 'get',
        params
    })
}
export function approvalStatisticsCount(params) {
    return request({
        url: serviceAreaName + 'ApprovalStatisticsCount',
        method: 'get',
        params
    })
}

export function aproval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function favorites(data) {
    return request({
        url: serviceAreaName + 'Favorites',
        method: 'post',
        data
    })
}

export function batchEdit(data) {
    return request({
        url: serviceAreaName + 'BatchEdit',
        method: 'post',
        data
    })
}

export function addComment(data) {
    return request({
        url: serviceAreaName + 'AddComment',
        method: 'post',
        data
    })
}
export function getComment(params) {
    return request({
        url: serviceAreaName + 'GetComment',
        method: 'get',
        params
    })
}

export function revocation(params) {
    return request({
        url: serviceAreaName + 'Revocation',
        method: 'get',
        params
    })
}

export function approvalList(data) {
    return request({
        url: serviceAreaName + 'ApprovalList',
        method: 'post',
        data
    })
}
