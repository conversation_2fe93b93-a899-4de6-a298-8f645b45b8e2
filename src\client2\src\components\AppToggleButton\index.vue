<template>
  <div class="app-btns-wrapper" v-if="list && list.length > 0">
    <div class="btns" :class="[{pr: type == 'not-compact'}, {compact: type == 'compact'}]">
      <div
        v-for="(btn, idx) in list.slice(0, 3)"
        :class="[btn[keyName] == selected ? 'selected' : '']"
        :key="`btn-${idx}`"
        :style="{'max-width': `${maxWidth}`}"
        :title="btn.label"
        @click="handleToggle(btn)"
      >
        <div class="omit inner-wrapper">
          <slot :name="btn[keyName]" :data="btn">{{ btn.label }}</slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AppToggleButton",
  model: {
    prop: "selected",
    event: "change",
  },
  props: {
    selected: {
      type: [String, Number],
      default: null,
    },
    keyName: {
      type: String,
      default: "value",
    },
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    type: {
      type: String,
      default: 'compact', //compact（紧凑型）、not-compact（非紧凑型）
    },
    /**
     * 按钮的最大宽度
     */
    maxWidth: {
      type: String,
      default: 'none'
    },
    /**
     * 按钮最大展示数量（默认10000表示不限制）
     */
    showLimit: {
      type: Number,
      default: 10000
    },
  },
  data() {
    return {};
  },
  methods: {
    handleToggle(btn) {
      this.$emit("change", btn[this.keyName], btn);
    },
  },
};
</script>

<style scoped lang="scss">

$border-radius: $border-radius-base;

.app-btns-wrapper {
  display: inline-block;

  .btns {
    display: flex;
    > div {
      user-select: none;
      cursor: pointer;
      height: 32px;
      box-sizing: border;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 10px;
      border: 1px solid $border-color-lighter;
      background: $bg-color-2;
      white-space: nowrap;
      &:not(.selected) {
        &:hover{
          background: rgba($color: $color-primary, $alpha: .1);
          color: $color-primary;
        }
      }
      &.selected {
        background: #fff;
      }
      &:first-child {
        border-top-left-radius: $border-radius;
        border-bottom-left-radius: $border-radius;
      }
      &:last-child {
        border-top-right-radius: $border-radius;
        border-bottom-right-radius: $border-radius;
      }

      .inner-wrapper{
        svg{
          vertical-align: middle;
        }
      }
    }

    &.pr{
      > div {
        border-radius: $border-radius;
      }
      >div:not(:last-child){
        margin-right: 5px;
      }
    }
  }

  .compact{
    >div{
      &:not(:first-child) {
        border-left: none;
      }
    }
  }
}
</style>
