<template>
    <div class="list-wrapper">
        <div class="tag-common" @click="handleClick(t)" v-for="t in list" :key="t[keyName]" :class="[t[keyName] == selected ? 'active' : '', mode == 'list' ? 'tag-list' : 'tag']">
            <slot :name="t[keyName]" :data='t'>{{ t.label }}</slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'tags',
    model: {
        prop: 'selected',
        event: 'change'
    },
    props: {
        selected: {
            type: [String, Number],
            default: null
        },
        items: { //格式要求 [{value: 1, label: ''}]————label内容不能重复
            type: Array,
            default: () => {
                return []
            }
        },
        mode: {
            type: String,
            default: 'tag' // tag、list
        },
        keyName: {
            type: String,
            default: 'value'
        }
    },
    watch: {
        items: {
            handler(val) {
                this.list = JSON.parse(JSON.stringify(this.items))
            },
            immediate: true,
            deep: true
        },
    },
    data () {
        return {
            list: []
        }
    },
    methods: {
        handleClick(tag) {
            this.$emit('change', tag[this.keyName])
        },
    },
}
</script>

<style lang="scss" scoped>
.list-wrapper{
    padding: 0 10px;
    padding-left: 0;
    background: $color-white;
    color: $text-regular;
    .tag-common {
        display: inline-block;
        padding: 10px 2px;
        cursor: pointer;
        box-sizing: border-box;
        // border-radius: 5px;
        // margin: 2px;
        position: relative;
    }
    .tag{
        font-weight: 700;
        &.active {
            color: $color-primary;
        }
        &.active::after{
            content: " ";
            position: absolute;
            left: 0;
            right: 0;
            bottom: -1px;
            height: 2px;
            background: $color-primary;
        }
        &:hover{
            color: rgba($color: $color-primary, $alpha: .7);
        }        
    }
    .tag:not(:last-child) {
        margin-right: 20px;
    }
    
    .tag-list{
        display: block;
        // height: 40px;
        // line-height: 40px;
        // padding: 0 4px;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        border-radius: 5px;

        // &.active {
        //     background: #DCDFE6;
        // }
        &.active {
            color: $color-primary;
        }
        &:hover{
            background: $bg-color-2;
        }  
    }
}
</style>