<template>
  <div class="_tab_common_wrapper __dynamicTabContentWrapper">
    <template>
      <div
        class="conditionArea-wrap clearfix"
        :style="{ paddingTop: layoutMode == 'simple' ? '10px' : '' }"
        v-show="isShowConditionArea"
      >
        <slot name="conditionArea"></slot>
      </div>
      <div class="btns-area" v-show="isShowBtnsArea">
        <el-button-group>
          <slot name="btnsArea"></slot>
        </el-button-group>
        <!-- <el-button-group>
                  <el-dropdown :hide-on-click='false'>
                      <el-button type="default">
                          显示隐藏列 <span v-show="hiddenColumnTotal > 0">({{ hiddenColumnTotal }})</span><i class="el-icon-arrow-down el-icon--right"></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item v-for="head in headers" :key="head.attr.prop">
                              <el-checkbox @change='handleChange(head)' v-model="head.isShow" :label="head.attr.label" :key="head.attr.prop" :disabled='head.isShow && headers.length - hiddenColumnTotal <= 1'></el-checkbox>
                          </el-dropdown-item>
                      </el-dropdown-menu>
                  </el-dropdown>
        </el-button-group>-->
      </div>
    </template>
    <div>
      <slot name="tableTopAres"></slot>
    </div>
    <div class="content" :id="`${dynamicTableContentId}`">
      <app-table-core
        v-bind="$attrs"
        v-on="$listeners"
        ref="appTableCore"
        :tabColumns="tabColumns"
        :height="tabHeight"
        :viewBusinessType="viewBusinessType"
      >
        <!-- 不建议使用该属性（customHeader），可以通过设置 renderHeader 来代替 -->
        <template
          v-for="c in columns.filter(s => s.customHeader)"
          slot-scope="scope"
          :slot="`header_${c.attr.prop}`"
        >
          <slot :name="'header_' + c.attr.prop" :column="scope.column"></slot>
        </template>
        <!-- 表格中需要自定义的列 -->
        <template v-for="c in columns.filter(s => s.slot)" slot-scope="scope" :slot="c.attr.prop">
          <slot :name="c.attr.prop" :row="scope.row" :index="scope.index"></slot>
        </template>

        <template
          v-for="c in columns.filter(s => s.isFilterColumn)"
          :slot="`filter_${c.attr.prop}`"
        >
          <slot :name="'filter_' + c.attr.prop"></slot>
        </template>

        <!-- 表格行操作区域 -->
        <template slot-scope="scope">
          <slot :row="scope.row" :index="scope.index"></slot>
        </template>
        <template slot="append">
          <slot name="append"></slot>
        </template>
      </app-table-core>
    </div>
  </div>
</template>

<script>
// import { getHideColumnsOfPage, setHideColumnsOfPage } from '@/utils/auth'
import { listToTreeSelect, throttle } from "@/utils";
import tabDynamicHeightMixins from "@/mixins/tabDynamicHeightMixins";

import mixins from "./mixins";
export default {
  name: "app-table",
  mixins: [mixins, tabDynamicHeightMixins],
  props: {
    isShowConditionArea: {
      type: Boolean,
      default: true,
    },
    isShowBtnsArea: {
      //是否显示表格批量操作按钮区域
      type: Boolean,
      default: true,
    },
    /**
     * 查询条件区域布局方式：
     * classic：经典模式，最老的布局方式
     * simple：简易模式，只显示一个查询条件（综合输入框），其他查询条件下拉展示，后面跟着表格批量操作按钮区块；
     */
    layoutMode: {
      type: String,
      default: "classic",
    },
  },
  computed: {
    customHeaders() {
      let headers = this.$slots;
    },
  },
  created() {
    let myDate = new Date();
    this.dynamicTableContentId = `__dynamicTabCoreWrapper${myDate.getTime()}`;
  },
};
</script>

<style scoped>
.conditionArea-wrap {
  padding: 0 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}
.btns-area {
  text-align: left;
  padding: 10px;
  /* padding-right: 10px; */
}

._tab_common_wrapper {
  height: 100%;
}
</style>
