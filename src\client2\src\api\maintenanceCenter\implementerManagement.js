
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/ImplementerManagement/'


export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(params) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'get',
        params
    })
}
export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}


export function adjustRegional(data) {
    return request({
        url: serviceAreaName + 'AdjustRegional',
        method: 'post',
        data
    })
}


export function getApprovalPrestore(params) {
    return request({
        url: serviceAreaName + 'GetApprovalPrestore',
        method: 'get',
        params
    })
}

export function setApprovalPrestore(data) {
    return request({
        url: serviceAreaName + 'SetApprovalPrestore',
        method: 'post',
        data
    })
}


export function getDetailsList(data) {
    return request({
        url: serviceAreaName + 'GetDetailsList',
        method: 'post',
        data
    })
}



export function setAgent(data) {
    return request({
        url: serviceAreaName + 'SetAgent',
        method: 'post',
        data
    })
}

export function cancelAgentSet(data) {
    return request({
        url: serviceAreaName + 'CancelAgentSet',
        method: 'post',
        data
    })
}