import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/MeetProAgenda/'


export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}
// 获取议题结论数组
export function getConclusionList(params) {
    return request({
        url: serviceAreaName + 'getConclusionList',
        method: 'get',
        params
    })
}
// 议题处理
export function dealAgendaApi(data) {
    return request({
        url: serviceAreaName + 'DealAgenda',
        method: 'post',
        data
    })
}

export function study(data) {
    return request({
        url: serviceAreaName + 'Study',
        method: 'post',
        data
    })
}
export function remindStudy(data) {
    return request({
        url: serviceAreaName + 'RemindStudy',
        method: 'post',
        data
    })
}



// 议题处理详情
export function getMeetProAgendaDealDetail(params) {
    return request({
        url: serviceAreaName + 'GetMeetProAgendaDealDetail',
        method: 'get',
        params
    })
}

export function getMeetProAgendaFollowUpList(params) {
    return request({
        url: serviceAreaName + 'GetMeetProAgendaFollowUpList',
        method: 'get',
        params
    })
}


export function dealAgendaSummary(data) {
    return request({
        url: serviceAreaName + 'DealAgendaSummary',
        method: 'post',
        data
    })
}

export function getAgendaSummaryList(params) {
    return request({
        url: serviceAreaName + 'GetAgendaSummaryList',
        method: 'get',
        params
    })
}
export function addDemandApi(data) {
    return request({
        url: serviceAreaName + 'AddDemand',
        method: 'post',
        data
    })
}
export function getListDemandPageApi(data) {
    return request({
        url: serviceAreaName + 'ListDemandPage',
        method: 'post',
        data
    })
}
export function getDemandDetailsApi(params) {
    return request({
        url: serviceAreaName + 'GetDemandDetails',
        method: 'get',
        params
    })
}
export function editDemandApi(data) {
    return request({
        url: serviceAreaName + 'editDemand',
        method: 'post',
        data
    })
}
export function solicitSolutionsApi(data) {
    return request({
        url: serviceAreaName + 'SolicitSolutions',
        method: 'post',
        data
    })
}
export function agreeApi(params) {
    return request({
        url: serviceAreaName + 'Agree',
        method: 'get',
        params
    })
}
export function disAgreeApi(data) {
    return request({
        url: serviceAreaName + 'DisAgree',
        method: 'post',
        data
    })
}
export function voteApi(data) {
    return request({
        url: serviceAreaName + 'Vote',
        method: 'post',
        data
    })
}
export function deleteDemandApi(data) {
    return request({
        url: serviceAreaName + 'DeleteDemand',
        method: 'post',
        data
    })
}
export function getListPageApi(data) {
  return request({
    url: serviceAreaName + "GetListPage",
    method: "post",
    data,
  });
}
export function getHistorysApi(params) {
    return request({
        url: serviceAreaName + 'GetHistorys',
        method: 'get',
        params
    })
}
export function resolveApi(data) {
    return request({
        url: serviceAreaName + 'Resolve',
        method: 'post',
        data
    })
}


export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}