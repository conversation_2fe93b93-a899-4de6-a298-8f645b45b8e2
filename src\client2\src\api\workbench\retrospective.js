import request from "@/utils/request";
import { serviceArea } from "../serviceArea";
let serviceAreaName = serviceArea.business;

export function addIssueTagApi(data) {
  return request({
    url: serviceAreaName + "/Review/AddOrEditReviewQuestionTag",
    method: "post",
    data,
  });
}
export function getReviewProblemTagListApi(data) {
  return request({
    url: serviceAreaName + "/Review/GetReviewQuestionTagList",
    method: "post",
    data,
  });
}
export function retrospectiveDeleteApi(data) {
  return request({
    url: serviceAreaName + "/Review/Delete",
    method: "post",
    data,
  });
}
export function addOrEditReviewProblemMainTagApi(data) {
  return request({
    url: serviceAreaName + "/Review/AddOrEditReviewQuestionMainTag",
    method: "post",
    data,
  });
}
export function deleteReviewProblemTagApi(params) {
  return request({
    url: serviceAreaName + "/Review/DeleteReviewQuestionTag",
    method: "get",
    params,
  });
}
export function addOrEditReviewSelfAssessmentTagApi(data) {
  return request({
    url: serviceAreaName + "/Review/AddOrEditReviewSelfAssessmentTag",
    method: "post",
    data,
  });
}
export function getReviewSelfAssessmentTagListApi(data) {
  return request({
    url: serviceAreaName + "/Review/GetReviewSelfAssessmentTagList",
    method: "post",
    data,
  });
}
export function deleteReviewSelfAssessmentTagApi(params) {
  return request({
    url: serviceAreaName + "/Review/DeleteReviewSelfAssessmentTag",
    method: "get",
    params,
  });
}
export function addOrEditReviewSelfAssessmentMainTagApi(data) {
  return request({
    url: serviceAreaName + "/Review/AddOrEditReviewSelfAssessmentMainTag",
    method: "post",
    data,
  });
}
export function getTaskDetailApi(params) {
  return request({
    url: serviceAreaName + "/HypertrunkTask/GetDetails",
    method: "get",
    params,
  });
}
export function getAllQuestionTag(params) {
  return request({
    url: serviceAreaName + "/Review/GetAllQuestionTag",
    method: "get",
    params,
  });
}
export function retAddOrEditApi(data) {
  return request({
    url: serviceAreaName + "/Review/AddOrEdit",
    method: "post",
    data,
  });
}
export function getRetrospectiveListApi(data) {
  return request({
    url: serviceAreaName + "/Review/getList",
    method: "post",
    data,
  });
}
export function getRetrospectiveDetailApi(params) {
  return request({
    url: serviceAreaName + "/Review/GetDetail",
    method: "get",
    params,
  });
}
export function getAllSelfAssessmentTagApi(params) {
  return request({
    url: serviceAreaName + "/Review/GetAllSelfAssessmentTag",
    method: "get",
    params,
  });
}
export function getDetailByGradeApi(params) {
  return request({
    url: serviceAreaName + "/Review/GetDetailByGrade",
    method: "get",
    params,
  });
}
export function getAssignRetDetail(params) {
  return request({
    url: serviceAreaName + "/Review/GetAssignDetail",
    method: "get",
    params,
  });
}
export function getCommentApi(params) {
  return request({
    url: serviceAreaName + "/Review/GetComment",
    method: "get",
    params,
  });
}
export function addCommentApi(data) {
  return request({
    url: serviceAreaName + "/Review/AddComment",
    method: "post",
    data,
  });
}
export function setGiveLikeApi(params) {
  return request({
    url: serviceAreaName + "/Review/SetGiveLike",
    method: "get",
    params,
  });
}
export function getGiveLikeApi(params) {
  return request({
    url: serviceAreaName + "/Review/getGiveLike",
    method: "get",
    params,
  });
}
export function recallApi(params) {
  return request({
    url: serviceAreaName + "/Review/Recall",
    method: "get",
    params,
  });
}
export function getAssigningEmployeeListApi(data) {
  return request({
    url: serviceAreaName + "/Review/GetAssigningEmployeeList",
    method: "post",
    data,
  });
}
export function assigningEmployeeGradeApi(data) {
  return request({
    url: serviceAreaName + "/Review/AssigningEmployeeGrade",
    method: "post",
    data,
  });
}
export function markGradeApi(data) {
  return request({
    url: serviceAreaName + "/Review/MarkGrade",
    method: "post",
    data,
  });
}
export function getQuestionListApi(data) {
  return request({
    url: serviceAreaName + "/CommonQuestion/GetList",
    method: "post",
    data,
  });
}
export function getQuestionDetailApi(params) {
  return request({
    url: serviceAreaName + "/CommonQuestion/GetDetail",
    method: "get",
    params,
  });
}
export function solveQuestionApi(data) {
  return request({
    url: serviceAreaName + "/CommonQuestion/SolveQuestion",
    method: "post",
    data,
  });
}
export function assignQuestionAddOrEditApi(data) {
  return request({
    url: serviceAreaName + "/AssignQuestion/AddOrEdit",
    method: "post",
    data,
  });
}
export function assignQuestionGetDetailApi(params) {
  return request({
    url: serviceAreaName + "/AssignQuestion/GetDetail",
    method: "get",
    params,
  });
}
export function getAssignReviewSettingPageList(data) {
  return request({
    url: serviceAreaName + "/AssignReview/GetAssignReviewSettingPageList",
    method: "post",
    data,
  });
}
export function addOrEditAssignReviewSetting(data) {
  return request({
    url: serviceAreaName + "/AssignReview/AddOrEditAssignReviewSetting",
    method: "post",
    data,
  });
}
export function getAssignReviewSettingDetail(params) {
  return request({
    url: serviceAreaName + "/AssignReview/GetAssignReviewSettingDetail",
    method: "get",
    params,
  });
}
export function changeAssignReviewSettingIsOpen(params) {
  return request({
    url: serviceAreaName + "/AssignReview/ChangeAssignReviewSettingIsOpen",
    method: "get",
    params,
  });
}
export function deleteAssignReviewSetting(data) {
  return request({
    url: serviceAreaName + "/AssignReview/DeleteAssignReviewSetting",
    method: "post",
    data,
  });
}
export function getQuestionTagCountBoard(data) {
  return request({
    url: serviceAreaName + "/Review/GetQuestionTagCountBoard",
    method: "post",
    data,
  });
}
export function getReviewExperiencesCountBoard(data) {
  return request({
    url: serviceAreaName + "/Review/GetReviewExperiencesCountBoard",
    method: "post",
    data,
  });
}
export function getReviewCountBoard(data) {
  return request({
    url: serviceAreaName + "/Review/GetReviewCountBoard",
    method: "post",
    data,
  });
}
export function getGiveLikeCountBoard(data) {
  return request({
    url: serviceAreaName + "/Review/GetGiveLikeCountBoard",
    method: "post",
    data,
  });
}
export function getUrgentEmployeeList(data) {
  return request({
    url: serviceAreaName + "/Review/GetUrgentEmployeeList",
    method: "post",
    data,
  });
}
export function endReviewGetMidReviewDetail(params) {
  return request({
    url: serviceAreaName + "/Review/EndReviewGetMidReviewDetail",
    method: "get",
    params,
  });
}
export function getReviewDetailByGradeList(params) {
  return request({
    url: serviceAreaName + "/Review/GetReviewDetailByGradeList",
    method: "get",
    params,
  });
}
export function getIssueTaskList(params) {
  return request({
    url: serviceAreaName + "/CommonQuestion/GetTaskList",
    method: "get",
    params,
  });
}
export function getAssignTaskList(params) {
  return request({
    url: serviceAreaName + "/CommonQuestion/GetAssignTaskList",
    method: "get",
    params,
  });
}
export function questionOperateApi(data) {
  return request({
    url: serviceAreaName + "/CommonQuestion/AddOrEditOrDelete",
    method: "post",
    data,
  });
}
export function editSortReviewSelfAssessmentTag(data) {
  return request({
    url: serviceAreaName + "/Review/EditSortReviewSelfAssessmentTag",
    method: "post",
    data,
  });
}
export function editSortReviewQuestionTag(data) {
  return request({
    url: serviceAreaName + "/Review/EditSortReviewQuestionTag",
    method: "post",
    data,
  });
}
