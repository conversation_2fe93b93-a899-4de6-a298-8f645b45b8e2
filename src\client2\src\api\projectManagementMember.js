import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

// 分页查询
export function getListPage(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/GetListPage',
        method: 'post',
        data
    })
}

// 根据成员ID获取成员详情信息
export function getDetails(params) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/GetDetails',
        method: 'get',
        params
    })
}

// 根据成员ID获取成员的员工信息
export function getEmployeeDetailsById(params) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/GetEmployeeDetailsById',
        method: 'get',
        params
    })
}

// 操作数据集合
// 新增/修改/删除
export function addOrUpdateOrDeleteByProjectId(data_1, data_2) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/AddOrUpdateOrDeleteByProjectId',
        method: 'post',
        data: {
            data_1,
            data_2
        }
    })
}

// 批量删除团队成员
export function deleteProjectManagementMembers(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/DeleteProjectManagementMembers',
        method: 'post',
        data
    })
}


export function deleteByIds(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/DeleteProjectManagementMembersByIds',
        method: 'post',
        data
    })
}


// 修改团队成员
export function editProjectManagementMember(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/EditProjectManagementMember',
        method: 'post',
        data
    })
}

// 添加团队成员
export function addProjectManagementMember(data_1, data_2) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/AddProjectManagementMember',
        method: 'post',
        data: {
            data_1,
            data_2
        }
    })
}

// 提交未通过评审的成员变更
export function unapprovedManagementMemberChange(data_1, data_2) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/UnapprovedManagementMemberChange',
        method: 'post',
        data: {
            data_1,
            data_2
        }
    })
}

// 审核通过项目团队变更
export function approvedManagementMemberChange(data_1, data_2) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/ApprovedManagementMemberChange',
        method: 'post',
        data: {
            data_1,
            data_2
        }
    })
}

// 根据项目ID查询当前团队成员变更
export function getCurrentManagementMemberChangeByProjectId(params) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/GetCurrentManagementMemberChangeByProjectId',
        method: 'get',
        params
    })
}

// 根据变更序号查询团队成员变更历史
export function getManagementMemberChangeByChangeSerialNumber(params) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/GetManagementMemberChangeByChangeSerialNumber',
        method: 'get',
        params
    })
}

// 删除未审批通过的成员变更
export function deleteUnapprovedManagementMemberChange(data) {
    return request({
        url: serviceAreaName + '/ProjectManagementMember/DeleteUnapprovedManagementMemberChange',
        method: 'post',
        data
    })
}