import request from '@/utils/request'

import { serviceArea } from './serviceArea'

let areaName = serviceArea.business

export function getList(data) {
    return request({
        url: areaName + '/EnterpriseConfigure/Allkey',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: areaName + '/EnterpriseConfigure/GetList',
        method: 'get',
        params
    })
}

export function edit(data) {
    return request({
        url: areaName + '/EnterpriseConfigure/Edit',
        method: 'post',
        data
    })
}
