import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/Stage/'

export function getStageList(data) {
  data = data || {};
  return request({
    url: busServiceAreaName + 'GetListByCondition',
    method: 'post',
    data
  })
}

export function getStagePageList(data) {
  data = data || {};
  return request({
    url: busServiceAreaName + 'GetListPage',
    method: 'post',
    data
  })
}

export function addStage(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function getDetail(params) {
  return request({
    url: busServiceAreaName + 'GetDetails',
    method: 'get',
    params
  })
}

export function edit(data) {
  return request({
    url: busServiceAreaName + 'Edit',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: busServiceAreaName + 'Delete',
    method: 'post',
    data
  })
}

export function changeProcess(data) {
  return request({
    url: busServiceAreaName + 'EditProgressAsync',
    method: 'post',
    data
  })
}