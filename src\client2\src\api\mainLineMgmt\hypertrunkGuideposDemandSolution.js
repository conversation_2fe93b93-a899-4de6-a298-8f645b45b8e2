import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkGuideposDemandSolution/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function up(data) {
    return request({
        url: serviceAreaName + 'Up',
        method: 'post',
        data
    })
}

export function viewVote(params) {
    return request({
        url: serviceAreaName + 'ViewVote',
        method: 'get',
        params
    })
}

export function used(data) {
    return request({
        url: serviceAreaName + 'Used',
        method: 'post',
        data
    })
}

export function bulkVote(data) {
    return request({
        url: serviceAreaName + 'BulkVote',
        method: 'post',
        data
    })
}
