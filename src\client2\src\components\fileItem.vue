<template>
  <div class="file-item-wrapper" v-if="fileObj">
    <viewer
      v-if="fileObj && getFileType(fileSuffix) == 'image' && !crossItemView"
      v-show="false"
      :images="[fileObj.Path]"
      :options="options"
      class="viewer"
      ref="viewer"
      @inited="inited"
    >
      <img :src="fileObj.Path" :data-source="fileObj.Path" class="image" />
    </viewer>

    <slot name="prefix"></slot>

    <img :src="require(`../assets/images/${getFileType(fileSuffix)}.png`)" alt="" srcset="" />
    <div class="file-name" :title="fileObj.FileName">
      <div class="file-title omit">{{ fileObj.FileName }}</div>
      <div class="file-size">{{ formatSize(fileObj.FileSize) }}</div>
    </div>

    <span style="width: 100px; display: flex; align-items: center; justify-content: end">
      <svg-icon
        icon-class="review-icon"
        title="查看"
        class="icon com-icon"
        v-if="['image','pdf'].includes(getFileType(fileSuffix))"
        @click.native="handleReview"
      ></svg-icon>
      <svg-icon
        icon-class="download-icon"
        title="下载"
        class="icon com-icon"
        @click.native="handleDownlaod"
      ></svg-icon>
      <svg-icon
        v-if="!readonly"
        icon-class="delete-icon"
        title="删除"
        class="icon danger-icon"
        @click.native="handleRemove"
      ></svg-icon>
    </span>
  </div>
</template>

<script>
import { serviceArea } from "@/api/serviceArea";
import { maps } from "@/utils/commonEnum";

export default {
  props: {
    fileObj: {
      type: Object,
      default: null,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否可以垮多个fileItem组件来查看图片（就是说查看图片由外部控制，不需要fileItem内部来控制）
     */
    crossItemView: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    fileSuffix() {
      let fileName = this.fileObj.FileName;
      let idx = fileName.lastIndexOf(".");
      if (idx > -1) {
        return fileName.substr(idx + 1);
      }
      return "";
    },
  },
  data() {
    return {
      maps: maps,
      // options: {
      //     toolbar: false,
      //     url: "data-source"
      // },
    };
  },
  methods: {
    handleRemove() {
      this.$emit("handleDelate");
    },
    formatSize: function (size) {
      if (size < 1024) {
        return size.toFixed(0) + " bytes";
      } else if (size < 1024 * 1024) {
        return (size / 1024.0).toFixed(0) + " KB";
      } else if (size < 1024 * 1024 * 1024) {
        return (size / 1024.0 / 1024.0).toFixed(1) + " MB";
      } else {
        return (size / 1024.0 / 1024.0 / 1024.0).toFixed(1) + " GB";
      }
    },
    getFileType(suffix) {
      let result = "weizhiwenjian";
      for (let i = 0; i < maps.length; i++) {
        let obj = maps[i].list.find(s => s == suffix.toLowerCase());
        if (obj) {
          result = maps[i].suffix;
          break;
        }
      }
      return result;
    },
    handleDownlaod() {
      var dowFileUrl = `api${serviceArea.resource}/FileUpload/Download?id=${this.fileObj.Id}`;
      window.open(dowFileUrl);
    },
    inited(viewer) {
      if (!this.crossItemView) {
        this.$viewer = viewer;
      }
    },
    handleReview() {
      const fileType = this.getFileType(this.fileSuffix)
      if (!this.crossItemView && fileType == 'image') {
        this.$viewer.view();
      } else {
        this.$emit("reviewFile", this.fileObj.Path,fileType);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.com-icon {
  color: rgb(160, 160, 160);
  &:hover.com-icon {
    color: #1c81fd;
  }
}
.danger-icon {
  color: #f1a1a1;
  &:hover {
    color: #e43b3b;
  }
}

.file-item-wrapper {
  box-sizing: border-box;
  // border: 1px solid #EEEEEE;
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  border-radius: 5px;
  &:hover {
    background: #f9fafb;
  }
  .file-name {
    width: 0;
    flex: 1;
    padding: 0 10px;
    .file-title {
      font-weight: 600;
      color: $text-primary;
      line-height: 18px;
    }
    .file-size {
      font-size: 12px;
      color: #666666;
      line-height: 14px;
    }
  }
  .icon {
    cursor: pointer;
    font-size: 16px;
  }
  .icon:not(:last-child) {
    margin-right: 10px;
  }
}
</style>
