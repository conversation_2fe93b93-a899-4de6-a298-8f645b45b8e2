import request from '@/utils/request'

import { serviceArea } from '../../serviceArea'

let busServiceAreaName = serviceArea.business + '/Edition/'

export function getEditionList(data) {
  data = data || {};
  return request({
    url: busServiceAreaName + 'GetListByCondition',
    method: 'post',
    data
  })
}

export function getList(data) {
  return request({
    url: busServiceAreaName + 'GetListPage',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: busServiceAreaName + 'Add',
    method: 'post',
    data
  })
}

export function edit(data) {
  return request({
    url: busServiceAreaName + 'Edit',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: busServiceAreaName + 'Delete',
    method: 'post',
    data
  })
}

//建仓当前版本是否有未完成的阶段
export function check(data) {
  return request({
    url: busServiceAreaName + 'CheckPublish',
    method: 'post',
    data
  })
}

export function detail(params) {
  return request({
    url: busServiceAreaName + 'GetDetails',
    method: 'get',
    params
  })
}

export function publish(data) {
  return request({
    url: busServiceAreaName + 'Publish',
    method: 'post',
    data
  })
}

export function withdraw(data) {
  return request({
    url: busServiceAreaName + 'Countermand',
    method: 'post',
    data
  })
}


export function getSummary(params) {
  return request({
    url: busServiceAreaName + 'GetTotalAsync',
    method: 'get',
    params
  })
}
// 项目看板的项目版本接口
export function getListAndSubsetByCondition(params) {
  return request({
    url: busServiceAreaName + 'GetListAndSubsetByCondition',
    method: 'get',
    params
  })
}

// 获得版本图表数据
export function getEditionChartAsync(params) {
  return request({
    url: busServiceAreaName + 'GetEditionChartAsync',
    method: 'get',
    params
  })
}
// 获得阶段图表数据
export function getStageChartAsync(params) {
  return request({
    url: busServiceAreaName + 'GetStageChartAsync',
    method: 'get',
    params
  })
}
// 获得迭代图表数据
export function getIterationChartAsync(params) {
  return request({
    url: busServiceAreaName + 'GetIterationChartAsync',
    method: 'get',
    params
  })
}