import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(params) {
    return request({
        url: serviceAreaName + '/OilfieldCompany/FindOilfieldCompanys',
        method: 'get',
        params
    })
}

// export function detail(params) {
//     return request({
//         url: '/OilfieldCompany/FindOilfieldCompanys',
//         method: 'get',
//         params
//     })
// }

export function add(data){
    return request({
        url: serviceAreaName + '/OilfieldCompany/AddOilfieldCompany',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/OilfieldCompany/EditOilfieldCompany',
        method: 'post',
        data
    })
}


export function del(data){
    return request({
        url: serviceAreaName + '/OilfieldCompany/DeleteOilfieldCompanyByIds',
        method: 'post',
        data
    })
}

