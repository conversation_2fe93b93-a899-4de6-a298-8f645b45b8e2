import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
//获取所有集团组织下所有 设备-属性-属性值
export function getList() {
    return request({
        url: serviceAreaName + '/Equipment/GetEquipmentsGroup',
        method: 'get',
    })
}


export function add(data) {
    return request({
        url: serviceAreaName + '/Equipment/add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/Equipment/edit',
        method: 'post',
        data
    })
}

//获取所有
export function getAll(params) {
    return request({
        url: serviceAreaName + '/Equipment/all',
        method: 'get',
        params
    })
}

export function getListByPage(data) {
    return request({
        url: serviceAreaName + '/Equipment/GetListPage',
        method: 'post',
        data
    })
}