<template>
    <div v-if="_isVisible">
        <el-dialog :fullscreen='fullscreen' v-if="isDialog" v-el-drag-dialog class="dialog-mini" :class="[className, {'not-show-line': !showLine}]" :width="`${width}px`" :title="title" :modal="modal" :visible.sync="_isVisible"
            :before-close="beforeClose ? beforeClose : handleClose" :close-on-click-modal="false" :append-to-body="true">
            <template slot="title">
                <slot name="title">
                    <div class="el-dialog__title">{{ title }}</div>
                </slot>
            </template>
            <div :style="maxHeightStyle">
                <slot name="body">

                </slot>
            </div>
            <div slot="footer" v-if="isShowFooter" :style="footerStyle">
                <slot name="footer">
                    <el-button @click="handleClose">取消</el-button>
                    <el-button @click="createData" type="primary">确认</el-button>
                </slot>
            </div>
        </el-dialog>
        <template v-else>
            <slot name="body">

            </slot>
            <slot name="footer" v-if="isShowFooter">

            </slot>
        </template>
    </div>
</template>

<script>
  import elDragDialog from "@/directive/el-dragDialog";
  export default {
    name: "app-dialog",
    directives: {
        elDragDialog
    },
    computed: {
        maxHeightStyle() {
            if(this.maxHeight > 0) {
                return {
                    maxHeight: `${this.maxHeight}px`,
                    overflowY: 'auto'
                }
            }
            return {}
        }
    },
    props: {
        fullscreen: {
            type: Boolean,
            default: false
        },
        width: {
            type: Number,
            default: 1000
        },
        maxHeight: {
            type: Number,
            default: -1
        },
        title: {
            type: String,
            default: ''
        },
        //新增(编辑)弹框是否显示
        dialogFormVisible: {
            required: true,
            type: Boolean
        },
        // 弹窗遮罩
        modal:{
            type:Boolean,
            default: true
        },
        className: {
            type: String,
            default: ''
        },
        isShowFooter: {
            type: Boolean,
            default: true
        },
        //是否为弹框模式，有时候能够通用，但是一类是弹框，一类不是弹框
        isDialog: {
            type: Boolean,
            default: true
        },
        showLine: {
            type: Boolean,
            default: true
        },
        beforeClose: {
            type: Function,
            default: null
        },
        footerStyle: {
            type: [Object, String],
            default: null
        },
    },
    watch: {
        width: {
            handler(val) {
                this.maxWidth = val
            },
            immediate: true
        },
        dialogFormVisible(val) {
            this._isVisible = val
        },
    },
    created() {
        this._isVisible = this.dialogFormVisible;
    },
    data() {
        return {
            maxWidth: 1000,
            _isVisible: false,
            

        };
    },
    methods: {
        handleClose() {
            this.$emit("closeDialog");
        },
        createData(...params) {
            this.$emit("saveSuccess", ...params);
        },
    
    }
};
</script>
