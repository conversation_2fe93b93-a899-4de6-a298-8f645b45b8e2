import request from '@/utils/request'
import { serviceArea } from '../../serviceArea'

let serviceAreaName = serviceArea.business + '/Demand/'
/**获取需求池列表 */
export function getDemandPoolList(data) {
    return request({
        url: serviceAreaName + 'GetDemandPoolListPage',
        method: 'post',
        data
    })
}

/**获取需求列表（非需求池） */
export function getDemandList(data) {
    data = data || {};
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

/**新增 */
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

/**删除 */
export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

/**修改 */
export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function editDemand(data) {
    return request({
        url: serviceAreaName + 'EditDemand',
        method: 'post',
        data
    })
}



/**详情 */
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

/**更新状态 */
export function updateStatus(data) {
    return request({
        url: serviceAreaName + 'UpdateStatus',
        method: 'post',
        data
    })
}

/**更新标签 */
export function updateLable(data) {
    return request({
        url: serviceAreaName + 'UpdateLable',
        method: 'post',
        data
    })
}

/**指派 */
export function assign(data) {
    return request({
        url: serviceAreaName + 'Assign',
        method: 'post',
        data
    })
}

/**标签 */
export function takeNotes(data) {
    return request({
        url: serviceAreaName + 'AddTakeNotes',
        method: 'post',
        data
    })
}


export function batchAddTakeNotes(data) {
    return request({
        url: serviceAreaName + 'BatchAddTakeNotes',
        method: 'post',
        data
    })
}


export function batchEditPrincipalEmployee(data) {
    return request({
        url: serviceAreaName + 'BatchEditPrincipalEmployee',
        method: 'post',
        data
    })
}




/**  */
export function getTakeNote(params) {
    return request({
        url: serviceAreaName + 'GetTakeNote',
        method: 'get',
        params
    })
}

/**  */
export function getEmployees(params) {
    return request({
        url: serviceAreaName + 'GetEmployee',
        method: 'get',
        params
    })
}