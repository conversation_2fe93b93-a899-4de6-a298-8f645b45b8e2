/**
 * 自定义表格共用方法
 *
 *
 */

import Sortable from "sortablejs";
import {
  isShowLeft,
  isShowRight,
  saveTabColumns,
  headerClassNameFlagNum,
  headerClassNameFlagOpt,
  headerClassNameFlagUnDraggable,
  headerClassNameFlagDraggable,
} from "@/utils/customTab";

export default {
  computed: {
    isDragable() {
      return this.viewBusinessType > -1
    },
  },
  props: {
    /**
     * 表格枚举：//需要配置（viewBusinessType > -1）才能支持拖拽
     * 1：超级干线管理首页列表表格（/workbench/mainLineMgmt）
     * 3：超级干线，干线任务列表：taskListVirtual
     * 4：任务管理，/workbench/taskMgmt
     */
    viewBusinessType: {
      type: Number,
      default: -1,
    },

    /**
     * 表格类型：
     * 1：el-table、u-table
     * 2：ux-table
     */
    tableType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      sortableIns: null,
      minWidthCommonCol: 120,
    };
  },

  methods: {
    /**
     * 列拖拽宽度后，需要重新绑定列拖拽排序
     * @param {*} newWidth 
     * @param {*} oldWidth 
     * @param {*} column 
     * @param {*} event 
     */
    handleHeaderDragend(newWidth, oldWidth, column, event) {

      debugger
      debugger

      //最小宽度
      let _min_width = column.minWidth ? column.minWidth : this.minWidthCommonCol
      
      let that = this;
      if (that.isDragable) {
        let currColumn = that.tabColumns.find(s => s.attr.prop == column.property)
        if(currColumn) {
          currColumn.attr.width = newWidth < _min_width ? _min_width : newWidth
        }
        this.$emit("headerDragendSuccess", column.property, newWidth)
      }
    },

    /**
     * ux-grid 改变列宽逻辑
     */
    // handleHeaderDragend2({ $rowIndex, column, columnIndex, $columnIndex, $event }) {
    //   let col = this.columns[this.showTabIdx ? columnIndex - 1 : columnIndex]
    //   if(col) {
    //     let width = column.renderWidth
    //     this.handleHeaderDragend(width, null, {property: col.attr.prop})
    //   }
    // },
    
    /**
     * 序号 列加上特有标识
     * @param {*} param0
     */
    headerCellClassName({ row, column, rowIndex, columnIndex }) {
      //el-table、u-table 取 label，ux-table 取 title
      let label = column.label || column.title;

      if (label == this.indexHeaderText) {
        return `${headerClassNameFlagUnDraggable} ${headerClassNameFlagNum}`; //序号列
      } else if (label == this.optHeaderText) {
        return `${headerClassNameFlagUnDraggable} ${headerClassNameFlagOpt}`; //操作列
      } else if (column.fixed) {
        //冻结列
        return `${headerClassNameFlagUnDraggable}`;
      }
      return this.isDragable ? headerClassNameFlagDraggable : '';
    },

    bindColumnDrop() {
      if (this.isDragable) {
        this.$nextTick(() => {
          this.columnDrop();
        });
      }
    },

    /**
     * 列拖拽
     * @returns 
     */
    columnDrop() {
      // 行拖拽
      // const table = document.querySelector(".el-table__body-wrapper > table");
      // const tbody = table.querySelector("tbody");
      // Sortable.create(tbody, {
      //   animation: 300,
      //   handle: ".el-table__row",
      //   onEnd: evt => {
      //     const oldIndex = evt.oldIndex;
      //     const newIndex = evt.newIndex;
      //     this.tabDatas.splice(newIndex, 0, this.tabDatas.splice(oldIndex, 1)[0]);
      //   },
      // });

      //列拖拽
      // 要侦听拖拽响应的DOM对象
      let wrapperTr = document.querySelector(".el-table__header tr");

      if (this.tableType == 2) {
        wrapperTr = document.querySelector(
          ".elx-table--header-wrapper .elx-table--header .elx-header--row"
        );
      }

      if (!wrapperTr) {
        return false;
      }

      const that = this;

      that.sortableIns && that.sortableIns.destroy();

      that.sortableIns = Sortable.create(wrapperTr, {
        animation: 180,
        delay: 0,
        handle: `.${headerClassNameFlagDraggable}`, //".el-table__cell",
        filter: `.${headerClassNameFlagUnDraggable}`, //过滤序号列（不能拖拽）
        // 结束拖拽后的回调函数
        onEnd: evt => {
          //是否包含“序号”列
          let containNumCol = evt.to.outerHTML.indexOf(headerClassNameFlagNum) > -1;
          //是否包含“操作”列
          let containOptCol = evt.to.outerHTML.indexOf(headerClassNameFlagOpt) > -1;

          if (evt.oldIndex != evt.newIndex) {
            let cols = JSON.parse(JSON.stringify(that.tabColumns));

            /**
             * 思路：
             * 如果是往左移动，当前列 放置到 目标列 前面————因为往左移动，你的后面（右侧）一定有列；
             * 如果是往右移动，当前列 放置到 目标列 后面————因为往右移动，你的前面（左侧）一定有列；
             */

            //往左移动还是往右移动
            let moveDirection = "left";
            if (evt.oldIndex < evt.newIndex) {
              moveDirection = "right";
            }

            //被拖拽的列
            let currCol = cols[containNumCol ? evt.oldIndex - 1 : evt.oldIndex];
            //停靠位置参考列
            let targCol = cols[containNumCol ? evt.newIndex - 1 : evt.newIndex];

            if (currCol && targCol) {
              that.$emit("dragSuccess", {
                direction: moveDirection,
                current: currCol.attr.prop,
                target: targCol.attr.prop,
              });
            }

            // //前端数据移动（表格数据同步）
            // const oldItem = cols[evt.oldIndex];
            // cols.splice(evt.oldIndex, 1);
            // cols.splice(evt.newIndex, 0, oldItem);
          }
        },
        onMove: e => {
          // e 表示即将停靠的元素（不能停靠在序号列、操作列上）
          let containNumCol = e.related.classList.contains(headerClassNameFlagNum);
          let containOptCol = e.related.classList.contains(headerClassNameFlagOpt);

          if (containNumCol || containOptCol) {
            return false;
          }
          return true;
        },
      });
    },
  },
};
