import request from "@/utils/request";
import { serviceArea } from "../serviceArea";

let serviceAreaName = serviceArea.business + "/Hypertrunk/";
export function add(data) {
  return request({
    url: serviceAreaName + "Add",
    method: "post",
    data,
  });
}

export function del(data) {
  return request({
    url: serviceAreaName + "Delete",
    method: "post",
    data,
  });
}

export function edit(data) {
  return request({
    url: serviceAreaName + "Edit",
    method: "post",
    data,
  });
}

export function getList(data) {
  return request({
    url: serviceAreaName + "GetListPage",
    method: "post",
    data,
  });
}

export function getListPageCard(data) {
  return request({
    url: serviceAreaName + "ListPageCard",
    method: "post",
    data,
  });
}

export function detail(params) {
  return request({
    url: serviceAreaName + "GetDetails",
    method: "get",
    params,
  });
}

export function editInfo(data) {
  return request({
    url: serviceAreaName + "EditInfo",
    method: "post",
    data,
  });
}

export function workbenchStatistics(params) {
  return request({
    url: serviceAreaName + "WorkbenchStatistics",
    method: "get",
    params,
  });
}

export function publish(data) {
  return request({
    url: serviceAreaName + "Publish",
    method: "post",
    data,
  });
}

export function complete(data) {
  return request({
    url: serviceAreaName + "Complete",
    method: "post",
    data,
  });
}

export function urge(data) {
  return request({
    url: serviceAreaName + "ToUrge",
    method: "post",
    data,
  });
}

export function shelve(data) {
  return request({
    url: serviceAreaName + "Shelve",
    method: "post",
    data,
  });
}

export function restart(data) {
  return request({
    url: serviceAreaName + "Restart",
    method: "post",
    data,
  });
}

export function abort(data) {
  return request({
    url: serviceAreaName + "Abort",
    method: "post",
    data,
  });
}

export function referenceCreation(data) {
  return request({
    url: serviceAreaName + "ReferenceCreation",
    method: "post",
    data,
  });
}

export function pending(data) {
  return request({
    url: serviceAreaName + "Pending",
    method: "post",
    data,
  });
}

export function getChildTree(data) {
  return request({
    url: serviceAreaName + "GetChildTree",
    method: "post",
    data,
  });
}

export function getInitiatorEmployee(params) {
  return request({
    url: serviceAreaName + "GetInitiatorEmployee",
    method: "get",
    params,
  });
}

export function getDataPermissions(params) {
  return request({
    url: serviceAreaName + "GetDataPermissions",
    method: "get",
    params,
  });
}

export function getBurndownChart(data) {
  return request({
    url: serviceAreaName + "BurndownChart",
    method: "post",
    data,
  });
}

export function getTaskViewStatistics(data) {
  return request({
    url: serviceAreaName + "TaskViewStatistics",
    method: "post",
    data,
  });
}
export function getWorkStatistics(params) {
  return request({
    url: serviceAreaName + "WorkStatistics",
    method: "get",
    params,
  });
}
export function editNotice(data) {
  return request({
    url: serviceAreaName + "EditNotice",
    method: "post",
    data,
  });
}
export function getTaskFollow(data) {
  return request({
    url: serviceAreaName + "TaskFollow",
    method: "post",
    data,
  });
}

export function follow(data) {
  return request({
    url: serviceAreaName + "Follow",
    method: "post",
    data,
  });
}


export function getTaskLag(data) {
  return request({
    url: serviceAreaName + "TaskLag",
    method: "post",
    data,
  });
}
export function getHangStatistics(data) {
  return request({
    url: serviceAreaName + "HangStatistics",
    method: "post",
    data,
  });
}
export function getTaskExceptionStatistics(data) {
  return request({
    url: serviceAreaName + "TaskExceptionStatistics",
    method: "post",
    data,
  });
}
export function getTaskDoneTrendAnalysis(data) {
  return request({
    url: serviceAreaName + "TaskDoneTrendAnalysis",
    method: "post",
    data,
  });
}
export function approval(data) {
  return request({
    url: serviceAreaName + "Approval",
    method: "post",
    data,
  });
}
export function revocation(data) {
  return request({
    url: serviceAreaName + "Revocation",
    method: "post",
    data,
  });
}
export function getGoalStatistics(params) {
  return request({
    url: serviceAreaName + "GoalStatistics",
    method: "get",
    params,
  });
}

export function financialGoals(data) {
  return request({
    url: serviceAreaName + "FinancialGoals",
    method: "post",
    data,
  });
}

export function getHypertrunkReport(data) {
  return request({
    url: serviceAreaName + "GetHypertrunkReport",
    method: "post",
    data,
  });
}

export function getHypertrunkRelation(params) {
  return request({
    url: serviceAreaName + "GetHypertrunkRelation",
    method: "get",
    params,
  });
}