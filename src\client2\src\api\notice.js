import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/Notice/GetListPage',
        method: 'post',
        data
    })
}

export function add(data){
    return request({
        url: serviceAreaName + '/Notice/Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/Notice/Edit',
        method: 'post',
        data
    })
}


export function del(data){
    return request({
        url: serviceAreaName + '/Notice/Delete',
        method: 'post',
        data
    })
}

export function detail(params){
    return request({
        url: serviceAreaName + '/Notice/GetDetails',
        method: 'get',
        params
    })
}

