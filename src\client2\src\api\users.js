import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.user
export function getList(params) {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'get',
    params
  })
}

// export function get(params) {
//   return request({
//     url: '/Users/<USER>',
//     method: 'get',
//     params
//   })
// }

// export function add(data) {
//   return request({
//     url: '/Users/<USER>',
//     method: 'post',
//     data
//   })
// }

// export function update(data) {
//   return request({
//     url: '/Users/<USER>',
//     method: 'post',
//     data
//   })
// }

// export function del(data) {
//   return request({
//     url: '/Users/<USER>',
//     method: 'post',
//     data
//   })
// }



//-------------- 新版api ------------
export function getList222(data) {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'post',
    data
  })
}

export function add222(data) {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'post',
    data
  })
}

export function update222(data) {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'post',
    data
  })
}

export function del222(data) {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'post',
    data
  })
}

//获取企业管理员信息
export function getEnterpriseAdministrator(params) {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'get',
    params
  })
}

export function detail(params) {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'get',
    params
  })
}

export function updatePersonalInfo(data) {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'post',
    data
  })
}

export function getCurrentUserInfo() {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'get'
  })
}

//获取维修单审批人员信息
export function getUserOfRepairOrder() {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'get'
  })
}

//获取维修单抄送人员
export function getCCPersonnels() {
  return request({
    url: serviceAreaName + '/Users/<USER>',
    method: 'get'
  })
}

