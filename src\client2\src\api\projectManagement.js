import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

export function getList(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/GetProjectManagementList',
        method: 'post',
        data
    })
}

export function getAllList(params) {
    return request({
        url: serviceAreaName + '/ProjectManagement/GetAllProjectManagementList',
        method: 'get',
        params
    })
}


export function getEmployeeDtoById(params) {
    return request({
        url: serviceAreaName + '/ProjectManagement/GetEmployeeDto',
        method: 'get',
        params
    })
}


export function detail(params) {
    return request({
        url: serviceAreaName + '/ProjectManagement/GetProjectManagementDetails',
        method: 'get',
        params
    })
}

export function getApprovals(params) {
    return request({
        url: serviceAreaName + '/ProjectManagement/GetProjectManagementInitiateApprovals',
        method: 'get',
        params
    })
}


export function getAllProductList(params) {
    return request({
        url: serviceAreaName + '/ProductManagement/GetAllProductList',
        method: 'get',
        params
    })
}


export function add(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/AddProjectManagement',
        method: 'post',
        data
    })
}


export function del(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/DeleteProjectManagement',
        method: 'post',
        data
    })
}


export function edit(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/EditProjectManagement',
        method: 'post',
        data
    })
}

export function approval(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/ApprovalProjectManagement',
        method: 'post',
        data
    })
}


export function favorite(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/FavoriteProjectManagement',
        method: 'post',
        data
    })
}


export function exports(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/ExportProjectManagement',
        method: 'post',
        data
    })
}




//项目验收接口

export function getAcceptanceInspection(params) {
    return request({
        url: serviceAreaName + '/ProjectManagement/GetProjectAcceptanceInspection',
        method: 'get',
        params
    })
}
export function addAcceptanceInspection(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/AddProjectAcceptanceInspection',
        method: 'post',
        data
    })
}
export function editAcceptanceInspection(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/EditProjectAcceptanceInspection',
        method: 'post',
        data
    })
}
export function approvalAcceptanceInspection(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/ApprovalProjectAcceptanceInspection',
        method: 'post',
        data
    })
}


//评论接口
export function getCommentListPage(data) {
    return request({
        url: serviceAreaName + '/Common/GetCommentListPage',
        method: 'post',
        data
    })
}

export function addcomment(data) {
    return request({
        url: serviceAreaName + '/Common/AddComment',
        method: 'post',
        data
    })
}


export function getOptRecord(data) {
    return request({
        url: serviceAreaName + '/ProjectManagement/GetOperationRecord',
        method: 'post',
        data
    })
} 