<template>
  <div class="app-upload-file">
    <el-dialog :visible.sync="dialogVisible" :close-on-click-modal='false' :append-to-body='true'>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <ul class="upload-file-list clearfix">
      <li v-for="(item, idx) in files" :key="idx" @mouseover="() => delIdx = idx" @mouseout="() => delIdx = -1">
        <!-- <a :href="preview ? item[fileProp.filePath] : 'javascript:void(0);'" target="_blank">
                    <img
                        class="file-item"
                        alt="点击放大"
                        :src="item[fileProp.filePath]"
                    >
                </a> -->
        <a>
          <img v-show="fileType == 1" @click="handlePictureCardPreview(item[fileProp.filePath])" class="file-item file-item-img" alt="点击放大" :src="item[fileProp.filePath]">
          <video style='width: 180px; height: 180px; background: #ccc;' controls="controls" v-show="fileType == 3" :src="item[fileProp.filePath]">
          </video>
          <div v-show="fileType == 4" class="file-item file-item-other">
            <i class="el-icon-document file-logo"></i>
          </div>
        </a>
        <i v-if="!readonly" v-show="delIdx == idx" class="file-btn-del el-icon-remove" @click.stop="removeFile(idx)"></i>
      </li>
      <li>
        <!-- 只读状态和上传中，不能点击 -->
        <el-upload :file-list='files' ref="upload" class="inline-item" :action="'string'" :before-upload="beforeUpload" :on-change="handleChange" :on-success="handleUploadSuccess" :http-request="uploadFileMethod" :on-error="handleUploadError" :disabled="readonly || uploading" :show-file-list="false" :multiple="multiple" :limit="limit" :on-exceed="handleExceed">
          <!-- element-loading-text="上传中..." -->
          <el-button :class="['upload-btn', 'icon-btn']" v-loading='uploading' element-loading-background="rgba(255, 255, 255, 0.8)" :type="'primary'" size="small" v-show="showUploadBtn">
            {{ uploading ? '正在上传' : '点击上传' }}
            <i class="el-icon-plus icon-plus"></i>
          </el-button>
        </el-upload>
        <!-- <el-button
                    @click="uploadBigFile"
                    :class="['upload-btn', 'icon-btn']"
                    v-loading='uploading'
                    element-loading-background="rgba(255, 255, 255, 0.8)"
                    :type="'primary'"
                    size="small"
                    v-show="showUploadBtn"
                >
                    {{ uploading ? '正在上传' : '点击上传' }}
                    <i class="el-icon-plus icon-plus"></i>
                </el-button> -->

      </li>
    </ul>
  </div>
</template>

<script>
import * as common from '@/api/common'
import { eventHub } from "../../eventHub"
import uploaderMixin from '@/mixins/uploader'
import { compress } from '@/utils'
export default {
  name: 'app-upload-file',
  props: {
    value: Array, // [{Id: '', Path: ''}]
    readonly: {
      type: Boolean,
      default: false
    },
    //上传文件格式限制（如果 fileType == 4，不校验格式——表示接受所有格式）
    accept: {
      type: String,
      default: 'jpg,jpeg,png',
    },
    //上传文件最大数量
    max: {
      type: Number,
      default: 3
    },
    //上传文件大小限制（默认不超过10mb）
    fileSize: {
      type: Number,
      default: 1024 * 1024 * 10,
    },
    //上传文件最小限制（-1表示不限制）——后续作废
    minFileSize: {
      type: Number,
      default: -1
    },
    fileType: {
      type: Number,
      default: 1, //1:图片;2:excel;3:视频;4:其他附件
    },
    //是否可预览
    preview: {
      type: Boolean,
      default: false
    },
    //是否支持多选
    multiple: {
      type: Boolean,
      default: false
    }, 
    limit: {
      type: Number,
      default: 1
    }
  },
  // watch: {
  //     uploading(newVal){
  //         console.log(newVal + ' xxxxxxxxxxxxxxxxxxxxxxxxx')
  //     }
  // },
  mixins: [uploaderMixin],
  data() {
    return {
      delIdx: -1,
      fileProp: {
        fileKey: 'Id',
        filePath: 'Path',
        fileMd5value: 'FileMd5value',
      },
      files: [],
      uploading: false,
      dialogVisible: false,
      dialogImageUrl: '',

    }
  },
  computed: {
    showUploadBtn() {
      return (this.files.length < this.max) && !this.readonly
    },
    acceptStr() {
      return this.accept.toLowerCase()
    }
  },
  destroyed() {
    this.onDestroyed()
  },
  mounted() {
    this.initFiles(this.value)
    // 文件选择后的回调
    this.onDestroyed()
    eventHub.$on('fileAdded', () => {
      // console.log('文件已选择')
    });
    // 文件上传成功的回调
    eventHub.$on('fileSuccess', (res) => {
      let self = this
      if (res && res.length > 0) {
        res.forEach(i => {
          let fileTmp = {
            [self.fileProp.fileKey]: i.Id,
            [self.fileProp.filePath]: i.Path,
            [self.fileProp.fileMd5value]: i.FileMd5value,
          }
          this.files.push(fileTmp)
        });
        this.filesChange()
      }
      console.log('文件上传成功')
    });
  },
  watch: {
    value: {
      handler(newval) {
        this.initFiles(newval)
      },
      deep: true
    }
  },
  methods: {
    handlePictureCardPreview(url) {
      if (this.preview) {
        this.dialogVisible = true
        this.dialogImageUrl = url
      }
    },
    initFiles(data) {
      let self = this;
      // if (_.isString(data)) {
      //     this.files = data.split(',').map(v => ({
      //         [self.fileProp.fileKey]: v.Id,
      //         [self.fileProp.filePath]: v.Path
      //     }))
      // } else {
      this.files = data || []
      // }
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },    
    onDestroyed() {
      eventHub.$off('fileAdded');
      eventHub.$off('fileSuccess');
    },
    removeFile(index) {
      let file = this.files[index]
      this.$refs.upload.abort(file) // upload: el-upload的ref值；file:File类型，file文件本身
      this.files.splice(index, 1)
      this.handleRemove(file, this.files) // 调用钩子函数on-remove
      this.filesChange()
    },
    filesChange() {
      this.$emit("change", this.files)
    },
    // conver(limit) {
    //     var size = "";
    //     if (limit < 0.1 * 1024) { //如果小于0.1KB转化成B  
    //         size = limit.toFixed(2) + "B";
    //     } else if (limit < 0.1 * 1024 * 1024) {//如果小于0.1MB转化成KB  
    //         size = (limit / 1024).toFixed(2) + "KB";
    //     } else if (limit < 1 * 1024 * 1024 * 1024) { //如果小于1GB转化成MB  
    //         size = (limit / (1024 * 1024)).toFixed(2) + "MB";
    //     } else { //其他转化成GB  
    //         size = (limit / (1024 * 1024 * 1024)).toFixed(2) + "GB";
    //     }

    //     var sizestr = size + "";
    //     var len = sizestr.indexOf(".");
    //     var dec = sizestr.substr(len + 1, 2);
    //     if (dec == "00") {//当小数点后为00时 去掉小数部分  
    //         return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
    //     }
    //     return sizestr;
    // },
    handleChange(file, fileList) {
      //图片压缩
      // https://blog.csdn.net/liona_koukou/article/details/84860899

    },
    //上传之前的事件
    beforeUpload(file) {
      let accept = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase(),
        typePass = ~this.acceptStr.indexOf(accept) || !this.acceptStr || this.acceptStr == 'all',
        sizePass = file.size < this.fileSize || !this.fileSize,
        // minSizePass = this.minFileSize == -1 || this.minFileSize > 0 && file.size > this.minFileSize || !this.minFileSize,
        maxLimit = this.files.length < this.max;

      let limitNotice = this.conver(this.fileSize)
      // let minLimitNotice = this.conver(this.minFileSize)

      !typePass && this.$message.error(`上传文件只能是 ${this.acceptStr} 格式!`);
      !sizePass && this.$message.error(`上传文件大小不能超过 ${limitNotice}!`);
      // !minSizePass && this.$message.error(`上传文件大小不能小于 ${minLimitNotice}!`);
      !maxLimit && this.$message.error(`上传文件不能超过 ${this.max} 张!`)

      let isUploadable = typePass
        && sizePass
        // && minSizePass 
        && maxLimit
      this.uploading = isUploadable
      return isUploadable;
    },
    //压缩并上传
    async comp(file) {

      let fileObj = file
      // if(this.fileType == 1){
      //     fileObj = await compress(file).then(res => {
      //         return res
      //     })
      // }

      let self = this;
      let config = {
        headers: { 'Content-Type': 'multipart/form-data' },
      }

      let formData = new FormData();

      formData.append("file", fileObj, file.name);
      formData.append('type', this.fileType)

      let postRes = null
      if (this.fileType == 3) { //上传视频
        config.timeout = 1000 * 60 * 10
        postRes = common.postVideoFile(formData, config) //上传成功返回对象
      } else if (this.fileType == 1) {
        postRes = common.postFile(formData, config) //上传成功返回集合
      } else if (this.fileType == 4) {
        config.timeout = 1000 * 60 * 10
        postRes = common.postOther(formData, config)
      }

      if (postRes) {
        postRes.then(res => {
          this.$message({
            showClose: true,
            message: "上传成功。",
            type: "success"
          });
          if (this.fileType == 3 || this.fileType == 4) {
            this.files.push({
              [self.fileProp.fileKey]: res[0].Id,
              [self.fileProp.filePath]: res[0].Path,
              [self.fileProp.fileMd5value]: res[0].FileMd5value
            })
          } else {
            this.files.push({
              [self.fileProp.fileKey]: res.Id,
              [self.fileProp.filePath]: res.Path,
              [self.fileProp.fileMd5value]: res.FileMd5value
            })
          }
          this.uploading = false
          this.filesChange()
        }).catch(err => {
          this.uploading = false
        })
      }
    },
    uploadFileMethod(param) {
      this.comp(param.file)
    },
    // 上传成功的事件
    handleUploadSuccess(res, file) {

    },
    // 上传文件限制的事件
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    // 上传失败
    handleUploadError(err) {
      this.uploading = false
      this.$message.error('上传失败，请稍后再试！')
    },
    // uploadBigFile() {
    //     // 打开文件选择框
    //     eventHub.$emit('openUploader', {
    //         id: '1111',  // 传入的参数
    //         validateObj: {
    //             accept: this.accept,
    //             fileSize: this.fileSize,
    //             max: this.max,
    //             total: this.files.length,
    //         }
    //     })
    // },
  }
}
</script>

<style scoped>
.upload-file-list {
  display: inline-block;
  margin-right: 10px;
  vertical-align: middle;
  line-height: 1;
  list-style: none;
  padding-left: 0;

  box-sizing: border-box;
  margin: 0 !important;
}

.upload-file-list li {
  float: left;
  position: relative;
  margin-right: 10px;
}

.file-item {
  display: inline-block;
  position: relative;
  width: 120px;
  height: 120px;
}
.file-item-img {
  cursor: pointer;
}
.file-item-other {
  cursor: auto;
  background-color: #efefef;
}
.file-logo {
  font-size: 40px;
  text-align: center;
  position: absolute;
  margin-top: -20px;
  top: 50%;
  margin-left: -20px;
  left: 50%;
}

.file-img {
  display: inline-block;
  padding: 2px;
  border-radius: 4px;
  vertical-align: middle;
  border-width: 1px;
  border-style: dashed;
}

.file-btn-del {
  background: transparent;
  display: block;
  cursor: pointer;
  position: absolute;
  font-size: 18px;
  top: -6px;
  right: -6px;
}

.file-btn-del:hover {
  transition: all 0.3s;
  color: red;
}

.upload-btn {
  position: relative;
  vertical-align: middle;
}

.icon-btn {
  width: 120px;
  height: 120px;
  padding: 0;
  font-size: 0;
  background: none;
  border-style: dashed;
  text-align: center;
}

.icon-plus {
  font-size: 24px;
  color: #8c939d;
}
</style>
