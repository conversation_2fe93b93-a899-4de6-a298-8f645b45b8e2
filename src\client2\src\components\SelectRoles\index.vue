<template>
  <div>
    <treeselect
      :noResultsText="noResultsTextOfSelTree"
      :noOptionsText="noOptionsTextOfSelTree"
      :normalizer="normalizer"
      :default-expand-level="3"
      :options="orgs"
      :multiple="false"
      :show-count="true"
      @select="onSelect"
      @input="input"
    ></treeselect>
    <el-transfer
      v-model="selectRoles"
      :data="orgRoles"
      @change="handleChange"
      :titles="['系统角色', '已选角色']"
    ></el-transfer>
  </div>
</template>

<script>
import { listToTreeSelect } from "@/utils";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import * as login from "@/api/login";
import * as orgs from "@/api/organization";
import * as apiRoles from "@/api/roles";
import { getUserInfo } from "@/utils/auth";

export default {
  name: "select-roles",
  components: {
    Treeselect
  },
  props: ["roles"],
  data() {
    // todo:兼容layui的样式、图标
    return {
      normalizer(node) {
        // treeselect定义字段
        return {
          label: node.label,
          id: node.Id,
          children: node.children
        };
      },
      selectRoles: this.roles,
      orgRoles: [], // 该组织的全部角色
      orgs: []
    };
  },
  watch: {
    roles(val) {
      // 因为组件只挂载一次，后期只能通过watch来改变selectusers的值
      this.selectRoles = val;
    }
  },
  mounted() {
    var _this = this;
    orgs.getValidOrgs().then(response => {
      var orgs = response.map(function(item, index, input) {
        return {
          Id: item.OrganizationId,
          label: item.Name,
          ParentId: item.ParentId
        };
      });
      var tree = listToTreeSelect(orgs);

      _this.orgs = tree;
      _this.getOrgRoles("");
    });
  },
  methods: {
    getOrgRoles(orgId) {
      var _this = this;
      apiRoles.getList({ orgId: orgId }).then(response => {
        _this.orgRoles = response.map(function(item, index, input) {
          return { key: item.Id, label: item.Name };
        });
      });
    },
    onSelect: function(node, instanceId) {
      this.getOrgRoles(node.Id);
    },
    input: function(node, instanceId) {
      if (node === undefined) {
        // 清空选择
        this.getOrgRoles("");
      }
    },
    handleChange(value, direction, movedKeys) {
      this.$emit("roles-change", this.selectRoles);
    }
  }
};
</script>

<style scoped>
.el-transfer {
  margin-top: 10px;
}
</style>
