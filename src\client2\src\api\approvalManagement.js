import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business+'/HRApprovalProcess/';
let servicePersonalInfo=serviceArea.business+'/HRpersonalInfo/';

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPageNew',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}


export function del(data){
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}
export function delPersonalInfo(data){
    return request({
        url: servicePersonalInfo + 'Delete',
        method: 'post',
        data
    })
}
export function getListByCondition(data){
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function editVisible(params) {
    return request({
        url: serviceAreaName + 'EditVisible',
        method: 'get',
        params
    })
}

export function getSignHRApprovalProcess(data) {
    return request({
        url: serviceAreaName + 'GetSignHRApprovalProcess',
        method: 'post',
        data
    })
}

export function ChangeClassify(data) {
    return request({
        url: serviceAreaName + 'ChangeClassify',
        method: 'post',
        data
    })
}

export function infoAdd(data){
    return request({
        url: servicePersonalInfo + 'Add',
        method: 'post',
        data
    })
}

export function getApprovalInfo(params) {
    return request({
        url: servicePersonalInfo + 'GetApprovalInfo',
        method: 'get',
        params
    })
}


export function getPersonalDetails(params) {
    return request({
        url: servicePersonalInfo + 'GetDetails',
        method: 'get',
        params
    })
}


export function createApproval(data){
    return request({
        url: servicePersonalInfo + 'Approval',
        method: 'post',
        data
    })
}

export function getHistories(data) {
    return request({
        url: servicePersonalInfo + 'GetListPage',
        method: 'post',
        data
    })
}

export function revocation(data) {
    return request({
        url: servicePersonalInfo + 'Revocation',
        method: 'post',
        data
    })
}

export function approvalRevocation(data) {
    return request({
        url: servicePersonalInfo + 'ApprovalRevocation',
        method: 'post',
        data
    })
}

export function businessTripEnd(data) {
    return request({
        url: servicePersonalInfo + 'BusinessTripEnd',
        method: 'post',
        data
    })
}

export function getDetect(params) {
    return request({
        url: servicePersonalInfo + 'GetDetect',
        method: 'get',
        params
    })
}

export function getSignHRApprovalProcessByMonth(data) {
    return request({
        url: serviceAreaName + 'GetSignHRApprovalProcessByMonth',
        method: 'post',
        data
    })
}
export function temporizeApi(data) {
    return request({
        url: servicePersonalInfo + 'Temporize',
        method: 'post',
        data
    })
}
export function editPaymentStateApi(data) {
    return request({
        url: servicePersonalInfo + 'EditPaymentState',
        method: 'post',
        data
    })
}