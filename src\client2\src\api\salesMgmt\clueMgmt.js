import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/OpportunityThread/'

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}


export function termination(data) {
    return request({
        url: serviceAreaName + 'Termination',
        method: 'post',
        data
    })
}


export function saveFollowUpNotes(data) {
    return request({
        url: serviceAreaName + 'SaveFollowUpNotes',
        method: 'post',
        data
    })
}

export function getDataStatistics(data) {
    return request({
        url: serviceAreaName + 'DataStatistics',
        method: 'post',
        data
    })
}

export function getPortrait(data) {
    return request({
        url: serviceAreaName + 'GetPortrait',
        method: 'post',
        data
    })
}


