/* Background */ .highlight-bg { background-color: #ffffff }
/* PreWrapper */ .highlight-chroma { background-color: #ffffff; }
/* Error */ .highlight-chroma .highlight-err { color: #ff0000; background-color: #ffaaaa }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .highlight-chroma .highlight-hl { background-color: #e5e5e5 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Line */ .highlight-chroma .highlight-line { display: flex; }
/* Keyword */ .highlight-chroma .highlight-k { color: #0000aa }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #0000aa }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #0000aa }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #0000aa }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #0000aa }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #0000aa }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #00aaaa }
/* NameAttribute */ .highlight-chroma .highlight-na { color: #1e90ff }
/* NameBuiltin */ .highlight-chroma .highlight-nb { color: #00aaaa }
/* NameClass */ .highlight-chroma .highlight-nc { color: #00aa00; text-decoration: underline }
/* NameConstant */ .highlight-chroma .highlight-no { color: #aa0000 }
/* NameDecorator */ .highlight-chroma .highlight-nd { color: #888888 }
/* NameEntity */ .highlight-chroma .highlight-ni { color: #880000; font-weight: bold }
/* NameFunction */ .highlight-chroma .highlight-nf { color: #00aa00 }
/* NameNamespace */ .highlight-chroma .highlight-nn { color: #00aaaa; text-decoration: underline }
/* NameTag */ .highlight-chroma .highlight-nt { color: #1e90ff; font-weight: bold }
/* NameVariable */ .highlight-chroma .highlight-nv { color: #aa0000 }
/* LiteralString */ .highlight-chroma .highlight-s { color: #aa5500 }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #aa5500 }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #aa5500 }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #aa5500 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #aa5500 }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #aa5500 }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #aa5500 }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #aa5500 }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #aa5500 }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #aa5500 }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #aa5500 }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #009999 }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #aa5500 }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #0000aa }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #009999 }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #009999 }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #009999 }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #009999 }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #009999 }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #009999 }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #009999 }
/* OperatorWord */ .highlight-chroma .highlight-ow { color: #0000aa }
/* Comment */ .highlight-chroma .highlight-c { color: #aaaaaa; font-style: italic }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #aaaaaa; font-style: italic }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #aaaaaa; font-style: italic }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #aaaaaa; font-style: italic }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #0000aa; font-style: italic }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #4c8317 }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #4c8317 }
/* GenericDeleted */ .highlight-chroma .highlight-gd { color: #aa0000 }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericError */ .highlight-chroma .highlight-gr { color: #aa0000 }
/* GenericHeading */ .highlight-chroma .highlight-gh { color: #000080; font-weight: bold }
/* GenericInserted */ .highlight-chroma .highlight-gi { color: #00aa00 }
/* GenericOutput */ .highlight-chroma .highlight-go { color: #888888 }
/* GenericPrompt */ .highlight-chroma .highlight-gp { color: #555555 }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #800080; font-weight: bold }
/* GenericTraceback */ .highlight-chroma .highlight-gt { color: #aa0000 }
/* GenericUnderline */ .highlight-chroma .highlight-gl { text-decoration: underline }
/* TextWhitespace */ .highlight-chroma .highlight-w { color: #bbbbbb }

/* Base16 Atelier Heath Light - Theme */
/* by Bram de Haan (http://atelierbram.github.io/syntax-highlighting/atelier-schemes/heath) */
/* Original Base16 color scheme by Chris Kempson (https://github.com/chriskempson/base16) */

/* Atelier-Heath Comment */
.hljs-comment,
.hljs-quote {
    color: #776977;
}

/* Atelier-Heath Red */
.hljs-variable,
.hljs-template-variable,
.hljs-attribute,
.hljs-tag,
.hljs-name,
.hljs-regexp,
.hljs-link,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class {
    color: #ca402b;
}

/* Atelier-Heath Orange */
.hljs-number,
.hljs-meta,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params {
    color: #a65926;
}

/* Atelier-Heath Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet {
    color: #918b3b;
}

/* Atelier-Heath Blue */
.hljs-title,
.hljs-section {
    color: #516aec;
}

/* Atelier-Heath Purple */
.hljs-keyword,
.hljs-selector-tag {
    color: #7b59c0;
}

.hljs {
    display: block;
    overflow-x: auto;
    background: #f7f3f7;
    color: #695d69;
    padding: 0.5em;
}

.hljs-emphasis {
    font-style: italic;
}

.hljs-strong {
    font-weight: bold;
}

