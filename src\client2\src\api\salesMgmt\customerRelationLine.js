import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/Customer/'
        
// ​/api​/Business​/Customer​/GetCustomerRelationLineListAsync

export function add(data) {
    return request({
        url: serviceAreaName + 'AddCustomerRelationLine',
        method: 'post',
        data
    })
}
export function del(data) {
    return request({
        url: serviceAreaName + 'DeleteCustomerRelationLine',
        method: 'post',
        data
    })
}
export function edit(data) {
    return request({
        url: serviceAreaName + 'EditCustomerRelationLine',
        method: 'post',
        data
    })
}
export function getCustomerRelationLineList(params) {
    return request({
        url: serviceAreaName + 'GetCustomerRelationLineList',
        method: 'get',
        params
    })
}
export function getCustomerRelationLineDetails(params) {
    return request({
        url: serviceAreaName + 'GetCustomerRelationLineDetails',
        method: 'get',
        params
    })
}

export function batchEdit(data) {
    return request({
        url: serviceAreaName + 'BatchEdit',
        method: 'post',
        data
    })
}

export function getCustomerDataStatistics(data) {
    return request({
        url: serviceAreaName + 'CustomerDataStatistics',
        method: 'post',
        data
    })
}

export function getGetCustomerPortrait(data) {
    return request({
        url: serviceAreaName + 'GetCustomerPortrait',
        method: 'post',
        data
    })
}




