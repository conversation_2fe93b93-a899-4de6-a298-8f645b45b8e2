import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
    return request({
        url: serviceAreaName + '/Feedback/GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName +'/Feedback/GetDetails',
        method: 'get',
        params
    })
}

export function add(data) {
    return request({
        url: serviceAreaName +'/Feedback/Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName +'/Feedback/Edit',
        method: 'post',
        data
    })
}


export function del(data){
    return request({
        url: serviceAreaName +'/Feedback/Delete',
        method: 'post',
        data
    })
}

