import request from "@/utils/request";
import axios from "axios";
import { serviceArea } from "./serviceArea";

let serviceAppraName = serviceArea.business + "/Values";

export function testOcrUpLoadApi(data) {
  return request({
    url: serviceAppraName + "/TestOcrUpLoad",
    method: "post",
    data,
    config: {
      timeout: 180 * 1000,
    },
  });
}

export function testOcrUpLoadApiWithCancel(data) {
  // 创建一个取消令牌源
  const source = axios.CancelToken.source();
  
  // 发起请求并添加取消令牌
  const promise = request({
    url: serviceAppraName + "/TestOcrUpLoad",
    method: "post",
    data,
    cancelToken: source.token, // 添加取消令牌
    config: {
      timeout: 180 * 1000,
    },
  });
  
  // 返回请求的 promise 和取消函数
  return {
    promise,
    cancel: () => source.cancel('cancelRequest')
  };
}
