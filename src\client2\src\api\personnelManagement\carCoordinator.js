
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let carCoordinatorServiceName = serviceArea.business + '/CarCoordinatorManagement/'


export function getCarStateAllList() {
    return request({
        url: carCoordinatorServiceName + 'GetCarStateAllData',
        method: 'get'
    })
}

export function getList(data) {
    return request({
        url: carCoordinatorServiceName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: carCoordinatorServiceName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: carCoordinatorServiceName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: carCoordinatorServiceName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: carCoordinatorServiceName + 'Delete',
        method: 'post',
        data
    })
}


export function getDepartmentList(data) {
    return request({
        url: carCoordinatorServiceName + 'GetCarCoordinatorDepartmentListPage',
        method: 'post',
        data
    })
}


export function getDepartmentCondition(data) {
    return request({
        url: carCoordinatorServiceName + 'GetCarCoordinatorDepartmentListByCondition',
        method: 'post',
        data
    })
}

export function addDepartment(data) {
    return request({
        url: carCoordinatorServiceName + 'AddCarCoordinatorDepartment',
        method: 'post',
        data
    })
}

export function editDepartment(data) {
    return request({
        url: carCoordinatorServiceName + 'EditCarCoordinatorDepartment',
        method: 'post',
        data
    })
}

export function getDepartmentDetail(params) {
    return request({
        url: carCoordinatorServiceName + 'GetCarCoordinatorDepartmentDetails',
        method: 'get',
        params
    })
}

export function delDepartment(data) {
    return request({
        url: carCoordinatorServiceName + 'DeleteCarCoordinatorDepartment',
        method: 'post',
        data
    })
}
