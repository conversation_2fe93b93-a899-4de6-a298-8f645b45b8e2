import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + '/EquipmentSetting'

/**新增 */
export function add(data) {
    return request({
        url: serviceAreaName + '/Add',
        method: 'post',
        data
    })
}

/**新增集合 */
export function addList(data) {
    return request({
        url: serviceAreaName + '/AddList',
        method: 'post',
        data
    })
}

/**删除 */
export function del(data) {
    return request({
        url: serviceAreaName + '/Delete',
        method: 'post',
        data
    })
}

/**修改 */
export function edit(data) {
    return request({
        url: serviceAreaName + '/Edit',
        method: 'post',
        data
    })
}

/**获取详情 */
export function getDetails(params) {
    return request({
        url: serviceAreaName + '/GetDetails',
        method: 'get',
        params
    })
}

/**获取子节点数目 */
export function getChildrenNumber(params) {
    return request({
        url: serviceAreaName + '/GetChildrenNumber',
        method: 'get',
        params
    })
}

/**获取分页列表 */
export function getListPage(data) {
    return request({
        url: serviceAreaName + '/GetListPage',
        method: 'post',
        data
    })
}

/**获取条件查询所有内容 */
export function getListByCondition(data) {
    return request({
        url: serviceAreaName + '/GetListByCondition',
        method: 'post',
        data
    })
}