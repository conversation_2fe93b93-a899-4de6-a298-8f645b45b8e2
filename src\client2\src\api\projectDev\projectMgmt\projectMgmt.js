import request from '@/utils/request'
import { serviceArea } from '../../serviceArea'

let serviceAreaName = serviceArea.business + '/Project/'
let serviceAreaNameFormAccept = serviceArea.business + '/ProjectAcceptanceApplication/'
export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function addChange(data) {
    return request({
        url: serviceAreaName + 'AddChange',
        method: 'post',
        data
    })
}



export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function detail(params){
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

//创建项目审批
export function createApproval(data) {
    return request({
        url: serviceAreaName + 'ApprovalAsync',
        method: 'post',
        data
    })
}


export function getAllProjecByProductId(data) {
    return request({
        url: serviceAreaName + 'GetProjecByProductId',
        method: 'post',
        data
    })
}


export function getProjectGroupTypeCount(params) {
    return request({
        url: serviceAreaName + 'GetProjectGroupTypeCountAsync',
        method: 'get',
        params
    })
}


export function addAccept(data) {
    return request({
        url: serviceAreaNameFormAccept + 'Add',
        method: 'post',
        data
    })
}

export function getAcceptList(data) {
    return request({
        url: serviceAreaNameFormAccept + 'GetListPage',
        method: 'post',
        data
    })
}

export function getAcceptDetail(params) {
    return request({
        url: serviceAreaNameFormAccept + 'GetDetails',
        method: 'get',
        params
    })
}

//项目验收审批
export function acceptApproval(data) {
    return request({
        url: serviceAreaNameFormAccept + 'ApprovalAsync',
        method: 'post',
        data
    })
}

//项目看板基本信息
export function getBoardInfoAsync(params) {
    return request({
        url: serviceAreaName + 'GetBoardInfoAsync',
        method: 'get',
        params
    })
}

export function getProjectEmployeeList(data) {
    return request({
        url: serviceAreaName + 'GetProjectEmployeeList',
        method: 'post',
        data
    })
}

//  我参与的项目
export function GetMyProject(params) {
    return request({
        url: serviceAreaName + 'GetMyProject',
        method: 'get',
        params
    })
}


export function hasAuth(params) {
    return request({
        url: serviceAreaName + 'HasAuth',
        method: 'get',
        params
    })
}
