
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/Template/'

//新增
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}
//编辑
export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}
// //分页列表
// export function getList(data) {
//     return request({
//         url: serviceAreaName + 'GetListPage',
//         method: 'post',
//         data
//     })
// }
export function getAllList(params) {
    return request({
        url: serviceAreaName + 'GetAllList',
        method: 'get',
        params
    })
}

//删除
export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function editStatus(params) {
    return request({
        url: serviceAreaName + 'EditStatus',
        method: 'get',
        params
    })
}