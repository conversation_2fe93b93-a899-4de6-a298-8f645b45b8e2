import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getList(data) {
  return request({
    url: serviceAreaName + '/Flow/FindFlowInstanceByPage',
    method: 'post',
    data
  })
}

export function get(params) {
  return request({
    url: serviceAreaName + '/Flow/FindFlowInstanceById',
    method: 'get',
    params
  })
}

export function verify(data) {
  return request({
    url: serviceAreaName + '/Flow/NodeAudit',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: serviceAreaName + '/Flow/CreateFlowInstance',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: serviceAreaName + '/Flow/DeleteFlowInstanceByIds',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: serviceAreaName + '/flowinstances/update',
    method: 'post',
    data
  })
}


//转审
export function assign(data) {
  return request({
      url: serviceAreaName + '/Flow/Assign',
      method: 'post',
      data
  })
}

//驳回、同意
export function audit(data) {
  return request({
    url: serviceAreaName + '/Flow/NodeAudit',
    method: 'post',
    data
  })
}

//撤回
export function revoke(data){
  return request({
    url: serviceAreaName + '/Flow/RevokeFlowInstance?instanceId=' + data.instanceId,//bug post 提交还跟后面
    method: 'post',
    data
  })
}
