import request from '@/utils/request'

import { serviceArea } from '../serviceArea'

let busServiceAreaName = serviceArea.business + '/MaterialTransferApplication/'

let busVerificationServiceAreaName = serviceArea.business + '/MaterialCancelVerification/'

let busMaterialReturnServiceAreaName  = serviceArea.business + '/MaterialReturn/'

let busMaterialPersonalStockServiceAreaName  = serviceArea.business + '/MaterialPersonalStock/'


export function getList(data) {
    return request({
      url: busServiceAreaName + 'GetListPage',
      method: 'post',
      data
    })
  }


  export function materialDetails(params) {
    return request({
      url: busServiceAreaName + 'GetDetails',
      method: 'get',
      params
    })
  }



  export function addList(data) {
    return request({
      url: busServiceAreaName + 'AddList',
      method: 'post',
      data
    })
  }


  export function getListPageAndStructPart(data) {
    return request({
      url: busVerificationServiceAreaName + 'GetListPageAndStructPart',
      method: 'post',
      data
    })
  }


  
  export function writeOff(data) {
    return request({
      url: busVerificationServiceAreaName + 'Add',
      method: 'post',
      data
    })
  }


  export function getDetailsByStructPartId(params) {
    return request({
      url: busVerificationServiceAreaName + 'GetDetailsByStructPartId',
      method: 'get',
      params
    })
  }

  
  export function getListPageByMaintenance(data) {
    return request({
      url: busVerificationServiceAreaName + 'ListPageByMaintenance',
      method: 'post',
      data
    })
  }


  export function getMaterialReturnList(data) {
    return request({
      url: busMaterialReturnServiceAreaName + 'GetListPage',
      method: 'post',
      data
    })
  }

  export function addReturnMaterial(data) {
    return request({
      url: busMaterialReturnServiceAreaName + 'Add',
      method: 'post',
      data
    })
  }


  export function testing(data) {
    return request({
      url: busMaterialReturnServiceAreaName + 'Detection',
      method: 'post',
      data
    })
  }

  export function returnMaterialDetails(params) {
    return request({
      url: busMaterialReturnServiceAreaName + 'GetDetails',
      method: 'get',
      params
    })
  }


  export function cancellingStock(data) {
    return request({
      url: busMaterialReturnServiceAreaName + 'CancellingStock',
      method: 'post',
      data
    })
  }


  export function editPersonalMaterial(data) {
    return request({
      url: busMaterialPersonalStockServiceAreaName + 'Edit',
      method: 'post',
      data
    })
  }

  export function getMaterialPersonalStockList(data) {
    return request({
      url: busMaterialPersonalStockServiceAreaName + 'GetListPage',
      method: 'post',
      data
    })
  }

  export function addPersonalList(data) {
    return request({
      url: busMaterialPersonalStockServiceAreaName + 'AddList',
      method: 'post',
      data
    })
  }

  export function getPersonalDetails(params) {
    return request({
        url: busMaterialPersonalStockServiceAreaName + 'GetDetails',
        method: 'get',
        params
    })
  }


