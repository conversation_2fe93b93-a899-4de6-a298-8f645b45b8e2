import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

export function getList(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/GetMongoDynamicFormList',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/AddMongoDynamicForm',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/DeleteMongoDynamicForm',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/EditMongoDynamicForm',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + '/DynamicForm/GetMongoDynamicFormDetails',
        method: 'get',
        params
    })
}


//操作表单数据
export function getDatas(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/GetMongoDynamicFormData',
        method: 'post',
        data
    })    
}

export function addData(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/AddMongoDynamicFormData',
        method: 'post',
        data
    })
}

export function delData(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/DeleteMongoDynamicFormData',
        method: 'post',
        data
    })
}

export function editData(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/EditMongoDynamicFormData',
        method: 'post',
        data
    })    
}

export function getDataDetail(data) {
    return request({
        url: serviceAreaName + '/DynamicForm/GetMongoDynamicFormDataDetails',
        method: 'post',
        data
    })
}
