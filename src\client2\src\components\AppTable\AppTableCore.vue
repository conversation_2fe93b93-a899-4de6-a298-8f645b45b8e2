<template>
  <div>
    <el-table
      v-if="tabNewLifeFlag"
      class="app-table"
      ref="appTable"
      fit
      :data="tabDatas"
      highlight-current-row
      @select-all="handleSelectAll"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
      v-bind="$attrs"
      v-on="$listeners"
      v-loading="loading"
      @sort-change="handleSortChange"
      :row-class-name="getRowClsssName"
      :header-row-style="{ height: '40px' }"
      :header-cell-style="{ position: 'relative' }"
      :header-cell-class-name="headerCellClassName"
      @header-dragend="handleHeaderDragend"
    >
      <div style="padding-top: 20px; padding-bottom: 20px;" slot="empty">
        <img style="width: 72px;" src="../../assets/images/no-data.png" alt />
        <div class="placeholder-text-color">
          暂无数据
          <slot name="empty-data"></slot>
        </div>
      </div>

      <!-- 多选列 -->
      <el-table-column
        type="selection"
        width="42"
        align="center"
        :fixed="
          columns.find(s => s.attr.fixed == 'left' || s.attr.fixed === true) ? 'left' : firstFixed
        "
        :selectable="checkSelectable"
        v-if="multable"
      ></el-table-column>

      <!-- 如果有自定义序号 index -->
      <template v-if="customeIndex">
        <el-table-column
          type="index"
          :fixed="
            columns.find(s => s.attr.fixed == 'left' || s.attr.fixed === true) ? 'left' : firstFixed
          "
          :align="indexAlign"
          :label="indexHeaderText"
          v-if="serial"
          :min-width="indexColMinWidth > 0 ? indexColMinWidth : undefined"
        >
          <template slot-scope="scope">
            <slot name="myCustomeIndex" :row="scope.row" :index="scope.$index + 1"></slot>
          </template>
        </el-table-column>
      </template>
      <template v-else>
        <el-table-column
          type="index"
          :index="indexMethod"
          :fixed="
            columns.find(s => s.attr.fixed == 'left' || s.attr.fixed === true) ? 'left' : firstFixed
          "
          :align="indexAlign"
          :label="indexHeaderText"
          v-if="serial"
          :min-width="indexColMinWidth > 0 ? indexColMinWidth : undefined"
        ></el-table-column>
      </template>

      <!-- 用户自定义需要显示的列 -->
      <!-- <el-table-column v-for="c in columns"
                    :key="c.attr.prop"
                    v-bind="c.attr"
                    >
                    <template slot-scope="scope">
                        <slot v-if="c.slot" :name="c.attr.prop" :row='scope.row'></slot>
                        <span v-else>{{ c.attr.formatter ? c.attr.formatter(scope.row, c) : scope.row[c.attr.prop] }}</span>
                    </template>
      </el-table-column>-->

      <!-- 用户自定义需要显示的列 -->
      <template v-for="c in columns">
        <!-- 如果对应的列需要用到slot，则为对应的列生成具名插槽（作用域插槽） -->
        <el-table-column v-bind="c.attr">
          <template slot="header" slot-scope="scope">
            <!-- 不建议使用该属性（customHeader），可以通过设置 renderHeader 来代替 -->
            <span v-if="c.customHeader">
              <slot :name="'header_' + c.attr.prop" :column="scope"></slot>
            </span>
            <span v-else>{{ c.attr.label }}</span>

            <el-popover
              placement="bottom"
              title=""
              trigger="click"
              popper-class="filter-popper-wrapper"
              :ref="`filter_${c.attr.prop}_ref`"
              v-if="c.isFilterColumn"
            >
              <div>
                <div class="content">
                  <slot :name="`filter_${c.attr.prop}`"></slot>
                </div>
                <div class="footer" v-if="isShowFilterFooter">
                  <app-cus-button
                    type="default"
                    text="重置"
                    @click="handleReset(c.attr.prop)"
                    style="margin-right: 4px;"
                  ></app-cus-button>
                  <app-cus-button
                    type="primary"
                    text="筛选"
                    @click="handleSearch(c.attr.prop)"
                  ></app-cus-button>

                  <!-- <el-link class="btn pointer" :underline="false" style="margin-right: 10px;" @click="handleSearch(c.attr.prop)">筛选</el-link>
                  <el-link class="btn pointer" :underline="false" @click="handleReset(c.attr.prop)">重置</el-link> -->
                </div>
              </div>
              <i slot="reference" style="margin-left: 4px;" class="el-icon-arrow-down pointer"></i>
            </el-popover>

            <!-- 表格列，更多操作按钮，需要考虑header-cell左右边距 10px -->
            <span v-if="c.attr.isShowMoreOpt" :class="headerClassMoreBtn">
              <!-- 如果设置了isShowMoreOpt，并且有回调函数 -->
              <app-table-column-more-opt
                :isShowLeft="_isShowLeft(c.attr.prop)"
                :isShowRight="_isShowRight(c.attr.prop)"
                :isShowHide="c.attr && c.attr.disabledClose !== true"
                :isFixed="!!c.attr.fixed"
                :cusSortable="c.attr.cusSortable"
                :sortType="c.attr.ascending ? 'ascending' : c.attr.descending ? 'descending' : ''"
                v-if="c.attr.callBackColOptBtn"
                @colOptBtnClick="optCmd => c.attr.callBackColOptBtn(c.attr.prop, optCmd)"
              ></app-table-column-more-opt>
              <app-table-column-more-opt
                v-else
                @colOptBtnClick="optCmd => {}"
              ></app-table-column-more-opt>
            </span>
          </template>
          <template slot-scope="scope">
            <slot
              v-if="c.slot"
              :name="c.attr.prop"
              :row="scope.row"
              :index="scope.$index + 1"
            ></slot>
            <template v-else>{{ scope.row[c.attr.prop] }}</template>
          </template>
        </el-table-column>
      </template>

      <template slot="append" v-if="showAppend">
        <slot name="append"></slot>
      </template>

      <!-- 操作列 在右侧最后一列显示 -->
      <el-table-column
        :fit="true"
        :label="optHeaderText"
        align="left"
        v-if="isShowOpatColumn"
        :width="optColWidth > 0 ? optColWidth : undefined"
        :min-width="optColMinWidth > 0 ? optColMinWidth : undefined"
      >
        <template slot-scope="scope">
          <!-- 将作用域插槽返回的对象scope继续通过作用域插槽暴露出去 -->
          <!-- 注意：为了有意义，所以索引从1开始 -->
          <slot :row="scope.row" :index="scope.$index + 1"></slot>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { recursive } from "@/utils";
import mixins from "./mixins";
import {
  headerClassMoreBtn
} from "@/utils/customTab";

export default {
  name: "app-table-core",
  mixins: [mixins],
  components: {},
  props: {
    rowClassName: {
      type: String,
      default: "",
    },
    stripe: {
      type: Boolean,
      default: true,
    },
    tabDatas: Array,
    multable: {
      //是否多选
      type: Boolean,
      default: true,
    },
    //是否显示编号
    serial: {
      type: Boolean,
      default: true,
    },
    firstFixed: {
      type: Boolean,
      default: false,
    },
    // 按钮操作列 fixed 是否到左侧第一列  后的对齐方式
    btnFirstAlign: {
      type: String,
      default: "center",
    },

    //是否显示操作列
    isShowOpatColumn: {
      //如果非系统模块，则根据该集合来显示过滤显示的列（存在该集合中的列才显示）
      type: Boolean,
      default: true,
    },
    //行编号其实值（和分页相关——页索引 * 每页条数 + 当前索引）
    startOfTable: {
      type: Number,
      default: 0,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    disabledList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    /**勾选主键名称 */
    selectKeyName: {
      type: String,
      default: "Id",
    },
    showAppend: {
      type: Boolean,
      default: false,
    },

    /**
     * 主要用于列按比例划分宽度（一般情况下，indexColMinWidth 搭配 optColMinWidth、optColWidth 器中一个使用）
     * 划分规则：所有列都必须设置 min-width；假设有10列，所有列都设置为1，就表示等分列宽度；
     */
    indexColMinWidth: {
      type: Number,
      default: -1,
    },
    optColMinWidth: {
      type: Number,
      default: -1,
    },
    optColWidth: {
      type: Number,
      default: -1,
    },
    //是否自定义序号index
    customeIndex: {
      type: Boolean,
      default: false,
    },
    indexAlign: {
      type: String,
      default: "left",
    },
    indexHeaderText: {
      type: String,
      default: "序号",
    },
    optHeaderText: {
      type: String,
      default: "操作",
    },
    isShowFilterFooter: {
      type: Boolean,
      default: true,
    },
  },

  // watch: {
  //   tabAuthColumns(_authColumns) {
  //     this.authColumns = _authColumns;
  //     this._initColumns();
  //   }
  // },
  // computed: {
  //   //表格需要显示的列
  //   columns() {
  //     let res = this.headers.filter(h => h.isShow);
  //     if (!this.isShowAllColumn) {
  //       res = res.filter(h => this.authColumns.some(o => o == h.attr.prop));
  //     }
  //     return res;
  //   }
  //   //表格隐藏列总数
  //   // hiddenColumnTotal() {
  //   //     let res = this.headers.filter(h => !h.isShow);
  //   //     if(!this.isShowAllColumn){
  //   //         res = res.filter(h => this.authColumns.some(o => o == h.attr.prop))
  //   //     }
  //   //     return res.length
  //   // },
  // },
  // created() {
  //   // this.headers = JSON.parse(JSON.stringify(this.tabColumns));
  //   this.authColumns = this.tabAuthColumns;
  //   this._initColumns();
  // },
  mounted() {
    this.__tabResizeHandler = _.debounce(({ vm, val }) => {
      this.doLayout();
    }, 150);

    window.addEventListener("resize", this.__tabResizeHandler);
  },
  data() {
    return {
      headerClassMoreBtn,
      isSelectAll: false,
      search: "",
      __tabResizeHandler: null,
    };
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.__tabResizeHandler);
  },
  methods: {

    
    getRowClsssName({ row, rowIndex }) {
      let result = this.rowClassName;

      if (this.stripe && rowIndex % 2 != 0) {
        result += `${result ? " " : ""}cus-row-striped`;
      }
      return result;
    },
    //单击行
    handleRowClick(row) {
      // if(!this.multable){ //单选
      //     this.$refs.appTable.clearSelection();
      // }
      // this.$refs.appTable.clearSelection();
      //应需求人员要求,取消表格中 单击行时自动选中复选框 特性
      //2020-05-06 15:14:55 lsf
      //this.$refs.appTable.toggleRowSelection(row);
    },
    //选择项改变时候（返回当前选中的行）
    handleSelectAll(selection) {
      this.isSelectAll = !this.isSelectAll;
      recursive(this.tabDatas, item => {
        this.$refs.appTable.toggleRowSelection(item, this.isSelectAll);
      });
    },
    // 勾选行或者取消勾选行
    selectRow(list,isSelect=true) {
      this.$nextTick(()=>{
        const selectIdList = list.map(item => item[this.selectKeyName]);
        const selectTableDataList = this.tabDatas.filter(t=>selectIdList.includes(t[this.selectKeyName]));
        selectTableDataList.forEach(item => {
          this.$refs.appTable.toggleRowSelection(item,isSelect);
        });
      })
    },
    handleSelectionChange(selection) {
      this.$emit("rowSelectionChanged", selection);
    },
    //排序
    handleSortChange({ column, prop, order }) {
      this.$emit("sortChagned", { column, prop, order });
    },
    // _initColumns() {
    //   let res = this.tabColumns;
    //   if (!this.isShowAllColumn) {
    //     res = res.filter(h => this.authColumns.some(o => o == h.attr.prop));
    //   }
    //   const currUrl = this.$route.path;
    //   // const hideColumns = getHideColumnsOfPage(currUrl) || []
    //   this.headers = res.map(c => {
    //     if (c.isShow === undefined) {
    //       // if(hideColumns.findIndex(cc => cc == c.attr.prop) == -1){
    //       //     this.$set(c, 'isShow', true)
    //       // }else{
    //       //     this.$set(c, 'isShow', false)
    //       // }
    //       this.$set(c, "isShow", true);
    //     }
    //     return c;
    //   });
    // },
    // handleChange(head) {
    //     let hides = this.headers.filter(c => !c.isShow) || []
    //     const currUrl = this.$route.path
    //     setHideColumnsOfPage(currUrl, hides.map(c => c.attr.prop))
    // },
    indexMethod(index) {
      return (index += 1) + this.startOfTable;
    },
    showOrHideColumn(columnName, isShow) {
      let header = this.headers.find(s => s.attr.prop == columnName);
      if (header) {
        header.isShow = isShow;
      }
    },
    checkSelectable(row) {
      if (this.disabledList && this.disabledList.length > 0) {
        return this.disabledList.findIndex(s => s == row[this.selectKeyName]) == -1;
      }
      return true;
    },
    doLayout() {
      //处理固定（fixed）列错位的问题
      this.$refs.appTable.doLayout();
    },
    handleSearch(prop) {
      this.$emit("handleSearch", prop);
      this.doClose(prop);
    },
    handleReset(prop) {
      this.$emit("handleReset", prop);
      this.doClose(prop);
    },
    doClose(prop) {
      let refObj = this.$refs[`filter_${prop}_ref`];
      refObj && refObj.length > 0 && refObj[0].doClose();
      this.$nextTick(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/.el-table__empty-text {
  line-height: 20px !important;
}
</style>
