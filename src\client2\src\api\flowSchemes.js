import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business
export function getListByPage(data) {
  return request({
    url: serviceAreaName + '/Flow/FindFlowSchemeByPage',
    method: 'post',
    data
  })
}

export function getList(params) {
  return request({
    url: serviceAreaName + '/Flow/FindFlowSchemes',
    method: 'get',
    params
  })
}


export function get(params) {
  return request({
    url: serviceAreaName + '/Flow/FindFlowSchemeById',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: serviceAreaName + '/Flow/CreateFlowScheme',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: serviceAreaName + '/Flow/EditFlowScheme',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: serviceAreaName + '/Flow/DeleteFlowSchemeByIds',
    method: 'post',
    data
  })
}

