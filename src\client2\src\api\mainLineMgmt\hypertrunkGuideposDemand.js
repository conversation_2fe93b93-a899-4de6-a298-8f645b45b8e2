import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/HypertrunkGuideposDemand/'
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function solicitSolutions(data) {
    return request({
        url: serviceAreaName + 'SolicitSolutions',
        method: 'post',
        data
    })
}


export function vote(data) {
    return request({
        url: serviceAreaName + 'Vote',
        method: 'post',
        data
    })
}


export function resolve(data) {
    return request({
        url: serviceAreaName + 'Resolve',
        method: 'post',
        data
    })
}

