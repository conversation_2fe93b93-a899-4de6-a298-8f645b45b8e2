import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/SystemEmployee/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetRecordListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'EditEmployeeRecord',
        method: 'post',
        data
    })
}

export function edit2(data) {
    return request({
        url: serviceAreaName + 'edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function setOrUnsetSelfMaintenance(data) {
    return request({
        url: serviceAreaName + 'setOrUnsetSelfMaintenance',
        method: 'post',
        data
    })
}

export function modifyLaborContractSubject(data) {
    return request({
        url: serviceAreaName + 'ModifyLaborContractSubject',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}


export function resetPassword(params) {
    return request({
        url: serviceAreaName + 'ResetPassword',
        method: 'get',
        params
    })
}


export function setPrincipal(data) {
    return request({
        url: serviceAreaName + 'SetPrincipal',
        method: 'post',
        data
    })
}


export function adjustDepartment(data) {
    return request({
        url: serviceAreaName + 'AdjustDepartment',
        method: 'post',
        data
    })
}


export function importEmployee(data) {
    return request({
        url: serviceAreaName + 'ImportEmployee',
        method: 'post',
        data
    })
}


export function startExportExcel(data) {
    return request({
        url: serviceAreaName + 'StartExportExcel',
        method: 'post',
        data
    })
}

export function getAllEmployees(data) {
    return request({
        url: serviceAreaName + 'GetAllEmployees',
        method: 'post',
        data
    })
}

export function cardDetail(params) {
    return request({
        url: serviceAreaName + 'GetCardDetails',
        method: 'get',
        params
    })
}

export function editCard(data) {
    return request({
        url: serviceAreaName + 'EditCard',
        method: 'post',
        data
    })
}

export function getDetaillist(params) {
    return request({
        url: serviceAreaName + 'GetCardDetailsList',
        method: 'get',
        params
    })
}
export function createQRCode(params) {
    return request({
        url: serviceAreaName + 'CreateQRCode',
        method: 'get',
        params
    })
}
export function editCardBus(data) {
    return request({
        url: serviceAreaName + 'EditCardBus',
        method: 'post',
        data
    })
}
// ********************************员工档案接口*********************************//

export function editEmployeeRecord(data) {
    return request({
        url: serviceAreaName + 'EditEmployeeRecord',
        method: 'post',
        data
    })
}

export function getEmployeeRecordDetails(params) {
    return request({
        url: serviceAreaName + 'GetEmployeeRecordDetails',
        method: 'get',
        params
    })
}


export function editAvatar(params) {
    return request({
        url: serviceAreaName + 'EditAvatar',
        method: 'get',
        params
    })
}

export function getListByType(params) {
    return request({
        url: serviceAreaName + 'GetListByType',
        method: 'get',
        params
    })
}

export function getListByLaborContractState(params) {
    return request({
        url: serviceAreaName + 'GetListByLaborContractState',
        method: 'get',
        params
    })
}


//根据部门id集合返回所有部门人员列表
export function systemEmployee(data) {
    return request({
        url: serviceAreaName + 'GetListByDepartmentId',
        method: 'post',
        data
    })
}

export function checkPassword(params) {
    return request({
        url: serviceAreaName + 'CheckPassword',
        method: 'get',
        params
    })
}

export function getOverviewPage(data) {
    return request({
        url: serviceAreaName + 'GetOverviewPage',
        method: 'post',
        data
    })
}

export function getOverviewDetails(params) {
    return request({
        url: serviceAreaName + 'GetOverviewDetails',
        method: 'get',
        params
    })
}
