import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/Order/'


export function getOrderTotalAmount(data) {
    return request({
        url: serviceAreaName + 'GetOrderTotalAmountContract',
        method: 'post',
        data
    })
}


export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function getClientUnitsList(params) {
    return request({
        url: serviceAreaName + 'GetClientUnitsList',
        method: 'get',
        params
    })
}


export function createApproval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function getOrdersHistory(params) {
    return request({
        url: serviceAreaName + 'GetOrdersHistory',
        method: 'get',
        params
    })
}

export function getEmployees(params) {
    return request({
        url: serviceAreaName + 'GetEmployee',
        method: 'get',
        params
    })
}

export function getListByType(data) {
    return request({
        url: serviceAreaName + 'GetListByType',
        method: 'post',
        data
    })
}

export function importFile(data) {
    return request({
        url: serviceAreaName + 'Import',
        method: 'post',
        data
    })
}

export function saveDrafts(data) {
    return request({
        url: serviceAreaName + 'SaveDrafts',
        method: 'post',
        data
    })
}

export function GetOrdersHistoryDetails(params) {
    return request({
        url: serviceAreaName + 'GetOrdersHistoryDetails',
        method: 'get',
        params
    })
}

export function saveFollowUpNotes(data) {
    return request({
        url: serviceAreaName + 'SaveFollowUpNotes',
        method: 'post',
        data
    })
}

export function getDataStatistics(data) {
    return request({
        url: serviceAreaName + 'DataStatistics',
        method: 'post',
        data
    })
}

export function getPortrait(data) {
    return request({
        url: serviceAreaName + 'GetPortrait',
        method: 'post',
        data
    })
}


