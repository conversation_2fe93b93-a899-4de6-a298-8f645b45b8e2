<template>
  <div :class="className" :style="{height:height, width:width}" />
</template>

<script>
import echarts from 'echarts'
import "echarts-wordcloud/dist/echarts-wordcloud";
import "echarts-wordcloud/dist/echarts-wordcloud.min";
export default {
  props: {
    xAxisLabelTooltip: {
      type: Boolean,
      default: false
    },
    xAxisLabelLimit: {
      type: Number,
      default: 8
    },
    rotate: {
      type: Number,
      default: 30
    },
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    option: {
      type: Object,
      default: () => {
        return null
      }
    },
    isShowAxisLabel: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    option: {
      handler(val) {
        if(val) {
          this.initChart()
        }
      },
      deep: true,
      // immediate: true
    },
  },
  data() {
    return {
      __resizeHandler: null,
      chart: null,
      
    }
  },
  mounted() {
    this.initChart()
    this.__resizeHandler = _.debounce(() => {
      if (this.chart) {
        this.chart.resize()
      }
    }, 150)
    window.addEventListener('resize', this.__resizeHandler)
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    window.removeEventListener('resize', this.__resizeHandler)
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    showLoading() {
      if(this.chart) {
        this.chart.showLoading({
            text: '加载中...',
            textStyle: { fontSize : 30 , color: '#444' },
            effectOption: {backgroundColor: 'rgba(0, 0, 0, 0)'}
        });
      }
    },
    hideLoading() {
      if(this.chart) {
        this.chart.hideLoading();
      }
    },
    initChart() {
      this.chart = echarts.init(this.$el)

      let tempOption = {
        tooltip: {
          transitionDuration: 0, //防止（首次显示 tooltip 内容时导致）页面抖动
          confine: true,
          trigger: 'item',
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          // left: 'center',
          // bottom: '10',
          // data: []
          // selected: {
          //   '姓名': false
          // },
        },
        calculable: true,
        series: [
        
        ],
        color: [],
        
      }
      
      let targetOption = Object.assign({}, tempOption)
      let option = JSON.parse(JSON.stringify(this.option))
      if(option) {
        if(this.xAxisLabelTooltip) {
          let xAxis = option.xAxis
          if(xAxis) {
            xAxis.triggerEvent = this.xAxisLabelTooltip || false
            if(xAxis.triggerEvent) {
              let limit = this.xAxisLabelLimit

              if(this.isShowAxisLabel) {
                xAxis.axisLabel = {
                  rotate: this.rotate,
                  color: 'black',
                  interval: 0,
                  // color: 'red', // y轴字体颜色
                  formatter: function(value) { // y轴自定义数据
                    let str = value
                    if(value && value.length > limit) {
                        str = value.substr(0, limit) + '...'
                    }
                    return str
                  }
                }
              }
            }
          }
        }
        targetOption = _.merge(targetOption, option)
      }

      if(this.xAxisLabelTooltip) {
        this.extension(this.chart)
      }

      this.chart.setOption(targetOption)
    },

    //鼠标移动到y轴坐标，显示 label
    extension(chart) {
        // 注意这里，是以X轴显示内容过长为例，如果是y轴的话，需要把params.componentType == 'xAxis'改为yAxis
        // 判断是否创建过div框,如果创建过就不再创建了
        // 该div用来盛放文本显示内容的，方便对其悬浮位置进行处理
        var elementDiv = document.getElementById('extension')
        if (!elementDiv) {
            var div = document.createElement('div')
            div.setAttribute('id', 'extension')
            div.style.display = 'block'
            document.querySelector('html').appendChild(div)
        }
        chart.on('mouseover', function (params) {
            if (params.componentType == 'xAxis') {
                var elementDiv = document.querySelector('#extension')
                //设置悬浮文本的位置以及样式
                var elementStyle = 'position: absolute;z-index: 99999; color: #fff; font-size: 12px;padding: 5px;display: inline;border-radius: 4px;background-color: #303133;box-shadow: rgba(0, 0, 0, 0.3) 2px 2px 8px'
                    elementDiv.style.cssText = elementStyle
                    elementDiv.innerHTML = params.value
                    //没有全屏的话，直接使用document.querySelector('html')、event.pageX 即可
                    document.querySelector('html').onmousemove = function (event) {
                    var elementDiv = document.querySelector('#extension')
                    var xx = event.pageX + 15
                    // var xx = event.layerX + 15
                    var yy = event.pageY + 15
                    // var yy = event.layerY + 15
                    elementDiv.style.top = yy + 'px'
                    elementDiv.style.left = xx + 'px'
                }
            }
        })
        chart.on('mouseout', function (params) {
            //注意这里，我是以X轴显示内容过长为例，如果是y轴的话，需要改为yAxis
            if (params.componentType == 'xAxis') {
                var elementDiv = document.querySelector('#extension')
                elementDiv.style.cssText = 'display:none'
            }
        })
    },
  }
}
</script>
