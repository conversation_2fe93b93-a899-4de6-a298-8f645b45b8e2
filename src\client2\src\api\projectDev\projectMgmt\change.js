import request from '@/utils/request'
import { serviceArea } from '../../serviceArea'

let serviceAreaName = serviceArea.business + '/Change/'


export function addDemandChange(data) {
    return request({
        url: serviceAreaName + 'AddDemandChange',
        method: 'post',
        data
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}



export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function getSummary(params) {
    return request({
        url: serviceAreaName + 'GetChangeStatusCount',
        method: 'get',
        params
    })
}


export function addMilepostChange(data) {
    return request({
        url: serviceAreaName + 'AddMilepostChange',
        method: 'post',
        data
    })
}

export function addTeamMemberChange(data) {
    return request({
        url: serviceAreaName + 'AddTeamMemberChange',
        method: 'post',
        data
    })
}

export function createApproval(data) {
    return request({
        url: serviceAreaName + 'Approval',
        method: 'post',
        data
    })
}

export function getListByCondition(data) {
    return request({
        url: serviceAreaName + 'GetListByCondition',
        method: 'post',
        data
    })
}

export function addOrderChange(data) {
    return request({
        url: serviceAreaName + 'AddOrderChange',
        method: 'post',
        data
    })
}

export function addContractChange(data) {
    return request({
        url: serviceAreaName + 'AddContractChange',
        method: 'post',
        data
    })
}

export function addBidChange(data) {
    return request({
        url: serviceAreaName + 'AddBidChange',
        method: 'post',
        data
    })
}