import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + '/EquipmentUse/'


//新增设备
export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}
export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}
//删除
export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

//获取设备列表
export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function detail(data) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'post',
        data
    })
}

