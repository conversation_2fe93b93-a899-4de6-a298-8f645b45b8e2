
import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.user + '/TimecardGroup/'

let serviceAreaName2 = serviceArea.business + '/TimecardHumanReview/'

export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

export function add(data) {
    return request({
        url: serviceAreaName + 'Add',
        method: 'post',
        data
    })
}

export function edit(data) {
    return request({
        url: serviceAreaName + 'Edit',
        method: 'post',
        data
    })
}

export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDetails',
        method: 'get',
        params
    })
}

export function del(data) {
    return request({
        url: serviceAreaName + 'Delete',
        method: 'post',
        data
    })
}

// export function getListByCondition(data) {
//     return request({
//         url: serviceAreaName + 'GetListByCondition',
//         method: 'post',
//         data
//     })
// }

export function getDetail(params) {
    return request({
        url: serviceAreaName2 + 'GetDetails',
        method: 'get',
        params
    })
}


export function approval(data) {
    return request({
        url: serviceAreaName2 + 'Approval',
        method: 'post',
        data
    })
}