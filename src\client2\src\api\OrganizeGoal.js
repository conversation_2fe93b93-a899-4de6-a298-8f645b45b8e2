import request from "@/utils/request";
import { serviceArea } from "./serviceArea";

let serviceAreaName = serviceArea.business + "/OrganizeGoal/";
export function getList(data) {
  return request({
    url: serviceAreaName + "GetListPage",
    method: "post",
    data,
  });
}
//新增
export function add(data) {
  return request({
    url: serviceAreaName + "Add",
    method: "post",
    data,
  });
}

//编辑
export function edit(data) {
  return request({
    url: serviceAreaName + "Edit",
    method: "post",
    data,
  });
}

//删除
export function del(data) {
  return request({
    url: serviceAreaName + "Delete",
    method: "post",
    data,
  });
}
export function detail(params) {
  return request({
    url: serviceAreaName + "GetDetails",
    method: "get",
    params,
  });
}
export function editHypertrunkMilestone(data) {
  return request({
    url: serviceAreaName + "EditHypertrunkMilestone",
    method: "post",
    data,
  });
}

export function getMapDetails(params) {
  return request({
    url: serviceAreaName + "GetMapDetails",
    method: "get",
    params,
  });
}

export function setMapDetails(data) {
  return request({
    url: serviceAreaName + "SetMapDetails",
    method: "post",
    data,
  });
}

export function deleteVueTest(params) {
  return request({
    url: serviceAreaName + "DeleteVueTest",
    method: "get",
    params,
  });
}
