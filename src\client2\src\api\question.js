import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business + '/ProjectManagementDefect/'
    //获取首页问题列表
export function getList(data) {
    return request({
        url: serviceAreaName + 'GetListPage',
        method: 'post',
        data
    })
}

//新增编辑问题
export function edit(data) {
    return request({
        url: serviceAreaName + 'AddOrUpdateDefect',
        method: 'post',
        data
    })
}

//获取问题详情
export function detail(params) {
    return request({
        url: serviceAreaName + 'GetDefectDetailById',
        method: 'get',
        params
    })
}

//删除问题
export function del(data) {
    return request({
        url: serviceAreaName + 'DeleteDefect',
        method: 'post',
        data
    })
}

//解决问题
export function resolve(data) {
    return request({
        url: serviceAreaName + 'AddSolution',
        method: 'post',
        data
    })
}

//查看问题解决详情
export function reviewSolution(params) {
    return request({
        url: serviceAreaName + 'GetSolutionDetailByDefectId',
        method: 'get',
        params
    })
}

//跟新缺陷状态
export function updateDefectStatus(params) {
    return request({
        url: serviceAreaName + 'UpdateDefectStatusById',
        method: 'get',
        params
    })
}

//重新分配缺陷
export function reallocate(data) {
    return request({
        url: serviceAreaName + 'ReallocateDefectById',
        method: 'post',
        data
    })
}

export function exportList(data) {
    return request({
        url: serviceAreaName + 'Export',
        method: 'post',
        data
    })
}