import request from '@/utils/request'
import { serviceArea } from '../serviceArea'

let serviceAreaName = serviceArea.business + '/MeetProFeedback/'


export function getFeedbackCondition(params) {
    return request({
        url: serviceAreaName + 'GetFeedbackCondition',
        method: 'get',
        params
    })
}
// 新增议题反馈
export function addFeedbackApi(data) {
    return request({
        url: serviceAreaName + 'AddFeedback',
        method: 'post',
        data
    })
}
// 获取议题反馈
export function getFeedbackListApi(params) {
    return request({
        url: serviceAreaName + 'GetList',
        method: 'get',
        params
    })
}
// 议题反馈添加评论
export function addCommentApi(data) {
    return request({
        url: serviceAreaName + 'AddComment',
        method: 'post',
        data
    })
}
// 议题反馈撤回评论
export function meetProFeedbackApi(params) {
    return request({
        url: serviceAreaName + 'Recall',
        method: 'get',
        params
    })
}
// 议题反馈点赞评论
export function setGiveLikeApi(params) {
    return request({
        url: serviceAreaName + 'SetGiveLike',
        method: 'get',
        params
    })
}

export function remindNotFeedback(params) {
    return request({
        url: serviceAreaName + 'RemindNotFeedback',
        method: 'get',
        params
    })
}

