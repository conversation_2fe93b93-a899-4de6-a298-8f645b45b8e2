import { isShowLeft, isShowRight } from "@/utils/customTab";
import customTabMixins from "@/components/AppTable/customTabMixins";

export default {
  mixins: [customTabMixins],
  data() {
    return {
      headers: [],
      // authColumns: [],
      //方式tab诡异抖动，通过v-if解决该问题
      tabNewLifeFlag: true,
    };
  },
  props: {
    /**
     * 控制 tabNewLifeFlag 是否起作用
     */
    isForceFlush: {
      type: Boolean,
      default: true
    },

    // isShowAllColumn: {
    //     //是否显示所有列，true：显示所有列（如果是系统模块，显示所有列）
    //     type: Boolean,
    //     default: false
    // },
    // tabAuthColumns: {
    //     type: Array,
    //     default: () => {
    //       return [];
    //     }
    // },
    tabColumns: {
      type: Array,
      required: true,
      // validator: cols => {
      //   return cols.length >= 1; //表格至少需要1列
      // }
    },
  },
  watch: {
    // tabAuthColumns(_authColumns) {
    //     this.authColumns = _authColumns;
    //     this._initColumns();
    // }
    tabColumns: {
      handler(val) {
        this._initColumns();
      },
      deep: true,
    },
  },
  computed: {
    //表格需要显示的列
    columns() {
      let res = this.headers && this.headers.filter(h => h.isShow);
      //   if (!this.isShowAllColumn) {
      //     res = res && res.filter(h => this.authColumns.some(o => o == h.attr.prop));
      //   }
      return res;
    },
  },
  mounted() {},
  methods: {
    _initColumns() {
      let res = this.tabColumns;
      // if (!this.isShowAllColumn) {
      //   res = res && res.filter(h => this.authColumns.some(o => o == h.attr.prop));
      // }
      // const currUrl = this.$route.path;
      // const hideColumns = getHideColumnsOfPage(currUrl) || []
      this.headers =
        res &&
        res.map(c => {
          if (c.isShow === undefined) {
            // if(hideColumns.findIndex(cc => cc == c.attr.prop) == -1){
            //     this.$set(c, 'isShow', true)
            // }else{
            //     this.$set(c, 'isShow', false)
            // }
            this.$set(c, "isShow", true);
          }

          //如果没有特定设置为不可改变宽度，默认设置为true
          if (c.resizable !== false) {
            this.$set(c, "resizable", true);
          }

          if (c.attr.width === undefined) {
            this.$set(c.attr, "width", "");
          }

          return c;
        });


      if(this.isForceFlush) {
        this.tabNewLifeFlag = false;
      }

      this.$nextTick(() => {

        if(this.isForceFlush) {
          this.tabNewLifeFlag = true;
        }
        
        this.bindColumnDrop();
      });
    },

    _isShowLeft(colProp) {
      return isShowLeft(colProp, this.headers);
    },
    _isShowRight(colProp) {
      return isShowRight(colProp, this.headers);
    },
  },
  created() {
    // this.headers = JSON.parse(JSON.stringify(this.tabColumns));
    // this.authColumns = this.tabAuthColumns;
    this._initColumns();
  },
};
