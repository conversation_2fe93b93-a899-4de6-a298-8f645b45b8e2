import request from "@/utils/request";
import { serviceArea } from "./serviceArea";

let serviceAreaName = serviceArea.business + "/Finance";

export function getInterlinkApi(params) {
  return request({
    url: serviceAreaName + "/Interlink",
    method: "get",
    params,
  });
}
export function getBankAccountApi(params) {
  return request({
    url: serviceAreaName + "/GetBankAccount",
    method: "get",
    params,
  });
}
