/* Background */ .highlight-bg { color: #f8f8f2; background-color: #272822 }
/* PreWrapper */ .highlight-chroma { color: #f8f8f2; background-color: #272822; }
/* Error */ .highlight-chroma .highlight-err { color: #960050; background-color: #1e0010 }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .highlight-chroma .highlight-hl { background-color: #3c3d38 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* LineNumbers */ .highlight-chroma .highlight-ln { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #7f7f7f }
/* Line */ .highlight-chroma .highlight-line { display: flex; }
/* Keyword */ .highlight-chroma .highlight-k { color: #66d9ef }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #66d9ef }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #66d9ef }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #f92672 }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #66d9ef }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #66d9ef }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #66d9ef }
/* NameAttribute */ .highlight-chroma .highlight-na { color: #a6e22e }
/* NameClass */ .highlight-chroma .highlight-nc { color: #a6e22e }
/* NameConstant */ .highlight-chroma .highlight-no { color: #66d9ef }
/* NameDecorator */ .highlight-chroma .highlight-nd { color: #a6e22e }
/* NameException */ .highlight-chroma .highlight-ne { color: #a6e22e }
/* NameFunction */ .highlight-chroma .highlight-nf { color: #a6e22e }
/* NameOther */ .highlight-chroma .highlight-nx { color: #a6e22e }
/* NameTag */ .highlight-chroma .highlight-nt { color: #f92672 }
/* Literal */ .highlight-chroma .highlight-l { color: #ae81ff }
/* LiteralDate */ .highlight-chroma .highlight-ld { color: #e6db74 }
/* LiteralString */ .highlight-chroma .highlight-s { color: #e6db74 }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #e6db74 }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #e6db74 }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #e6db74 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #e6db74 }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #e6db74 }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #e6db74 }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #ae81ff }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #e6db74 }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #e6db74 }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #e6db74 }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #e6db74 }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #e6db74 }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #e6db74 }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #ae81ff }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #ae81ff }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #ae81ff }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #ae81ff }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #ae81ff }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #ae81ff }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #ae81ff }
/* Operator */ .highlight-chroma .highlight-o { color: #f92672 }
/* OperatorWord */ .highlight-chroma .highlight-ow { color: #f92672 }
/* Comment */ .highlight-chroma .highlight-c { color: #75715e }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #75715e }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #75715e }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #75715e }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #75715e }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #75715e }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #75715e }
/* GenericDeleted */ .highlight-chroma .highlight-gd { color: #f92672 }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericInserted */ .highlight-chroma .highlight-gi { color: #a6e22e }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #75715e }
/*
Monokai style - ported by Luigi Maselli - http://grigio.org
*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    background: #272822; color: #ddd;
}

.hljs-tag,
.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-strong,
.hljs-name {
    color: #f92672;
}

.hljs-code {
    color: #66d9ef;
}

.hljs-class .hljs-title {
    color: white;
}

.hljs-attribute,
.hljs-symbol,
.hljs-regexp,
.hljs-link {
    color: #bf79db;
}

.hljs-string,
.hljs-bullet,
.hljs-subst,
.hljs-title,
.hljs-section,
.hljs-emphasis,
.hljs-type,
.hljs-built_in,
.hljs-builtin-name,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
    color: #a6e22e;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion,
.hljs-meta {
    color: #75715e;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-doctag,
.hljs-title,
.hljs-section,
.hljs-type,
.hljs-selector-id {
    font-weight: bold;
}

