import request from "@/utils/request";
import { serviceArea } from "../serviceArea";

let serviceAreaName = serviceArea.business + "/ActionPool/";

export function getList(params) {
  return request({
    url: serviceAreaName + "GetActionPoolOperationList",
    method: "get",
    params,
  });
}

export function acceptActionPoolHistory(data) {
  return request({
    url: serviceAreaName + "AcceptActionPoolHistory",
    method: "post",
    data,
  });
}

export function del(data) {
  return request({
    url: serviceAreaName + "DeleteSingleActionPool",
    method: "post",
    data,
  });
}

export function abandonActionPool(params) {
  return request({
    url: serviceAreaName + "AbandonActionPool",
    method: "get",
    params,
  });
}


