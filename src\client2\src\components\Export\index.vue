<template >
  <div>
    <app-dialog
      title="导出"
      ref="appDialogRef"
      v-bind="$attrs"
      v-on="$listeners"
      :width='700'
      :maxHeight='600'>
      <template slot="body">
        <main
          v-loading="loading"
          element-loading-text="文件生成中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(255, 255,255, 0.8)">
            <div class="firstDiv" style="padding-top: 10px;">导出字段选择</div>
            <div class="checkBox">
                <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
                <el-checkbox-group v-model="checkList" @change="handleCheckedsChange">
                    <el-checkbox v-for="item in cData" :label="item.label" :key="item.label"></el-checkbox>
                </el-checkbox-group>
            </div>
        </main>
      </template>
      <template slot="footer">
        <!-- 取消 -->
        <app-button @click="handleClose" :buttonType='2'></app-button>
        <!-- 确认 -->
        <app-button @click="createData" text="导出"></app-button>
      </template>
    </app-dialog>
  </div>
</template>

<script>
import * as exportPort from "@/api/export"
import { downloadFile } from "@/utils/index";
export default {
  name: "mainten-order-mgmt-assign",
  components: {

  },
  mixins: [],
//   导出页面枚举
// 我的客户_客户管理 = 1,
// 商机_项目管理 = 2,
// 投标管理 = 3,
// 订单管理 = 4,
// 合同管理 = 5,
// 实施数据分析_实施数据报表 = 6,
// 实施工程管理 = 7,
// 需求池管理 = 8,
// 项目工作台_需求 = 9,
// 项目工作台_任务 = 10,
// 项目工作台_问题 = 11,
// 客服中心_客诉管理 = 12,
// 客服中心_售后服务 = 13,
// 维修中心_报修单管理 = 14,
// 维修中新_巡检任务 = 15,
// 业务地图 = 16,
// 意见反馈 = 17
// 拜访计划管理 = 23,
  props: {
    rData:{
        type:Object,
        default:null
    },
    cData:{
        type:Array,
        default:function(){
            return []
        }
    }
  },
  watch: {
    "$attrs.dialogFormVisible"(val) {
        if(val){
            this.allLabel=[];
            this.checkList=[];
            this.isIndeterminate=false;
            this.checkAll=true;
            this.cData.forEach(v => {
                this.allLabel.push(v.label);
            })
            this.checkList=this.allLabel;
        }
    }
  },
  created() {
    
  },
  data() {
    return {
        loading:false,
        checkList: [],
        isIndeterminate:false,
        checkAll:true,
        allLabel:[],
    };
  },
  methods: {
    handleCheckAllChange(val) {
        this.checkList = val ? this.allLabel : [];
        this.isIndeterminate = false;
    },
    handleCheckedsChange(value) {
        let checkedCount = value.length;
        this.checkAll = checkedCount === this.cData.length;
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.cData.length;
    },
    createData() {
        if(this.checkList.length<1){
            this.$message({
                message: '请选择至少一条导出数据!',
                type: 'warning'
            });
        }else{
            this.loading=true;
            let a=null;

            this.cData.forEach(v => {
                a=this.checkList.find(s => s == v.label);
                if(a){
                    this.rData.columns.push(v.value);
                }
            })

            //特殊情况，写死
            let columnsTemp = JSON.parse(JSON.stringify(this.rData.columns))
            let idx = columnsTemp.indexOf('__details')
            if(idx > -1) {
              let exList = ['AllString'].concat(Array.from(Array(31), (v, k) => {
                return `Day${k + 1}`
              }))
              columnsTemp.splice(idx, 1, ...exList)
            }

            let postDatas = JSON.parse(JSON.stringify(this.rData))
            postDatas.columns = columnsTemp

            exportPort.exportData(postDatas, {
              timeout: 300 * 1000,
            }).then(res => {
                this.loading=false;
                this.handleClose();
                downloadFile(res.Url);
            }).catch(err => {
                this.loading=false;
            })
        }
    },
    handleClose() {
      this.$refs.appDialogRef.handleClose();
    },
  }
};
</script>
<style scoped>
    .checkBox >>> .el-checkbox__label{
        font-weight:normal!important;
    }
</style>
<style lang="scss" scoped>
    main{
        padding-left:10px;
        padding-bottom: 10px;
        >div:first-child{
            font-weight: bold;
            margin-bottom: 10px;
        }
    }
</style>