<template>
    <div>
        <el-tag :title="text" size="mini" v-bind="$attrs" :closable='false'>
            <i @click="handleClose" v-show="closable" class="el-tag__close el-icon-close"></i>
            &nbsp;&nbsp;{{ text }}
        </el-tag>
    </div>
</template>

<script>
export default {
    name: 'app-tag',
    props: {
        text: {
            type: String,
            default: ''
        },
        closable: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {

        }
    },
    methods: {
        handleClose() {
            this.$emit('close')
        }
    }
}
</script>