<template>
  <el-form :inline="true" v-bind="$attrs" class="search-form">
    <template v-if="layoutMode == 'classic'">
      <app-table-form-item
        class="__btns__wrapper__commTop item-height"
        v-for="(item, idx) in items"
        :key="idx"
        :sm="smWidth"
        :label="item.label"
        v-show="idx < 5 || showMoreConditions"
      >
        <slot :name="item.prop"></slot>
      </app-table-form-item>
      <app-table-form-item
        class="__btns__wrapper __btns__wrapper__commTop item-height"
        :sm="sm"
        :class="[conditionTotal % 3 === 0 ? 'showMoreCondition' : 'hideMoreCondition']"
      >
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button type="default" @click="handleReset">重置</el-button>
        <slot name="other-btns"></slot>
        <el-button type="text" @click="handleShowMoreCondition" v-if="conditionTotal > 5">
          <span v-if="showMoreConditions" @click="handleCollapse">
            收起
            <i class="el-icon-arrow-up el-icon--right"></i>
          </span>
          <span v-else @click="handleExpand">
            展开
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
        </el-button>
      </app-table-form-item>
    </template>
    <template v-else>
      <el-row>
        <div class="search-wrapper">
          <div class="search-input-wrapper">
            <template v-if="mainCondition.length || (otherCondition && otherCondition.length > 0)">
              <div class="ipt-wrapper">
                <div
                  v-for="(mainSearch, index) in mainCondition"
                  :key="index"
                  class="main_condition_item"
                >
                  <!-- showMainLabel控制是否显示label -->
                  <label v-if="mainSearch.showMainLabel" class="main_condition_label">
                    {{ mainSearch.label }}
                  </label>
                  <slot :name="mainSearch.prop"></slot>
                </div>
                <el-button
                  id="__btnShowMore"
                  class="__btnShowMore"
                  v-if="otherCondition && otherCondition.length > 0"
                  type="primary"
                  @click.stop="handleShowMoreCondition"
                >
                  更多筛选
                  <i
                    :class="
                      showMoreConditions
                        ? 'el-icon-arrow-up el-icon--right'
                        : 'el-icon-arrow-down el-icon--right'
                    "
                  ></i>
                </el-button>
                <div class="search-btns" v-if="showSimpleOperate">
                  <el-button type="primary" @click="handleSearch">查询</el-button>
                  <el-button type="default" @click="handleReset">重置</el-button>
                </div>
              </div>
            </template>
            <slot name="btnsArea"></slot>
            <slot name="otherArea"></slot>
          </div>
        </div>
      </el-row>
      <!-- <el-row> -->
      <div class="search-wrapper2">
        <div
          class="condition-area-wrapper"
          v-show="showMoreConditions"
          v-click-outside="clickOutSide"
        >
          <div class="search-btns">
            <app-table-row-button :type="3" text="重置" @click="handleReset"></app-table-row-button>
            <app-table-row-button
              :type="1"
              text="查询"
              @click="handleSearch"
            ></app-table-row-button>
          </div>
          <div v-for="(item, idx) in otherCondition" :key="idx">
            <el-row style="margin-bottom: 10px;">
              <app-table-form-item
                :sm="smWidth"
                :label="item.label"
                class="item-height"
                :layoutMode="layoutMode"
              >
                <slot :name="item.prop"></slot>
              </app-table-form-item>
            </el-row>
          </div>
        </div>
      </div>
      <!-- </el-row> -->
    </template>
  </el-form>
</template>

<script>
import AppTableFormItem from "../AppTableFormItem";
export default {
  name: "app-table-form",
  components: {
    AppTableFormItem,
  },
  computed: {
    //主要的搜索条件
    mainCondition() {
      return this.items.filter(s => s.mainCondition === true);
    },
    otherCondition() {
      return this.items.filter(s => s.mainCondition !== true && !s.isFilterColumn);
    },

    // formItems() {
    //     return this.items.map((item, idx) => {
    //         if(idx < 2){
    //             this.$set(item, 'isShow', true)
    //         }else{
    //             this.$set(item, 'isShow', false)
    //         }
    //         return item;
    //     })
    // },
    //列显示宽度
    sm() {
      if (!this.showMoreConditions && this.conditionTotal > 5) {
        return this.smWidth;
      }

      return this.conditionTotal % Math.floor(24 / this.smWidth) === 0 ? 24 : this.smWidth;
    },
    conditionTotal() {
      return this.items.length;
    },
  },
  props: {
    items: Array, //item: label, prop
    // bindModel: Object
    smWidth: {
      type: Number,
      default: 8,
    },
    /**
     * 查询条件区域布局方式：
     * classic：经典模式，最老的布局方式
     * simple：简易模式，其他查询条件下拉展示，后面跟着表格批量操作按钮区块；通过showSimpleOperate控制显示操作重置按钮
     */
    layoutMode: {
      type: String,
      default: "classic",
    },
    showSimpleOperate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showMoreConditions: false,
    };
  },
  directives: {
    clickOutside: {
      bind: function(el, binding, vnode) {
        // el.clickOutsideEvent = function (event) {
        //     // here I check that click was outside the el and his childrens
        //     if (!(el == event.target || el.contains(event.target))) {
        //     // and if it did, call method provided in attribute value
        //         if(event.target.id != '__btnShowMore') {
        //             vnode.context[binding.expression](event)
        //         }
        //     }
        // }
        // document.body.addEventListener('click', el.clickOutsideEvent)
      },
      unbind: function(el) {
        // document.body.removeEventListener('click', el.clickOutsideEvent)
      },
    },
  },
  methods: {
    handleCollapse() {
      this.$emit("onCollapse");
    },
    handleExpand() {
      this.$emit("onExpand");
    },
    clickOutSide() {
      // this.showMoreConditions = false
    },
    handleShowMoreCondition() {
      this.showMoreConditions = !this.showMoreConditions;
      this.$emit("collapseOrExpand", this.showMoreConditions);
    },
    handleSearch() {
      if (this.layoutMode == "simple") {
        this.showMoreConditions = false;
      }
      this.$emit("onSearch");
    },
    handleReset() {
      if (this.layoutMode == "simple") {
        this.showMoreConditions = false;
      }
      this.$emit("onReset");
    },
  },
};
</script>

<style scoped>
/* .search-form {
    overflow: hidden;
} */

.item-height{
  /* height: 29px; */
  height: 33px;
}
.search-form >>> .el-form-item {
  margin-bottom: 0;
}

/* 隐藏更多搜索条件 */
/*独占一行，按钮右对齐*/
.showMoreCondition {
  text-align: right;
  padding-right: 10px;
}

/* 隐藏或只有一行时，按钮左边距小一些 */
.hideMoreCondition {
  text-align: left;
  padding-left: 20px;
}

/*修正*/
.__btns__wrapper >>> .el-form-item__content {
  width: 100%;
}

.__btns__wrapper {
}

.__btns__wrapper__commTop {
  margin-top: 10px;
}

.ipt-wrapper >>> .el-icon--right {
  height: 10px;
  width: 10px;
}
</style>

<style lang="scss" scoped>
.search-wrapper {
  .search-input-wrapper {
    display: flex;
    .ipt-wrapper {
      display: flex;
      // max-width: 400px;
      // margin-right: 4px;
      .main_condition_item {
        display: flex;
        align-items: center;
        width: 300px;
        margin-right: 10px;
        .main_condition_label {
          white-space: nowrap;
          padding-right: 5px;
        }
      }
    }
  }
}

.search-wrapper2 {
  position: relative;
  height: 0;
  .condition-area-wrapper {
    box-sizing: border-box;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    max-height: 600px;
    overflow-y: auto;
    background: #fff;
    z-index: 999;
    // border: 1px solid #dcdfe6;
    box-shadow: 1px 1px 3px #aaaaaa;
    transition: height 0.3s ease-in-out;
    padding-bottom: 10px;
    .search-btns {
      padding: 10px;
    }
  }
}
</style>
