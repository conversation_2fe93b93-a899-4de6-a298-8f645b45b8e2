/* Background */ .highlight-bg { color: #cccccc; background-color: #000000 }
/* PreWrapper */ .highlight-chroma { color: #cccccc; background-color: #000000; }
/* Error */ .highlight-chroma .highlight-err {  }
/* LineTableTD */ .highlight-chroma .highlight-lntd { vertical-align: top; padding: 0; margin: 0; border: 0; }
/* LineTable */ .highlight-chroma .highlight-lntable { border-spacing: 0; padding: 0; margin: 0; border: 0; }
/* LineHighlight */ .highlight-chroma .highlight-hl { background-color: #191919 }
/* LineNumbersTable */ .highlight-chroma .highlight-lnt { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #666666 }
/* LineNumbers */ .highlight-chroma .highlight-ln { white-space: pre; user-select: none; margin-right: 0.4em; padding: 0 0.4em 0 0.4em;color: #666666 }
/* Line */ .highlight-chroma .highlight-line { display: flex; }
/* Keyword */ .highlight-chroma .highlight-k { color: #cdcd00 }
/* KeywordConstant */ .highlight-chroma .highlight-kc { color: #cdcd00 }
/* KeywordDeclaration */ .highlight-chroma .highlight-kd { color: #00cd00 }
/* KeywordNamespace */ .highlight-chroma .highlight-kn { color: #cd00cd }
/* KeywordPseudo */ .highlight-chroma .highlight-kp { color: #cdcd00 }
/* KeywordReserved */ .highlight-chroma .highlight-kr { color: #cdcd00 }
/* KeywordType */ .highlight-chroma .highlight-kt { color: #00cd00 }
/* NameBuiltin */ .highlight-chroma .highlight-nb { color: #cd00cd }
/* NameClass */ .highlight-chroma .highlight-nc { color: #00cdcd }
/* NameException */ .highlight-chroma .highlight-ne { color: #666699; font-weight: bold }
/* NameVariable */ .highlight-chroma .highlight-nv { color: #00cdcd }
/* LiteralString */ .highlight-chroma .highlight-s { color: #cd0000 }
/* LiteralStringAffix */ .highlight-chroma .highlight-sa { color: #cd0000 }
/* LiteralStringBacktick */ .highlight-chroma .highlight-sb { color: #cd0000 }
/* LiteralStringChar */ .highlight-chroma .highlight-sc { color: #cd0000 }
/* LiteralStringDelimiter */ .highlight-chroma .highlight-dl { color: #cd0000 }
/* LiteralStringDoc */ .highlight-chroma .highlight-sd { color: #cd0000 }
/* LiteralStringDouble */ .highlight-chroma .highlight-s2 { color: #cd0000 }
/* LiteralStringEscape */ .highlight-chroma .highlight-se { color: #cd0000 }
/* LiteralStringHeredoc */ .highlight-chroma .highlight-sh { color: #cd0000 }
/* LiteralStringInterpol */ .highlight-chroma .highlight-si { color: #cd0000 }
/* LiteralStringOther */ .highlight-chroma .highlight-sx { color: #cd0000 }
/* LiteralStringRegex */ .highlight-chroma .highlight-sr { color: #cd0000 }
/* LiteralStringSingle */ .highlight-chroma .highlight-s1 { color: #cd0000 }
/* LiteralStringSymbol */ .highlight-chroma .highlight-ss { color: #cd0000 }
/* LiteralNumber */ .highlight-chroma .highlight-m { color: #cd00cd }
/* LiteralNumberBin */ .highlight-chroma .highlight-mb { color: #cd00cd }
/* LiteralNumberFloat */ .highlight-chroma .highlight-mf { color: #cd00cd }
/* LiteralNumberHex */ .highlight-chroma .highlight-mh { color: #cd00cd }
/* LiteralNumberInteger */ .highlight-chroma .highlight-mi { color: #cd00cd }
/* LiteralNumberIntegerLong */ .highlight-chroma .highlight-il { color: #cd00cd }
/* LiteralNumberOct */ .highlight-chroma .highlight-mo { color: #cd00cd }
/* Operator */ .highlight-chroma .highlight-o { color: #3399cc }
/* OperatorWord */ .highlight-chroma .highlight-ow { color: #cdcd00 }
/* Comment */ .highlight-chroma .highlight-c { color: #000080 }
/* CommentHashbang */ .highlight-chroma .highlight-ch { color: #000080 }
/* CommentMultiline */ .highlight-chroma .highlight-cm { color: #000080 }
/* CommentSingle */ .highlight-chroma .highlight-c1 { color: #000080 }
/* CommentSpecial */ .highlight-chroma .highlight-cs { color: #cd0000; font-weight: bold }
/* CommentPreproc */ .highlight-chroma .highlight-cp { color: #000080 }
/* CommentPreprocFile */ .highlight-chroma .highlight-cpf { color: #000080 }
/* GenericDeleted */ .highlight-chroma .highlight-gd { color: #cd0000 }
/* GenericEmph */ .highlight-chroma .highlight-ge { font-style: italic }
/* GenericError */ .highlight-chroma .highlight-gr { color: #ff0000 }
/* GenericHeading */ .highlight-chroma .highlight-gh { color: #000080; font-weight: bold }
/* GenericInserted */ .highlight-chroma .highlight-gi { color: #00cd00 }
/* GenericOutput */ .highlight-chroma .highlight-go { color: #888888 }
/* GenericPrompt */ .highlight-chroma .highlight-gp { color: #000080; font-weight: bold }
/* GenericStrong */ .highlight-chroma .highlight-gs { font-weight: bold }
/* GenericSubheading */ .highlight-chroma .highlight-gu { color: #800080; font-weight: bold }
/* GenericTraceback */ .highlight-chroma .highlight-gt { color: #0044dd }
/* GenericUnderline */ .highlight-chroma .highlight-gl { text-decoration: underline }
/*

Qt Creator dark color scheme

*/

.hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    background: #000000;
}

.hljs,
.hljs-subst,
.hljs-tag,
.hljs-title {
    color: #aaaaaa;
}

.hljs-strong,
.hljs-emphasis {
    color: #a8a8a2;
}

.hljs-bullet,
.hljs-quote,
.hljs-number,
.hljs-regexp,
.hljs-literal {
    color: #ff55ff;
}

.hljs-code
.hljs-selector-class {
    color: #aaaaff;
}

.hljs-emphasis,
.hljs-stronge,
.hljs-type {
    font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-function,
.hljs-section,
.hljs-symbol,
.hljs-name {
    color: #ffff55;
}

.hljs-attribute {
    color: #ff5555;
}

.hljs-variable,
.hljs-params,
.hljs-class .hljs-title {
    color: #8888ff;
}

.hljs-string,
.hljs-selector-id,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-type,
.hljs-built_in,
.hljs-builtin-name,
.hljs-template-tag,
.hljs-template-variable,
.hljs-addition,
.hljs-link {
    color: #ff55ff;
}

.hljs-comment,
.hljs-meta,
.hljs-deletion {
    color: #55ffff;
}

