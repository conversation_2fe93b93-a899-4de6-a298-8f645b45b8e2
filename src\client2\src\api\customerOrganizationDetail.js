import request from '@/utils/request'
import { serviceArea } from './serviceArea'

let serviceAreaName = serviceArea.business

//新增
export function addCustomerOrganizationDetail(data) {
    return request({
        url: serviceAreaName + '/CustomerOrganizationDetail/Add',
        method: 'post',
        data
    })
}

//编辑
export function editCustomerOrganizationDetail(data) {
    return request({
        url: serviceAreaName + '/CustomerOrganizationDetail/Edit',
        method: 'post',
        data
    })
}

//删除
export function deleteCustomerOrganizationDetail(data) {
    return request({
        url: serviceAreaName + '/CustomerOrganizationDetail/Delete',
        method: 'post',
        data
    })
}

//分页列表
export function getCustomerOrganizationDetailListPage(data) {
    return request({
        url: serviceAreaName + '/CustomerOrganizationDetail/GetListPage',
        method: 'post',
        data
    })
}

//完整列表
export function getCustomerOrganizationDetailList(params) {
    return request({
        url: serviceAreaName + '/CustomerOrganizationDetail/GetList',
        method: 'get',
        params
    })
}

//导出
export function exportCustomerOrganizationDetail(data) {
    return request({
        url: serviceAreaName + '/CustomerOrganizationDetail/Export',
        method: 'post',
        data
    })
}

//详情
export function getCustomerOrganizationDetail(params) {
    return request({
        url: serviceAreaName + '/CustomerOrganizationDetail/GetCustomerOrganizationDetail',
        method: 'get',
        params
    })
}